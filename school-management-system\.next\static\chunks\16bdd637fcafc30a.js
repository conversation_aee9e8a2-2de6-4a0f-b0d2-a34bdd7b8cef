(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,53309,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>n,"CardContent",()=>c,"CardDescription",()=>l,"CardHeader",()=>s,"CardTitle",()=>i]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let n=r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",n),...s})});n.displayName="Card";let s=r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",n),...s})});s.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",n),...s})});i.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",n),...s})});l.displayName="CardDescription";let c=r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",n),...s})});c.displayName="CardContent",r.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",n),...s})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>f],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,n=t.map(t=>{let n=a(t,e);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let e=0;e<n.length;e++){let r=n[e];"function"==typeof r?r():a(t[e],null)}}}}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(n(...t),t)}function i(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...s}=e;if(r.isValidElement(a)){var i,l,c;let e,d,o=(d=(e=null==(l=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(d=(e=null==(c=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:c.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=s(...t);return n(...t),a}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,a.props);return a.type!==r.Fragment&&(u.ref=t?n(t,o):o),r.cloneElement(a,u)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),s=r.forwardRef((e,n)=>{let{children:s,...i}=e,l=r.Children.toArray(s),c=l.find(d);if(c){let e=c.props.children,s=l.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,t.jsx)(a,{...i,ref:n,children:s})});return s.displayName="".concat(e,".Slot"),s}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>n,"useComposedRefs",()=>s],35952);var l=i("Slot"),c=Symbol("radix.slottable");function d(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}var o=e.i(94323),u=e.i(36946);let x=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),f=r.forwardRef((e,r)=>{let{className:a,variant:n,size:s,asChild:i=!1,...c}=e;return(0,t.jsx)(i?l:"button",{className:(0,u.cn)(x({variant:n,size:s,className:a})),ref:r,...c})});f.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>s,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),a=e.i(88338),n=e.i(53379),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let s=(0,a.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?s:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,t.forwardRef)((e,r)=>{let{color:s="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:d="",children:o,iconNode:u,...x}=e;return(0,t.createElement)("svg",{ref:r,...n,width:i,height:i,stroke:s,strokeWidth:c?24*Number(l)/Number(i):l,className:a("lucide",d),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(o)?o:[o]])}),i=(e,n)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:c,...d}=i;return(0,t.createElement)(s,{ref:l,iconNode:n,className:a("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...d})});return i.displayName=r(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>n,"studentNavigation",()=>a,"teacherNavigation",()=>r]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],r=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],a=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function n(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},46945,(e,t,r)=>{"use strict";function a(e){let t={};for(let[r,a]of e.entries()){let e=t[r];void 0===e?t[r]=a:Array.isArray(e)?e.push(a):t[r]=[e,a]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,a]of t.entries())e.append(r,a)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return i},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return s}})},56229,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return s},formatWithValidation:function(){return l},urlObjectKeys:function(){return i}});let a=e.r(95039)._(e.r(46945)),n=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",i=e.pathname||"",l=e.hash||"",c=e.query||"",d=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?d=t+e.host:r&&(d=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(d+=":"+e.port)),c&&"object"==typeof c&&(c=String(a.urlQueryToSearchParams(c)));let o=e.search||c&&"?"+c||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||n.test(s))&&!1!==d?(d="//"+(d||""),i&&"/"!==i[0]&&(i="/"+i)):d||(d=""),l&&"#"!==l[0]&&(l="#"+l),o&&"?"!==o[0]&&(o="?"+o),""+s+d+(i=i.replace(/[?#]/g,encodeURIComponent))+(o=o.replace("#","%23"))+l}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return s(e)}},35251,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=e.r(46686);function n(e,t){let r=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=s(e,a)),t&&(n.current=s(t,a))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},79713,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return j},MissingStaticPage:function(){return y},NormalizeError:function(){return p},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return m},WEB_VITALS:function(){return a},execOnce:function(){return n},getDisplayName:function(){return d},getLocationOrigin:function(){return l},getURL:function(){return c},isAbsoluteUrl:function(){return i},isResSent:function(){return o},loadGetInitialProps:function(){return x},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var a=arguments.length,n=Array(a),s=0;s<a;s++)n[s]=arguments[s];return r||(r=!0,t=e(...n)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>s.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function c(){let{href:e}=window.location,t=l();return e.substring(t.length)}function d(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function o(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function x(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await x(t.Component,t.ctx)}:{};let a=await e.getInitialProps(t);if(r&&o(r))return a;if(!a)throw Object.defineProperty(Error('"'+d(e)+'.getInitialProps()" should resolve to an object. But found "'+a+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a}let f="undefined"!=typeof performance,m=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class p extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class j extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},46716,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return s}});let a=e.r(79713),n=e.r(36984);function s(e){if(!(0,a.isAbsoluteUrl)(e))return!0;try{let t=(0,a.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},9260,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},90842,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return p},useLinkStatus:function(){return y}});let a=e.r(95039),n=e.r(53379),s=a._(e.r(46686)),i=e.r(56229),l=e.r(33762),c=e.r(35251),d=e.r(79713),o=e.r(98497);e.r(53309);let u=e.r(1883),x=e.r(46716),f=e.r(74179);e.r(9260);let m=e.r(37802);function h(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function p(e){var t;let r,a,i,[p,y]=(0,s.useOptimistic)(u.IDLE_LINK_STATUS),j=(0,s.useRef)(null),{href:b,as:v,children:N,prefetch:k=null,passHref:w,replace:C,shallow:M,scroll:S,onClick:P,onMouseEnter:A,onTouchStart:E,legacyBehavior:R=!1,onNavigate:T,ref:O,unstable_dynamicOnHover:_,...L}=e;r=N,R&&("string"==typeof r||"number"==typeof r)&&(r=(0,n.jsx)("a",{children:r}));let U=s.default.useContext(l.AppRouterContext),B=!1!==k,D=!1!==k?null===(t=k)||"auto"===t?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:I,as:F}=s.default.useMemo(()=>{let e=h(b);return{href:e,as:v?h(v):e}},[b,v]);R&&(a=s.default.Children.only(r));let H=R?a&&"object"==typeof a&&a.ref:O,z=s.default.useCallback(e=>(null!==U&&(j.current=(0,u.mountLinkInstance)(e,I,U,D,B,y)),()=>{j.current&&((0,u.unmountLinkForCurrentNavigation)(j.current),j.current=null),(0,u.unmountPrefetchableInstance)(e)}),[B,I,U,D,y]),V={ref:(0,c.useMergedRef)(z,H),onClick(e){R||"function"!=typeof P||P(e),R&&a.props&&"function"==typeof a.props.onClick&&a.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,a,n,i,l){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,x.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}s.default.startTransition(()=>{(0,f.dispatchNavigateAction)(r||t,n?"replace":"push",null==i||i,a.current)})}}(e,I,F,j,C,S,T))},onMouseEnter(e){R||"function"!=typeof A||A(e),R&&a.props&&"function"==typeof a.props.onMouseEnter&&a.props.onMouseEnter(e),U&&B&&(0,u.onNavigationIntent)(e.currentTarget,!0===_)},onTouchStart:function(e){R||"function"!=typeof E||E(e),R&&a.props&&"function"==typeof a.props.onTouchStart&&a.props.onTouchStart(e),U&&B&&(0,u.onNavigationIntent)(e.currentTarget,!0===_)}};return(0,d.isAbsoluteUrl)(F)?V.href=F:R&&!w&&("a"!==a.type||"href"in a.props)||(V.href=(0,o.addBasePath)(F)),i=R?s.default.cloneElement(a,V):(0,n.jsx)("a",{...L,...V,children:r}),(0,n.jsx)(g.Provider,{value:p,children:i})}let g=(0,s.createContext)(u.IDLE_LINK_STATUS),y=()=>(0,s.useContext)(g);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},4534,e=>{"use strict";e.s(["CheckCircle",()=>t],4534);let t=(0,e.i(4741).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},33163,e=>{"use strict";e.s(["AlertCircle",()=>t],33163);let t=(0,e.i(4741).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86080,e=>{"use strict";e.s(["ArrowLeft",()=>t],86080);let t=(0,e.i(4741).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55392,e=>{"use strict";e.s(["default",()=>j],55392);var t=e.i(53379),r=e.i(46686),a=e.i(18125),n=e.i(69758),s=e.i(32668),i=e.i(30151),l=e.i(89559),c=e.i(84633),d=e.i(87523),o=e.i(4534),u=e.i(33163),x=e.i(7418);function f(e){let{exam:r,students:a,onEdit:n,showActions:l=!0}=e,c=e=>e>=90?"bg-green-100 text-green-800":e>=80?"bg-blue-100 text-blue-800":e>=70?"bg-yellow-100 text-yellow-800":e>=60?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",f=e=>e>=90?"A+":e>=80?"A":e>=70?"B+":e>=60?"B":e>=50?"C+":e>=40?"C":e>=30?"D":"F",m=a.filter(e=>e.hasMarks),h=a.filter(e=>!e.hasMarks),p={totalStudents:a.length,gradedStudents:m.length,pendingStudents:h.length,averageMarks:m.length>0?Math.round(m.reduce((e,t)=>{var r;return e+((null==(r=t.currentMark)?void 0:r.obtainedMarks)||0)},0)/m.length*100)/100:0,averagePercentage:m.length>0?Math.round(m.reduce((e,t)=>{var a;return e+((null==(a=t.currentMark)?void 0:a.obtainedMarks)||0)/r.maxMarks*100},0)/m.length*100)/100:0};return(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.Users,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Student Marks"})]}),l&&n&&(0,t.jsxs)(i.Button,{variant:"outline",onClick:()=>n(r.id),children:[(0,t.jsx)(x.Edit,{className:"w-4 h-4 mr-2"}),"Edit Marks"]})]}),(0,t.jsxs)(s.CardDescription,{children:["Marks for ",r.name," - ",r.subject.name," (",r.subject.class.name,")"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"text-center p-2 bg-gray-50 rounded",children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:p.totalStudents}),(0,t.jsx)("div",{className:"text-gray-600",children:"Total"})]}),(0,t.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,t.jsx)("div",{className:"font-semibold text-green-800",children:p.gradedStudents}),(0,t.jsx)("div",{className:"text-green-600",children:"Graded"})]}),(0,t.jsxs)("div",{className:"text-center p-2 bg-orange-50 rounded",children:[(0,t.jsx)("div",{className:"font-semibold text-orange-800",children:p.pendingStudents}),(0,t.jsx)("div",{className:"text-orange-600",children:"Pending"})]}),(0,t.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,t.jsx)("div",{className:"font-semibold text-blue-800",children:p.averageMarks}),(0,t.jsx)("div",{className:"text-blue-600",children:"Avg Marks"})]}),(0,t.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,t.jsxs)("div",{className:"font-semibold text-blue-800",children:[p.averagePercentage,"%"]}),(0,t.jsx)("div",{className:"text-blue-600",children:"Avg %"})]})]})]}),(0,t.jsx)(s.CardContent,{children:0===a.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(d.Users,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"No students found for this exam"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roll No."}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Section"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Remarks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(e=>{var a;let n=e.currentMark?Math.round(e.currentMark.obtainedMarks/r.maxMarks*1e4)/100:0,s=e.currentMark?f(n):"-";return(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.admissionNo})]})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.rollNumber||"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.sectionName||"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.currentMark?"".concat(e.currentMark.obtainedMarks,"/").concat(r.maxMarks):"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.currentMark?"".concat(n,"%"):"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.currentMark?(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(c(n)),children:s}):(0,t.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==(a=e.currentMark)?void 0:a.remarks)||"-"}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.hasMarks?(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,t.jsx)(o.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:[(0,t.jsx)(u.AlertCircle,{className:"w-3 h-3 mr-1"}),"Pending"]})})]},e.id)})})]})}),(0,t.jsx)("div",{className:"lg:hidden space-y-4",children:a.map(e=>{let a=e.currentMark?Math.round(e.currentMark.obtainedMarks/r.maxMarks*1e4)/100:0,n=e.currentMark?f(a):"-";return(0,t.jsx)(s.Card,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e.admissionNo," • Roll: ",e.rollNumber||"-"," • Section: ",e.sectionName||"-"]})]}),(0,t.jsx)("div",{className:"ml-4",children:e.hasMarks?(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,t.jsx)(o.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:[(0,t.jsx)(u.AlertCircle,{className:"w-3 h-3 mr-1"}),"Pending"]})})]}),e.currentMark?(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Marks:"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[e.currentMark.obtainedMarks,"/",r.maxMarks]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Percentage:"}),(0,t.jsxs)("p",{className:"text-gray-600 font-semibold",children:[a,"%"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Grade:"}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(c(a)),children:n})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Remarks:"}),(0,t.jsx)("p",{className:"text-gray-600",children:e.currentMark.remarks||"-"})]})]}):(0,t.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,t.jsx)(u.AlertCircle,{className:"h-8 w-8 mx-auto mb-2"}),(0,t.jsx)("p",{children:"No marks entered yet"})]})]})},e.id)})})]})})]})}var m=e.i(10535),h=e.i(69556),p=e.i(86080),g=e.i(23178),y=e.i(90842);function j(){let e=(0,a.useParams)(),o=(0,a.useRouter)(),{data:j}=(0,n.useSession)(),b=e.examId,[v,N]=(0,r.useState)(null),[k,w]=(0,r.useState)(!0);(0,r.useEffect)(()=>{C()},[b]);let C=async()=>{try{w(!0);let e=await fetch("/api/teacher/exams/".concat(b,"/students"));if(e.ok){let t=await e.json();N(t)}else console.error("Failed to fetch exam data")}catch(e){console.error("Error fetching exam data:",e)}finally{w(!1)}};if(k)return(0,t.jsx)(l.default,{title:"View Marks",navigation:c.teacherNavigation,children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading exam data..."})]})});if(!v)return(0,t.jsx)(l.default,{title:"View Marks",navigation:c.teacherNavigation,children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(u.AlertCircle,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Exam not found"}),(0,t.jsx)(y.default,{href:"/teacher/marks",children:(0,t.jsxs)(i.Button,{className:"mt-4",children:[(0,t.jsx)(p.ArrowLeft,{className:"w-4 h-4 mr-2"}),"Back to Marks"]})})]})});let M=v.students.filter(e=>e.hasMarks),S=v.students.filter(e=>!e.hasMarks),P={totalStudents:v.students.length,gradedStudents:M.length,pendingStudents:S.length,averageMarks:M.length>0?Math.round(M.reduce((e,t)=>{var r;return e+((null==(r=t.currentMark)?void 0:r.obtainedMarks)||0)},0)/M.length*100)/100:0,averagePercentage:M.length>0?Math.round(M.reduce((e,t)=>{var r;return e+((null==(r=t.currentMark)?void 0:r.obtainedMarks)||0)/v.exam.maxMarks*100},0)/M.length*100)/100:0};return(0,t.jsx)(l.default,{title:"View Marks",navigation:c.teacherNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,t.jsx)(y.default,{href:"/teacher/marks",children:(0,t.jsxs)(i.Button,{variant:"outline",size:"sm",children:[(0,t.jsx)(p.ArrowLeft,{className:"w-4 h-4 mr-1"}),"Back"]})})}),(0,t.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"View Marks"}),(0,t.jsxs)("p",{className:"text-sm sm:text-base text-gray-600",children:[v.exam.name," - ",v.exam.subject.name," (",v.exam.subject.class.name,")"]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(y.default,{href:"/teacher/marks/".concat(b),children:(0,t.jsxs)(i.Button,{variant:"outline",children:[(0,t.jsx)(x.Edit,{className:"w-4 h-4 mr-2"}),"Edit Marks"]})}),(0,t.jsxs)(i.Button,{variant:"outline",children:[(0,t.jsx)(g.Download,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Total Students"}),(0,t.jsx)(d.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:P.totalStudents})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Graded"}),(0,t.jsx)(CheckCircle,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:P.gradedStudents})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Pending"}),(0,t.jsx)(u.AlertCircle,{className:"h-4 w-4 text-orange-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:P.pendingStudents})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Average Marks"}),(0,t.jsx)(m.Award,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:P.averageMarks})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Average %"}),(0,t.jsx)(m.Award,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[P.averagePercentage,"%"]})})]})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{children:(0,t.jsxs)(s.CardTitle,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.BookOpen,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Exam Details"})]})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Subject:"}),(0,t.jsx)("p",{className:"text-gray-600",children:v.exam.subject.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Class:"}),(0,t.jsx)("p",{className:"text-gray-600",children:v.exam.subject.class.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Max Marks:"}),(0,t.jsx)("p",{className:"text-gray-600",children:v.exam.maxMarks})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700",children:"Date:"}),(0,t.jsx)("p",{className:"text-gray-600",children:new Date(v.exam.date).toLocaleDateString()})]})]})})]}),(0,t.jsx)(f,{exam:v.exam,students:v.students,onEdit:e=>{o.push("/teacher/marks/".concat(e))},showActions:!0})]})})}}]);