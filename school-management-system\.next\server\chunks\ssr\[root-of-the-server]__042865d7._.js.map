{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wTAAmB;QAClB,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wTAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;;;;;gCAYhC;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;;;;;4BASjC;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { \n  Calendar, \n  FileText, \n  Download, \n  BarChart3, \n  Users,\n  Award,\n  TrendingUp,\n  Printer,\n  Eye\n} from 'lucide-react'\n\ninterface ReportCard {\n  id: string\n  studentName: string\n  admissionNo: string\n  className: string\n  sectionName: string\n  termName: string\n  academicYear: string\n  totalMarks: number\n  obtainedMarks: number\n  percentage: number\n  grade: string\n  rank: number\n  generatedAt: string\n  status: 'GENERATED' | 'PENDING' | 'PUBLISHED'\n}\n\nimport { adminNavigation } from '@/lib/navigation';\n\nexport default function ReportsPage() {\n  const [reportCards, setReportCards] = useState<ReportCard[]>([])\n  const [selectedTerm, setSelectedTerm] = useState('all')\n  const [selectedClass, setSelectedClass] = useState('all')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n\n  // Mock data - in real app, this would come from API\n  useEffect(() => {\n    setReportCards([\n      {\n        id: '1',\n        studentName: 'John Doe',\n        admissionNo: 'STU001',\n        className: 'Grade 8',\n        sectionName: 'A',\n        termName: 'Term 1',\n        academicYear: '2024-2025',\n        totalMarks: 500,\n        obtainedMarks: 425,\n        percentage: 85,\n        grade: 'A',\n        rank: 3,\n        generatedAt: '2024-12-15',\n        status: 'GENERATED'\n      },\n      {\n        id: '2',\n        studentName: 'Jane Smith',\n        admissionNo: 'STU002',\n        className: 'Grade 8',\n        sectionName: 'A',\n        termName: 'Term 1',\n        academicYear: '2024-2025',\n        totalMarks: 500,\n        obtainedMarks: 380,\n        percentage: 76,\n        grade: 'B+',\n        rank: 8,\n        generatedAt: '2024-12-15',\n        status: 'PUBLISHED'\n      },\n      {\n        id: '3',\n        studentName: 'Mike Johnson',\n        admissionNo: 'STU003',\n        className: 'Grade 8',\n        sectionName: 'A',\n        termName: 'Term 1',\n        academicYear: '2024-2025',\n        totalMarks: 500,\n        obtainedMarks: 450,\n        percentage: 90,\n        grade: 'A+',\n        rank: 1,\n        generatedAt: '2024-12-15',\n        status: 'GENERATED'\n      }\n    ])\n  }, [])\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'GENERATED':\n        return 'bg-blue-100 text-blue-800'\n      case 'PUBLISHED':\n        return 'bg-green-100 text-green-800'\n      case 'PENDING':\n        return 'bg-yellow-100 text-yellow-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getGradeColor = (grade: string) => {\n    switch (grade) {\n      case 'A+':\n        return 'bg-green-100 text-green-800'\n      case 'A':\n        return 'bg-green-100 text-green-800'\n      case 'B+':\n        return 'bg-blue-100 text-blue-800'\n      case 'B':\n        return 'bg-blue-100 text-blue-800'\n      case 'C+':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'C':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'D':\n        return 'bg-orange-100 text-orange-800'\n      case 'F':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const reportStats = {\n    total: reportCards.length,\n    generated: reportCards.filter(r => r.status === 'GENERATED').length,\n    published: reportCards.filter(r => r.status === 'PUBLISHED').length,\n    pending: reportCards.filter(r => r.status === 'PENDING').length,\n    averagePercentage: reportCards.length > 0 \n      ? Math.round(reportCards.reduce((sum, card) => sum + card.percentage, 0) / reportCards.length)\n      : 0\n  }\n\n  return (\n    <DashboardLayout title=\"Reports Management\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Reports Management</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">Generate and manage student report cards and academic reports</p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Button variant=\"outline\" className=\"w-full sm:w-auto\">\n              <BarChart3 className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Analytics</span>\n              <span className=\"hidden sm:inline\">Analytics Report</span>\n            </Button>\n            <Button className=\"w-full sm:w-auto\">\n              <FileText className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Generate</span>\n              <span className=\"hidden sm:inline\">Generate Reports</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n          <Card key=\"total-reports\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Reports</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{reportStats.total}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"generated-reports\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Generated</CardTitle>\n              <FileText className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{reportStats.generated}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"published-reports\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Published</CardTitle>\n              <FileText className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{reportStats.published}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"pending-reports\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n              <FileText className=\"h-4 w-4 text-yellow-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-yellow-600\">{reportStats.pending}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"average-score\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Avg Score</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-purple-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-purple-600\">{reportStats.averagePercentage}%</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <FileText className=\"w-5 h-5 mr-2\" />\n                Generate Report Cards\n              </CardTitle>\n              <CardDescription>\n                Create report cards for all students in a class\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"term-select\">Select Term</Label>\n                  <select\n                    id=\"term-select\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">Choose a term</option>\n                    <option value=\"term1\">Term 1</option>\n                    <option value=\"term2\">Term 2</option>\n                    <option value=\"term3\">Term 3</option>\n                  </select>\n                </div>\n                <div>\n                  <Label htmlFor=\"class-select\">Select Class</Label>\n                  <select\n                    id=\"class-select\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">Choose a class</option>\n                    <option value=\"grade8\">Grade 8</option>\n                    <option value=\"grade9\">Grade 9</option>\n                    <option value=\"grade10\">Grade 10</option>\n                  </select>\n                </div>\n                <Button className=\"w-full\">\n                  <FileText className=\"w-4 h-4 mr-2\" />\n                  Generate Reports\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <BarChart3 className=\"w-5 h-5 mr-2\" />\n                Performance Analytics\n              </CardTitle>\n              <CardDescription>\n                View detailed performance analytics\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  <TrendingUp className=\"w-4 h-4 mr-2\" />\n                  Class Performance\n                </Button>\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Award className=\"w-4 h-4 mr-2\" />\n                  Subject Analysis\n                </Button>\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Users className=\"w-4 h-4 mr-2\" />\n                  Student Rankings\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Download className=\"w-5 h-5 mr-2\" />\n                Export Reports\n              </CardTitle>\n              <CardDescription>\n                Export reports in various formats\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Download className=\"w-4 h-4 mr-2\" />\n                  Export to Excel\n                </Button>\n                <Button variant=\"outline\" className=\"w-full\">\n                  <FileText className=\"w-4 h-4 mr-2\" />\n                  Export to PDF\n                </Button>\n                <Button variant=\"outline\" className=\"w-full\">\n                  <Printer className=\"w-4 h-4 mr-2\" />\n                  Print Reports\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Filters</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"term\">Term</Label>\n                <select\n                  id=\"term\"\n                  value={selectedTerm}\n                  onChange={(e) => setSelectedTerm(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Terms</option>\n                  <option value=\"term1\">Term 1</option>\n                  <option value=\"term2\">Term 2</option>\n                  <option value=\"term3\">Term 3</option>\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"class\">Class</Label>\n                <select\n                  id=\"class\"\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Classes</option>\n                  <option value=\"grade8\">Grade 8</option>\n                  <option value=\"grade9\">Grade 9</option>\n                  <option value=\"grade10\">Grade 10</option>\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"status\">Status</Label>\n                <select\n                  id=\"status\"\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"generated\">Generated</option>\n                  <option value=\"published\">Published</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Report Cards Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Report Cards</CardTitle>\n            <CardDescription>\n              Generated report cards for students\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {/* Desktop Table */}\n            <div className=\"hidden lg:block overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Student\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Class\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Term\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Marks\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Percentage\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Grade\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Rank\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {reportCards.map((card) => (\n                    <tr key={card.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-gray-700\">\n                                {card.studentName.split(' ').map(n => n[0]).join('')}\n                              </span>\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{card.studentName}</div>\n                            <div className=\"text-sm text-gray-500\">{card.admissionNo}</div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {card.className} - {card.sectionName}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {card.termName}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {card.obtainedMarks}/{card.totalMarks}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {card.percentage}%\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(card.grade)}`}>\n                          {card.grade}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {card.rank}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(card.status)}`}>\n                          {card.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <div className=\"flex space-x-2\">\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"w-4 h-4\" />\n                          </Button>\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Download className=\"w-4 h-4\" />\n                          </Button>\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Printer className=\"w-4 h-4\" />\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"lg:hidden space-y-4\">\n              {reportCards.map((card) => (\n                <Card key={card.id} className=\"p-4\">\n                  <div className=\"flex flex-col space-y-3\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0\">\n                          <span className=\"text-sm font-medium text-gray-700\">\n                            {card.studentName.split(' ').map(n => n[0]).join('')}\n                          </span>\n                        </div>\n                        <div className=\"min-w-0 flex-1\">\n                          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">\n                            {card.studentName}\n                          </h3>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            {card.admissionNo} • {card.className} - {card.sectionName}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(card.status)}`}>\n                          {card.status}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Term:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{card.termName}</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Marks:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{card.obtainedMarks}/{card.totalMarks}</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Percentage:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{card.percentage}%</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Grade:</span>\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(card.grade)}`}>\n                          {card.grade}\n                        </span>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Rank:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">#{card.rank}</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Generated:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{card.generatedAt}</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700\">\n                      <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                        <Eye className=\"w-4 h-4 mr-1\" />\n                        View\n                      </Button>\n                      <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                        <Download className=\"w-4 h-4 mr-1\" />\n                        Download\n                      </Button>\n                      <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                        <Printer className=\"w-4 h-4 mr-1\" />\n                        Print\n                      </Button>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA;AArCA;;;;;;;;;AAuCe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kWAAQ,EAAC;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,kWAAQ,EAAC;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kWAAQ,EAAC;IAErD,oDAAoD;IACpD,IAAA,mWAAS,EAAC;QACR,eAAe;YACb;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,cAAc;gBACd,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,cAAc;gBACd,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,cAAc;gBACd,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,QAAQ;YACV;SACD;IACH,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,YAAY,MAAM;QACzB,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACnE,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACnE,SAAS,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC/D,mBAAmB,YAAY,MAAM,GAAG,IACpC,KAAK,KAAK,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,YAAY,MAAM,IAC3F;IACN;IAEA,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAqB,YAAY,6KAAe;kBACrE,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,+XAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAEpD,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,8KAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,+XAAC,qVAAS;4CAAC,WAAU;;;;;;sDACrB,+XAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,+XAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,+XAAC,8KAAM;oCAAC,WAAU;;sDAChB,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;sDACpB,+XAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,+XAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;8BAMzC,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsB,YAAY,KAAK;;;;;;;;;;;;2BANhD;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAoC,YAAY,SAAS;;;;;;;;;;;;2BANlE;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAqC,YAAY,SAAS;;;;;;;;;;;;2BANnE;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsC,YAAY,OAAO;;;;;;;;;;;;2BANlE;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CAAsC,YAAY,iBAAiB;4CAAC;;;;;;;;;;;;;2BAN7E;;;;;;;;;;;8BAYZ,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;;sDACT,+XAAC,+KAAS;4CAAC,WAAU;;8DACnB,+XAAC,gVAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,+XAAC,qLAAe;sDAAC;;;;;;;;;;;;8CAInB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,+XAAC;wDACC,IAAG;wDACH,WAAU;;0EAEV,+XAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,+XAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,+XAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,+XAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAG1B,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,+XAAC;wDACC,IAAG;wDACH,WAAU;;0EAEV,+XAAC;gEAAO,OAAM;0EAAG;;;;;;0EACjB,+XAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,+XAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,+XAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAG5B,+XAAC,8KAAM;gDAAC,WAAU;;kEAChB,+XAAC,gVAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;;sDACT,+XAAC,+KAAS;4CAAC,WAAU;;8DACnB,+XAAC,qVAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,+XAAC,qLAAe;sDAAC;;;;;;;;;;;;8CAInB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,sVAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGzC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,mUAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,mUAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAO1C,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;;sDACT,+XAAC,+KAAS;4CAAC,WAAU;;8DACnB,+XAAC,4UAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,+XAAC,qLAAe;sDAAC;;;;;;;;;;;;8CAInB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,4UAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,gVAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,yUAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9C,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;sCACT,cAAA,+XAAC,+KAAS;0CAAC;;;;;;;;;;;sCAEb,+XAAC,iLAAW;sCACV,cAAA,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,+XAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,+XAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,+XAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,+XAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAG5B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,+XAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,+XAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlC,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;;8CACT,+XAAC,+KAAS;8CAAC;;;;;;8CACX,+XAAC,qLAAe;8CAAC;;;;;;;;;;;;sCAInB,+XAAC,iLAAW;;8CAEV,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAM,WAAU;;0DACf,+XAAC;gDAAM,WAAU;0DACf,cAAA,+XAAC;;sEACC,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;sEAG/F,+XAAC;4DAAG,WAAU;sEAAiF;;;;;;;;;;;;;;;;;0DAKnG,+XAAC;gDAAM,WAAU;0DACd,YAAY,GAAG,CAAC,CAAC,qBAChB,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EACZ,cAAA,+XAAC;oEAAI,WAAU;;sFACb,+XAAC;4EAAI,WAAU;sFACb,cAAA,+XAAC;gFAAI,WAAU;0FACb,cAAA,+XAAC;oFAAK,WAAU;8FACb,KAAK,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;sFAIvD,+XAAC;4EAAI,WAAU;;8FACb,+XAAC;oFAAI,WAAU;8FAAqC,KAAK,WAAW;;;;;;8FACpE,+XAAC;oFAAI,WAAU;8FAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;0EAI9D,+XAAC;gEAAG,WAAU;;oEACX,KAAK,SAAS;oEAAC;oEAAI,KAAK,WAAW;;;;;;;0EAEtC,+XAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ;;;;;;0EAEhB,+XAAC;gEAAG,WAAU;;oEACX,KAAK,aAAa;oEAAC;oEAAE,KAAK,UAAU;;;;;;;0EAEvC,+XAAC;gEAAG,WAAU;;oEACX,KAAK,UAAU;oEAAC;;;;;;;0EAEnB,+XAAC;gEAAG,WAAU;0EACZ,cAAA,+XAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,cAAc,KAAK,KAAK,GAAG;8EACrG,KAAK,KAAK;;;;;;;;;;;0EAGf,+XAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;0EAEZ,+XAAC;gEAAG,WAAU;0EACZ,cAAA,+XAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,KAAK,MAAM,GAAG;8EACvG,KAAK,MAAM;;;;;;;;;;;0EAGhB,+XAAC;gEAAG,WAAU;0EACZ,cAAA,+XAAC;oEAAI,WAAU;;sFACb,+XAAC,8KAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAC7B,cAAA,+XAAC,6TAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,+XAAC,8KAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAC7B,cAAA,+XAAC,4UAAQ;gFAAC,WAAU;;;;;;;;;;;sFAEtB,+XAAC,8KAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAC7B,cAAA,+XAAC,yUAAO;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uDAlDlB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8CA6DxB,+XAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,+XAAC,0KAAI;4CAAe,WAAU;sDAC5B,cAAA,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;wDAAI,WAAU;;0EACb,+XAAC;gEAAI,WAAU;;kFACb,+XAAC;wEAAI,WAAU;kFACb,cAAA,+XAAC;4EAAK,WAAU;sFACb,KAAK,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;kFAGrD,+XAAC;wEAAI,WAAU;;0FACb,+XAAC;gFAAG,WAAU;0FACX,KAAK,WAAW;;;;;;0FAEnB,+XAAC;gFAAE,WAAU;;oFACV,KAAK,WAAW;oFAAC;oFAAI,KAAK,SAAS;oFAAC;oFAAI,KAAK,WAAW;;;;;;;;;;;;;;;;;;;0EAI/D,+XAAC;gEAAI,WAAU;0EACb,cAAA,+XAAC;oEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,KAAK,MAAM,GAAG;8EACvG,KAAK,MAAM;;;;;;;;;;;;;;;;;kEAKlB,+XAAC;wDAAI,WAAU;;0EACb,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAE,WAAU;kFAAoC,KAAK,QAAQ;;;;;;;;;;;;0EAEhE,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAE,WAAU;;4EAAoC,KAAK,aAAa;4EAAC;4EAAE,KAAK,UAAU;;;;;;;;;;;;;0EAEvF,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAE,WAAU;;4EAAoC,KAAK,UAAU;4EAAC;;;;;;;;;;;;;0EAEnE,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAK,WAAW,CAAC,yDAAyD,EAAE,cAAc,KAAK,KAAK,GAAG;kFACrG,KAAK,KAAK;;;;;;;;;;;;0EAGf,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAE,WAAU;;4EAAmC;4EAAE,KAAK,IAAI;;;;;;;;;;;;;0EAE7D,+XAAC;;kFACC,+XAAC;wEAAK,WAAU;kFAA+C;;;;;;kFAC/D,+XAAC;wEAAE,WAAU;kFAAoC,KAAK,WAAW;;;;;;;;;;;;;;;;;;kEAIrE,+XAAC;wDAAI,WAAU;;0EACb,+XAAC,8KAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,WAAU;;kFAC5C,+XAAC,6TAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,+XAAC,8KAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,WAAU;;kFAC5C,+XAAC,4UAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,+XAAC,8KAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,WAAU;;kFAC5C,+XAAC,yUAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;2CAhEjC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ElC", "debugId": null}}]}