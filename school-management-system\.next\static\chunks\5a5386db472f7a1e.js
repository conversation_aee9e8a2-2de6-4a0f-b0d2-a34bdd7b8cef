(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,a)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>m],30151);var t=e.i(53379),a=e.i(46686);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return e=>{let a=!1,s=t.map(t=>{let s=r(t,e);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let e=0;e<s.length;e++){let a=s[e];"function"==typeof a?a():r(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.useCallback(s(...t),t)}function i(e){let r=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=r).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,t){let a={...t};for(let r in t){let s=e[r],n=t[r];/^on[A-Z]/.test(r)?s&&n?a[r]=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];let r=n(...t);return s(...t),r}:s&&(a[r]=s):"style"===r?a[r]={...s,...n}:"className"===r&&(a[r]=[s,n].filter(Boolean).join(" "))}return{...e,...a}}(n,r.props);return r.type!==a.Fragment&&(u.ref=t?s(t,o):o),a.cloneElement(r,u)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=a.forwardRef((e,s)=>{let{children:n,...i}=e,l=a.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,t.jsx)(r,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),u=e.i(36946);let h=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=a.forwardRef((e,a)=>{let{className:r,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,u.cn)(h({variant:s,size:n,className:r})),ref:a,...d})});m.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),a=e.i(50321),r=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let n=(0,r.createSlot)("Primitive.".concat(a)),i=t.forwardRef((e,t)=>{let{asChild:r,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(r?n:a,{...i,ref:t})});return i.displayName="Primitive.".concat(a),{...e,[a]:i}},{});function i(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},r=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:u,...h}=e;return(0,t.createElement)("svg",{ref:a,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:r("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(o)?o:[o]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:r("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=a(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>r,"teacherNavigation",()=>a]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],a=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],r=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},96487,e=>{"use strict";e.s(["Input",()=>s]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,type:n,...i}=e;return(0,t.jsx)("input",{type:n,className:(0,r.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...i})});s.displayName="Input"},70307,e=>{"use strict";e.s(["Alert",()=>i,"AlertDescription",()=>l]);var t=e.i(53379),a=e.i(46686),r=e.i(94323),s=e.i(36946);let n=(0,r.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),i=a.forwardRef((e,a)=>{let{className:r,variant:i,...l}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,s.cn)(n({variant:i}),r),...l})});i.displayName="Alert",a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)("h5",{ref:a,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",r),...n})}).displayName="AlertTitle";let l=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",r),...n})});l.displayName="AlertDescription"},90285,e=>{"use strict";e.s(["Badge",()=>n]);var t=e.i(53379),a=e.i(94323),r=e.i(36946);let s=(0,a.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function n(e){let{className:a,variant:n,...i}=e;return(0,t.jsx)("div",{className:(0,r.cn)(s({variant:n}),a),...i})}},76931,e=>{"use strict";e.s(["default",()=>h],76931);var t=e.i(53379),a=e.i(46686),r=e.i(18125),s=e.i(30151),n=e.i(32668),i=e.i(70307),l=e.i(96487),d=e.i(90285);function c(e){let{classes:i,pagination:c,onPageChange:o,onSearch:u,onFilter:h,loading:m=!1}=e,f=(0,r.useRouter)(),[x,g]=(0,a.useState)(""),[p,v]=(0,a.useState)("all"),[b,y]=(0,a.useState)(null),j=async e=>{if(confirm("Are you sure you want to delete this class? This action cannot be undone.")){y(e);try{let t=await fetch("/api/admin/classes/".concat(e),{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete class")}window.location.reload()}catch(e){alert(e.message)}finally{y(null)}}};return m?(0,t.jsx)(n.Card,{children:(0,t.jsx)(n.CardContent,{className:"p-6",children:(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})})})}):(0,t.jsxs)(n.Card,{children:[(0,t.jsx)(n.CardHeader,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)(n.CardTitle,{children:["Classes (",c.total,")"]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:[(0,t.jsx)(l.Input,{placeholder:"Search classes...",value:x,onChange:e=>{var t;g(t=e.target.value),u(t)},className:"w-full sm:w-64"}),(0,t.jsxs)("select",{value:p,onChange:e=>{var t;v(t=e.target.value),h(t)},className:"w-full sm:w-auto p-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"all",children:"All Classes"}),(0,t.jsx)("option",{value:"true",children:"Active Only"}),(0,t.jsx)("option",{value:"false",children:"Inactive Only"})]})]})]})}),(0,t.jsxs)(n.CardContent,{children:[0===i.length?(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No classes found."})}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Class Name"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Section"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Teacher"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Students"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Capacity"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Academic Year"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Status"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:i.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("div",{className:"font-medium",children:e.name})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(d.Badge,{variant:"secondary",children:e.section.name})}),(0,t.jsx)("td",{className:"p-2",children:e.teacher?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[e.teacher.firstName," ",e.teacher.lastName]}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.teacher.email})]}):(0,t.jsx)("span",{className:"text-gray-500 text-sm",children:"Not assigned"})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:e._count.students}),e.capacity&&(0,t.jsxs)("span",{className:"text-gray-500",children:["/ ",e.capacity]})]})}),(0,t.jsx)("td",{className:"p-2",children:e.capacity||"-"}),(0,t.jsx)("td",{className:"p-2",children:e.academicYear||"-"}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(d.Badge,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(s.Button,{size:"sm",variant:"outline",onClick:()=>f.push("/admin/classes/".concat(e.id)),children:"View"}),(0,t.jsx)(s.Button,{size:"sm",variant:"outline",onClick:()=>f.push("/admin/classes/".concat(e.id,"/edit")),children:"Edit"}),(0,t.jsx)(s.Button,{size:"sm",variant:"destructive",onClick:()=>j(e.id),disabled:b===e.id,children:b===e.id?"Deleting...":"Delete"})]})})]},e.id))})]})}),c.totalPages>1&&(0,t.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(c.page-1)*c.limit+1," to"," ",Math.min(c.page*c.limit,c.total)," of"," ",c.total," classes"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>o(c.page-1),disabled:c.page<=1,children:"Previous"}),(0,t.jsxs)("span",{className:"px-3 py-2 text-sm",children:["Page ",c.page," of ",c.totalPages]}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>o(c.page+1),disabled:c.page>=c.totalPages,children:"Next"})]})]})]})]})}var o=e.i(89559),u=e.i(84633);function h(){let e=(0,r.useRouter)(),[l,d]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0),[f,x]=(0,a.useState)(""),[g,p]=(0,a.useState)({page:1,limit:10,total:0,totalPages:0}),[v,b]=(0,a.useState)(""),[y,j]=(0,a.useState)("all"),N=async()=>{try{m(!0);let e=new URLSearchParams({page:g.page.toString(),limit:g.limit.toString(),...v&&{search:v},..."all"!==y&&{isActive:y}}),t=await fetch("/api/admin/classes?".concat(e));if(!t.ok)throw Error("Failed to fetch classes");let a=await t.json();d(a.classes),p(a.pagination)}catch(e){x(e.message)}finally{m(!1)}};return(0,a.useEffect)(()=>{N()},[g.page,v,y]),(0,t.jsx)(o.default,{title:"Class Management",navigation:u.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Class Management"}),(0,t.jsx)(s.Button,{onClick:()=>e.push("/admin/classes/new"),children:"Add New Class"})]}),f&&(0,t.jsx)(i.Alert,{variant:"destructive",children:(0,t.jsx)(i.AlertDescription,{children:f})}),(0,t.jsx)(c,{classes:l,pagination:g,onPageChange:e=>{p(t=>({...t,page:e}))},onSearch:e=>{b(e),p(e=>({...e,page:1}))},onFilter:e=>{j(e),p(e=>({...e,page:1}))},loading:h}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(n.Card,{children:[(0,t.jsx)(n.CardHeader,{className:"pb-2",children:(0,t.jsx)(n.CardTitle,{className:"text-sm font-medium",children:"Total Classes"})}),(0,t.jsx)(n.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:g.total})})]},"total-classes"),(0,t.jsxs)(n.Card,{children:[(0,t.jsx)(n.CardHeader,{className:"pb-2",children:(0,t.jsx)(n.CardTitle,{className:"text-sm font-medium",children:"Active Classes"})}),(0,t.jsx)(n.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:l.filter(e=>e.isActive).length})})]},"active-classes"),(0,t.jsxs)(n.Card,{children:[(0,t.jsx)(n.CardHeader,{className:"pb-2",children:(0,t.jsx)(n.CardTitle,{className:"text-sm font-medium",children:"Total Students"})}),(0,t.jsx)(n.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:l.reduce((e,t)=>e+t._count.students,0)})})]},"total-students-in-classes"),(0,t.jsxs)(n.Card,{children:[(0,t.jsx)(n.CardHeader,{className:"pb-2",children:(0,t.jsx)(n.CardTitle,{className:"text-sm font-medium",children:"Average Class Size"})}),(0,t.jsx)(n.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:l.length>0?Math.round(l.reduce((e,t)=>e+t._count.students,0)/l.length):0})})]},"average-class-size")]})]})})}}]);