(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,70307,e=>{"use strict";e.s(["Alert",()=>n,"AlertDescription",()=>d]);var t=e.i(53379),r=e.i(46686),a=e.i(94323),s=e.i(36946);let l=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),n=r.forwardRef((e,r)=>{let{className:a,variant:n,...d}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,s.cn)(l({variant:n}),a),...d})});n.displayName="Alert",r.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,t.jsx)("h5",{ref:r,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",a),...l})}).displayName="AlertTitle";let d=r.forwardRef((e,r)=>{let{className:a,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",a),...l})});d.displayName="AlertDescription"},18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>i,"CardDescription",()=>d,"CardHeader",()=>l,"CardTitle",()=>n]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...l})});s.displayName="Card";let l=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});l.displayName="CardHeader";let n=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});n.displayName="CardTitle";let d=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...l})});d.displayName="CardDescription";let i=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...l})});i.displayName="CardContent",r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>x],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,s=t.map(t=>{let s=a(t,e);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let e=0;e<s.length;e++){let r=s[e];"function"==typeof r?r():a(t[e],null)}}}}function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(s(...t),t)}function n(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...l}=e;if(r.isValidElement(a)){var n,d,i;let e,o,c=(o=(e=null==(d=Object.getOwnPropertyDescriptor((n=a).props,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=null==(i=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref,u=function(e,t){let r={...t};for(let a in t){let s=e[a],l=t[a];/^on[A-Z]/.test(a)?s&&l?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=l(...t);return s(...t),a}:s&&(r[a]=s):"style"===a?r[a]={...s,...l}:"className"===a&&(r[a]=[s,l].filter(Boolean).join(" "))}return{...e,...r}}(l,a.props);return a.type!==r.Fragment&&(u.ref=t?s(t,c):c),r.cloneElement(a,u)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),l=r.forwardRef((e,s)=>{let{children:l,...n}=e,d=r.Children.toArray(l),i=d.find(o);if(i){let e=i.props.children,l=d.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...n,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,t.jsx)(a,{...n,ref:s,children:l})});return l.displayName="".concat(e,".Slot"),l}e.s(["Slot",()=>d,"createSlot",()=>n],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>l],35952);var d=n("Slot"),i=Symbol("radix.slottable");function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}var c=e.i(94323),u=e.i(36946);let g=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=r.forwardRef((e,r)=>{let{className:a,variant:s,size:l,asChild:n=!1,...i}=e;return(0,t.jsx)(n?d:"button",{className:(0,u.cn)(g({variant:s,size:l,className:a})),ref:r,...i})});x.displayName="Button"},96487,e=>{"use strict";e.s(["Input",()=>s]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,type:l,...n}=e;return(0,t.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...n})});s.displayName="Input"},90285,e=>{"use strict";e.s(["Badge",()=>l]);var t=e.i(53379),r=e.i(94323),a=e.i(36946);let s=(0,r.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:l,...n}=e;return(0,t.jsx)("div",{className:(0,a.cn)(s({variant:l}),r),...n})}},43788,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(53379),r=e.i(46686),a=e.i(18125),s=e.i(30151),l=e.i(96487),n=e.i(90285),d=e.i(32668),i=e.i(70307);function o(){let e=(0,a.useRouter)(),[o,c]=(0,r.useState)([]),[u,g]=(0,r.useState)(!0),[x,f]=(0,r.useState)(""),[m,h]=(0,r.useState)({page:1,limit:10,total:0,totalPages:0}),[p,b]=(0,r.useState)(""),[y,v]=(0,r.useState)(""),j=async()=>{try{g(!0);let e=new URLSearchParams({page:m.page.toString(),limit:m.limit.toString(),...p&&{classId:p},...y&&{date:y}}),t=await fetch("/api/teacher/attendance?".concat(e));if(!t.ok)throw Error("Failed to fetch attendance");let r=await t.json();c(r.attendanceRecords),h(r.pagination)}catch(e){f(e.message)}finally{g(!1)}};(0,r.useEffect)(()=>{j()},[m.page,p,y]);let N=e=>{h(t=>({...t,page:e}))};return u?(0,t.jsx)("div",{className:"flex justify-center p-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Attendance Management"}),(0,t.jsx)(s.Button,{onClick:()=>e.push("/teacher/attendance/mark"),children:"Mark Attendance"})]}),x&&(0,t.jsx)(i.Alert,{variant:"destructive",children:(0,t.jsx)(i.AlertDescription,{children:x})}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)(d.CardTitle,{children:["Attendance Records (",m.total,")"]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:[(0,t.jsx)(l.Input,{type:"date",value:y,onChange:e=>v(e.target.value),className:"w-full sm:w-auto"}),(0,t.jsxs)("select",{value:p,onChange:e=>b(e.target.value),className:"w-full sm:w-auto p-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Classes"}),(0,t.jsx)("option",{value:"1",children:"Grade 8A"}),(0,t.jsx)("option",{value:"2",children:"Grade 8B"})]})]})]})}),(0,t.jsxs)(d.CardContent,{children:[0===o.length?(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No attendance records found."})}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Student"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Class"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Status"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Remarks"})]})}),(0,t.jsx)("tbody",{children:o.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("div",{className:"font-medium",children:new Date(e.date).toLocaleDateString()})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[e.student.firstName," ",e.student.lastName]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Roll No: ",e.student.rollNumber]})]})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)(n.Badge,{variant:"secondary",children:[e.class.name," ",e.class.section.name]})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(n.Badge,{className:(e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(e.status),children:e.status})}),(0,t.jsx)("td",{className:"p-2",children:e.remarks||"-"})]},e.id))})]})}),m.totalPages>1&&(0,t.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(m.page-1)*m.limit+1," to"," ",Math.min(m.page*m.limit,m.total)," of"," ",m.total," records"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>N(m.page-1),disabled:m.page<=1,children:"Previous"}),(0,t.jsxs)("span",{className:"px-3 py-2 text-sm",children:["Page ",m.page," of ",m.totalPages]}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>N(m.page+1),disabled:m.page>=m.totalPages,children:"Next"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Total Records"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:m.total})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Present Today"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:o.filter(e=>"PRESENT"===e.status&&new Date(e.date).toDateString()===new Date().toDateString()).length})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Absent Today"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:o.filter(e=>"ABSENT"===e.status&&new Date(e.date).toDateString()===new Date().toDateString()).length})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Late Today"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:o.filter(e=>"LATE"===e.status&&new Date(e.date).toDateString()===new Date().toDateString()).length})})]})]})]})}}]);