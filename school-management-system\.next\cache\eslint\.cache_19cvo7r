[{"C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx": "1", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx": "2", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx": "3", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx": "4", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx": "5", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx": "6", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx": "7", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx": "8", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx": "9", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx": "10", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx": "11", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx": "12", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx": "13", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx": "14", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx": "15", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx": "16", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx": "17", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx": "18", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx": "19", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx": "20", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx": "21", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx": "22", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx": "23", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx": "24", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx": "25", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx": "26", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx": "27", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx": "28", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx": "29", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts": "30", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts": "31", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts": "32", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts": "33", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts": "34", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts": "35", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts": "36", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts": "37", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts": "38", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts": "39", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts": "40", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts": "41", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts": "42", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts": "43", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts": "44", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "45", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts": "46", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts": "47", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts": "48", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts": "49", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts": "50", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts": "51", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts": "52", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts": "53", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts": "54", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx": "55", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx": "56", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx": "57", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx": "58", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx": "59", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx": "60", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx": "61", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx": "62", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx": "63", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx": "64", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx": "65", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx": "66", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx": "67", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx": "68", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx": "69", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx": "70", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx": "71", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx": "72", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx": "73", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx": "74", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx": "75", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx": "76", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx": "77", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx": "78", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx": "79", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx": "80", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx": "81", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx": "82", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx": "83", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx": "84", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts": "85", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts": "86", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts": "87", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts": "88", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts": "89", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts": "90", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts": "91", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts": "92", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts": "93", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts": "94", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts": "95", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\template\\route.ts": "96", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\StudentImport.tsx": "97", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\progress.tsx": "98"}, {"size": 6234, "mtime": 1756888610598, "results": "99", "hashOfConfig": "100"}, {"size": 13827, "mtime": 1756885850874, "results": "101", "hashOfConfig": "100"}, {"size": 633, "mtime": 1756885899712, "results": "102", "hashOfConfig": "100"}, {"size": 5114, "mtime": 1756899133723, "results": "103", "hashOfConfig": "100"}, {"size": 9702, "mtime": 1756885931424, "results": "104", "hashOfConfig": "100"}, {"size": 14659, "mtime": 1756899227679, "results": "105", "hashOfConfig": "100"}, {"size": 10877, "mtime": 1756898413319, "results": "106", "hashOfConfig": "100"}, {"size": 23444, "mtime": 1756899204270, "results": "107", "hashOfConfig": "100"}, {"size": 28080, "mtime": 1756887359141, "results": "108", "hashOfConfig": "100"}, {"size": 934, "mtime": 1756815886903, "results": "109", "hashOfConfig": "100"}, {"size": 1430, "mtime": 1756885266272, "results": "110", "hashOfConfig": "100"}, {"size": 4690, "mtime": 1756891054176, "results": "111", "hashOfConfig": "100"}, {"size": 2727, "mtime": 1756890831207, "results": "112", "hashOfConfig": "100"}, {"size": 13822, "mtime": 1756890820333, "results": "113", "hashOfConfig": "100"}, {"size": 8847, "mtime": 1756899161618, "results": "114", "hashOfConfig": "100"}, {"size": 1139, "mtime": 1756885273906, "results": "115", "hashOfConfig": "100"}, {"size": 4905, "mtime": 1756899174634, "results": "116", "hashOfConfig": "100"}, {"size": 3239, "mtime": 1756890791506, "results": "117", "hashOfConfig": "100"}, {"size": 9418, "mtime": 1756890885919, "results": "118", "hashOfConfig": "100"}, {"size": 8053, "mtime": 1756887545945, "results": "119", "hashOfConfig": "100"}, {"size": 18371, "mtime": 1756894954105, "results": "120", "hashOfConfig": "100"}, {"size": 13272, "mtime": 1756896519717, "results": "121", "hashOfConfig": "100"}, {"size": 11164, "mtime": 1756894988580, "results": "122", "hashOfConfig": "100"}, {"size": 398, "mtime": 1756798332445, "results": "123", "hashOfConfig": "100"}, {"size": 9863, "mtime": 1756798332445, "results": "124", "hashOfConfig": "100"}, {"size": 9273, "mtime": 1756894860221, "results": "125", "hashOfConfig": "100"}, {"size": 6607, "mtime": 1756894882482, "results": "126", "hashOfConfig": "100"}, {"size": 8895, "mtime": 1756894918199, "results": "127", "hashOfConfig": "100"}, {"size": 11708, "mtime": 1756896370625, "results": "128", "hashOfConfig": "100"}, {"size": 2332, "mtime": 1756886129726, "results": "129", "hashOfConfig": "100"}, {"size": 7806, "mtime": 1756892297872, "results": "130", "hashOfConfig": "100"}, {"size": 4551, "mtime": 1756886222137, "results": "131", "hashOfConfig": "100"}, {"size": 3234, "mtime": 1756898086295, "results": "132", "hashOfConfig": "100"}, {"size": 2536, "mtime": 1756814576844, "results": "133", "hashOfConfig": "100"}, {"size": 2467, "mtime": 1756814471224, "results": "134", "hashOfConfig": "100"}, {"size": 5236, "mtime": 1756814485233, "results": "135", "hashOfConfig": "100"}, {"size": 5515, "mtime": 1756799849243, "results": "136", "hashOfConfig": "100"}, {"size": 8633, "mtime": 1756900306743, "results": "137", "hashOfConfig": "100"}, {"size": 11490, "mtime": 1756976353537, "results": "138", "hashOfConfig": "100"}, {"size": 6863, "mtime": 1756891698086, "results": "139", "hashOfConfig": "100"}, {"size": 8063, "mtime": 1756885638017, "results": "140", "hashOfConfig": "100"}, {"size": 6338, "mtime": 1756816248462, "results": "141", "hashOfConfig": "100"}, {"size": 8295, "mtime": 1756891390912, "results": "142", "hashOfConfig": "100"}, {"size": 5832, "mtime": 1756891014057, "results": "143", "hashOfConfig": "100"}, {"size": 163, "mtime": 1756792184475, "results": "144", "hashOfConfig": "100"}, {"size": 4732, "mtime": 1756816268741, "results": "145", "hashOfConfig": "100"}, {"size": 4085, "mtime": 1756896137803, "results": "146", "hashOfConfig": "100"}, {"size": 1652, "mtime": 1756814576851, "results": "147", "hashOfConfig": "100"}, {"size": 1368, "mtime": 1756814576858, "results": "148", "hashOfConfig": "100"}, {"size": 8314, "mtime": 1756815108834, "results": "149", "hashOfConfig": "100"}, {"size": 4821, "mtime": 1756896118959, "results": "150", "hashOfConfig": "100"}, {"size": 1666, "mtime": 1756893659821, "results": "151", "hashOfConfig": "100"}, {"size": 2211, "mtime": 1756895800633, "results": "152", "hashOfConfig": "100"}, {"size": 7312, "mtime": 1756894402060, "results": "153", "hashOfConfig": "100"}, {"size": 945, "mtime": 1756886916185, "results": "154", "hashOfConfig": "100"}, {"size": 3287, "mtime": 1756886934351, "results": "155", "hashOfConfig": "100"}, {"size": 4644, "mtime": 1756889607556, "results": "156", "hashOfConfig": "100"}, {"size": 1775, "mtime": 1756792184480, "results": "157", "hashOfConfig": "100"}, {"size": 9653, "mtime": 1756890967660, "results": "158", "hashOfConfig": "100"}, {"size": 2569, "mtime": 1756888490440, "results": "159", "hashOfConfig": "100"}, {"size": 4796, "mtime": 1756888515488, "results": "160", "hashOfConfig": "100"}, {"size": 8108, "mtime": 1756798104289, "results": "161", "hashOfConfig": "100"}, {"size": 8908, "mtime": 1756798123208, "results": "162", "hashOfConfig": "100"}, {"size": 6869, "mtime": 1756892563597, "results": "163", "hashOfConfig": "100"}, {"size": 10055, "mtime": 1756894133538, "results": "164", "hashOfConfig": "100"}, {"size": 14550, "mtime": 1756894523296, "results": "165", "hashOfConfig": "100"}, {"size": 13419, "mtime": 1756894096579, "results": "166", "hashOfConfig": "100"}, {"size": 424, "mtime": 1756882431724, "results": "167", "hashOfConfig": "100"}, {"size": 3253, "mtime": 1756881867586, "results": "168", "hashOfConfig": "100"}, {"size": 11015, "mtime": 1756976543352, "results": "169", "hashOfConfig": "100"}, {"size": 12859, "mtime": 1756887183493, "results": "170", "hashOfConfig": "100"}, {"size": 17303, "mtime": 1756886864336, "results": "171", "hashOfConfig": "100"}, {"size": 10412, "mtime": 1756797937081, "results": "172", "hashOfConfig": "100"}, {"size": 14392, "mtime": 1756890917447, "results": "173", "hashOfConfig": "100"}, {"size": 1783, "mtime": 1756880256590, "results": "174", "hashOfConfig": "100"}, {"size": 1300, "mtime": 1756880299321, "results": "175", "hashOfConfig": "100"}, {"size": 2085, "mtime": 1756880229145, "results": "176", "hashOfConfig": "100"}, {"size": 2032, "mtime": 1756880929472, "results": "177", "hashOfConfig": "100"}, {"size": 7302, "mtime": 1756879926941, "results": "178", "hashOfConfig": "100"}, {"size": 920, "mtime": 1756880242756, "results": "179", "hashOfConfig": "100"}, {"size": 732, "mtime": 1756792184477, "results": "180", "hashOfConfig": "100"}, {"size": 2835, "mtime": 1756886907469, "results": "181", "hashOfConfig": "100"}, {"size": 1583, "mtime": 1756882485393, "results": "182", "hashOfConfig": "100"}, {"size": 1695, "mtime": 1756882497465, "results": "183", "hashOfConfig": "100"}, {"size": 5287, "mtime": 1756888536272, "results": "184", "hashOfConfig": "100"}, {"size": 5639, "mtime": 1756889574866, "results": "185", "hashOfConfig": "100"}, {"size": 2144, "mtime": 1756889548014, "results": "186", "hashOfConfig": "100"}, {"size": 288, "mtime": 1756791879185, "results": "187", "hashOfConfig": "100"}, {"size": 4030, "mtime": 1756799139956, "results": "188", "hashOfConfig": "100"}, {"size": 5462, "mtime": 1756895773964, "results": "189", "hashOfConfig": "100"}, {"size": 2316, "mtime": 1756898452977, "results": "190", "hashOfConfig": "100"}, {"size": 3183, "mtime": 1756812578167, "results": "191", "hashOfConfig": "100"}, {"size": 172, "mtime": 1756792184477, "results": "192", "hashOfConfig": "100"}, {"size": 1935, "mtime": 1756891638445, "results": "193", "hashOfConfig": "100"}, {"size": 559, "mtime": 1756798682449, "results": "194", "hashOfConfig": "100"}, {"size": 2121, "mtime": 1756976398242, "results": "195", "hashOfConfig": "100"}, {"size": 9303, "mtime": 1756976436031, "results": "196", "hashOfConfig": "100"}, {"size": 791, "mtime": 1756976447445, "results": "197", "hashOfConfig": "100"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13embjg", {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx", ["492"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx", ["493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx", ["505", "506", "507", "508", "509", "510"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx", ["511", "512", "513", "514"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx", ["515", "516", "517", "518"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx", ["519", "520"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx", ["521", "522", "523", "524", "525", "526", "527"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx", ["528", "529", "530"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx", ["531", "532", "533", "534"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx", ["535", "536", "537", "538", "539", "540"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx", ["541", "542", "543", "544", "545", "546"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx", ["547", "548", "549", "550", "551", "552", "553", "554"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx", ["555", "556"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx", ["557", "558", "559", "560"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx", ["561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx", ["573", "574"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx", ["575", "576"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx", ["577"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx", ["578", "579", "580"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx", ["581", "582", "583", "584", "585", "586"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx", ["587", "588"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx", ["589", "590"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx", ["591", "592", "593", "594"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx", ["595", "596", "597", "598", "599", "600", "601", "602", "603", "604"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx", ["605", "606", "607", "608", "609"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx", ["610", "611", "612"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts", ["613", "614", "615"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts", ["616", "617", "618", "619"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts", ["620", "621", "622"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts", ["623"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts", ["624"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts", ["625"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts", ["626"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts", ["627", "628", "629", "630"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts", ["631", "632", "633", "634", "635"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts", ["636", "637", "638"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts", ["639", "640", "641", "642", "643", "644"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts", ["645", "646"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts", ["647"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts", ["648", "649"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts", ["650"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts", ["651"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts", ["652"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts", ["653", "654"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts", ["655"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts", ["656"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts", ["657"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx", ["658"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx", ["659", "660", "661"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx", ["662", "663"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx", ["664", "665", "666"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx", ["667", "668"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx", ["669"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx", ["670", "671"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx", ["672"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx", ["673"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx", ["674", "675", "676", "677"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx", ["678", "679", "680", "681", "682"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx", ["683"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx", ["684", "685"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx", ["686"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts", ["687"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts", ["688", "689"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts", ["690"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts", ["691", "692", "693", "694"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts", ["695"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\template\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\StudentImport.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\progress.tsx", [], [], {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "699", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "700", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 16}, {"ruleId": "696", "severity": 1, "message": "701", "line": 14, "column": 3, "nodeType": null, "messageId": "698", "endLine": 14, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "702", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "697", "line": 16, "column": 3, "nodeType": null, "messageId": "698", "endLine": 16, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "703", "line": 17, "column": 3, "nodeType": null, "messageId": "698", "endLine": 17, "endColumn": 12}, {"ruleId": "696", "severity": 1, "message": "704", "line": 18, "column": 3, "nodeType": null, "messageId": "698", "endLine": 18, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "705", "line": 19, "column": 3, "nodeType": null, "messageId": "698", "endLine": 19, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "706", "line": 20, "column": 3, "nodeType": null, "messageId": "698", "endLine": 20, "endColumn": 16}, {"ruleId": "696", "severity": 1, "message": "707", "line": 21, "column": 3, "nodeType": null, "messageId": "698", "endLine": 21, "endColumn": 8}, {"ruleId": "708", "severity": 2, "message": "709", "line": 77, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 77, "endColumn": 22, "suggestions": "712"}, {"ruleId": "713", "severity": 1, "message": "714", "line": 86, "column": 6, "nodeType": "715", "endLine": 86, "endColumn": 49, "suggestions": "716"}, {"ruleId": "696", "severity": 1, "message": "717", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "718", "line": 7, "column": 10, "nodeType": null, "messageId": "698", "endLine": 7, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "719", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "707", "line": 16, "column": 3, "nodeType": null, "messageId": "698", "endLine": 16, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "720", "line": 43, "column": 10, "nodeType": null, "messageId": "698", "endLine": 43, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "721", "line": 44, "column": 10, "nodeType": null, "messageId": "698", "endLine": 44, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "717", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "701", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "699", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "722", "line": 6, "column": 29, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 44}, {"ruleId": "696", "severity": 1, "message": "697", "line": 14, "column": 3, "nodeType": null, "messageId": "698", "endLine": 14, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "704", "line": 16, "column": 3, "nodeType": null, "messageId": "698", "endLine": 16, "endColumn": 11}, {"ruleId": "723", "severity": 2, "message": "724", "line": 113, "column": 19, "nodeType": "725", "messageId": "726", "suggestions": "727"}, {"ruleId": "696", "severity": 1, "message": "717", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "704", "line": 11, "column": 3, "nodeType": null, "messageId": "698", "endLine": 11, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "699", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "728", "line": 163, "column": 14, "nodeType": null, "messageId": "698", "endLine": 163, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "728", "line": 184, "column": 14, "nodeType": null, "messageId": "698", "endLine": 184, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "728", "line": 205, "column": 14, "nodeType": null, "messageId": "698", "endLine": 205, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "728", "line": 226, "column": 14, "nodeType": null, "messageId": "698", "endLine": 226, "endColumn": 19}, {"ruleId": "708", "severity": 2, "message": "709", "line": 459, "column": 115, "nodeType": "710", "messageId": "711", "endLine": 459, "endColumn": 118, "suggestions": "729"}, {"ruleId": "696", "severity": 1, "message": "699", "line": 7, "column": 10, "nodeType": null, "messageId": "698", "endLine": 7, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "730", "line": 7, "column": 17, "nodeType": null, "messageId": "698", "endLine": 7, "endColumn": 23}, {"ruleId": "708", "severity": 2, "message": "709", "line": 16, "column": 43, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 46, "suggestions": "731"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 1, "column": 10, "nodeType": null, "messageId": "698", "endLine": 1, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "733", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "733", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "734", "line": 4, "column": 10, "nodeType": null, "messageId": "698", "endLine": 4, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "736", "line": 12, "column": 24, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 32}, {"ruleId": "708", "severity": 2, "message": "709", "line": 42, "column": 16, "nodeType": "710", "messageId": "711", "endLine": 42, "endColumn": 19, "suggestions": "737"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 1, "column": 10, "nodeType": null, "messageId": "698", "endLine": 1, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "733", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "699", "line": 9, "column": 10, "nodeType": null, "messageId": "698", "endLine": 9, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "738", "line": 9, "column": 17, "nodeType": null, "messageId": "698", "endLine": 9, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "732", "line": 1, "column": 10, "nodeType": null, "messageId": "698", "endLine": 1, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "733", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "722", "line": 8, "column": 29, "nodeType": null, "messageId": "698", "endLine": 8, "endColumn": 44}, {"ruleId": "696", "severity": 1, "message": "739", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 7}, {"ruleId": "696", "severity": 1, "message": "697", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "740", "line": 16, "column": 3, "nodeType": null, "messageId": "698", "endLine": 16, "endColumn": 9}, {"ruleId": "708", "severity": 2, "message": "709", "line": 57, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 57, "endColumn": 22, "suggestions": "741"}, {"ruleId": "713", "severity": 1, "message": "742", "line": 66, "column": 6, "nodeType": "715", "endLine": 66, "endColumn": 35, "suggestions": "743"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 1, "column": 10, "nodeType": null, "messageId": "698", "endLine": 1, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "733", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 4, "column": 10, "nodeType": null, "messageId": "698", "endLine": 4, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "699", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "700", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 16}, {"ruleId": "696", "severity": 1, "message": "701", "line": 14, "column": 3, "nodeType": null, "messageId": "698", "endLine": 14, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "702", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "697", "line": 16, "column": 3, "nodeType": null, "messageId": "698", "endLine": 16, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "703", "line": 17, "column": 3, "nodeType": null, "messageId": "698", "endLine": 17, "endColumn": 12}, {"ruleId": "696", "severity": 1, "message": "704", "line": 18, "column": 3, "nodeType": null, "messageId": "698", "endLine": 18, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "705", "line": 19, "column": 3, "nodeType": null, "messageId": "698", "endLine": 19, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "706", "line": 20, "column": 3, "nodeType": null, "messageId": "698", "endLine": 20, "endColumn": 16}, {"ruleId": "696", "severity": 1, "message": "707", "line": 21, "column": 3, "nodeType": null, "messageId": "698", "endLine": 21, "endColumn": 8}, {"ruleId": "708", "severity": 2, "message": "709", "line": 86, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 86, "endColumn": 22, "suggestions": "744"}, {"ruleId": "713", "severity": 1, "message": "745", "line": 95, "column": 6, "nodeType": "715", "endLine": 95, "endColumn": 49, "suggestions": "746"}, {"ruleId": "696", "severity": 1, "message": "747", "line": 54, "column": 16, "nodeType": null, "messageId": "698", "endLine": 54, "endColumn": 19}, {"ruleId": "708", "severity": 2, "message": "709", "line": 74, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 74, "endColumn": 24, "suggestions": "748"}, {"ruleId": "696", "severity": 1, "message": "747", "line": 57, "column": 16, "nodeType": null, "messageId": "698", "endLine": 57, "endColumn": 19}, {"ruleId": "708", "severity": 2, "message": "709", "line": 77, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 77, "endColumn": 24, "suggestions": "749"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 56, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 56, "endColumn": 24, "suggestions": "750"}, {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "751", "line": 45, "column": 17, "nodeType": null, "messageId": "698", "endLine": 45, "endColumn": 24}, {"ruleId": "713", "severity": 1, "message": "752", "line": 56, "column": 6, "nodeType": "715", "endLine": 56, "endColumn": 37, "suggestions": "753"}, {"ruleId": "696", "severity": 1, "message": "722", "line": 5, "column": 29, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 44}, {"ruleId": "696", "severity": 1, "message": "754", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 7}, {"ruleId": "696", "severity": 1, "message": "700", "line": 11, "column": 3, "nodeType": null, "messageId": "698", "endLine": 11, "endColumn": 16}, {"ruleId": "696", "severity": 1, "message": "755", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 9}, {"ruleId": "696", "severity": 1, "message": "699", "line": 19, "column": 3, "nodeType": null, "messageId": "698", "endLine": 19, "endColumn": 8}, {"ruleId": "723", "severity": 2, "message": "724", "line": 120, "column": 17, "nodeType": "725", "messageId": "726", "suggestions": "756"}, {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "751", "line": 35, "column": 17, "nodeType": null, "messageId": "698", "endLine": 35, "endColumn": 24}, {"ruleId": "708", "severity": 2, "message": "709", "line": 63, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 63, "endColumn": 22, "suggestions": "757"}, {"ruleId": "713", "severity": 1, "message": "758", "line": 72, "column": 6, "nodeType": "715", "endLine": 72, "endColumn": 52, "suggestions": "759"}, {"ruleId": "696", "severity": 1, "message": "697", "line": 10, "column": 3, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "760", "line": 14, "column": 3, "nodeType": null, "messageId": "698", "endLine": 14, "endColumn": 7}, {"ruleId": "696", "severity": 1, "message": "751", "line": 47, "column": 17, "nodeType": null, "messageId": "698", "endLine": 47, "endColumn": 24}, {"ruleId": "713", "severity": 1, "message": "761", "line": 55, "column": 6, "nodeType": "715", "endLine": 55, "endColumn": 37, "suggestions": "762"}, {"ruleId": "696", "severity": 1, "message": "722", "line": 6, "column": 29, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 44}, {"ruleId": "696", "severity": 1, "message": "697", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "707", "line": 13, "column": 3, "nodeType": null, "messageId": "698", "endLine": 13, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "699", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 8}, {"ruleId": "696", "severity": 1, "message": "763", "line": 67, "column": 9, "nodeType": null, "messageId": "698", "endLine": 67, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "751", "line": 68, "column": 17, "nodeType": null, "messageId": "698", "endLine": 68, "endColumn": 24}, {"ruleId": "713", "severity": 1, "message": "764", "line": 77, "column": 6, "nodeType": "715", "endLine": 77, "endColumn": 14, "suggestions": "765"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 113, "column": 56, "nodeType": "710", "messageId": "711", "endLine": 113, "endColumn": 59, "suggestions": "766"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 114, "column": 54, "nodeType": "710", "messageId": "711", "endLine": 114, "endColumn": 57, "suggestions": "767"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 121, "column": 81, "nodeType": "710", "messageId": "711", "endLine": 121, "endColumn": 84, "suggestions": "768"}, {"ruleId": "696", "severity": 1, "message": "722", "line": 6, "column": 29, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 44}, {"ruleId": "696", "severity": 1, "message": "697", "line": 12, "column": 3, "nodeType": null, "messageId": "698", "endLine": 12, "endColumn": 11}, {"ruleId": "696", "severity": 1, "message": "751", "line": 72, "column": 17, "nodeType": null, "messageId": "698", "endLine": 72, "endColumn": 24}, {"ruleId": "713", "severity": 1, "message": "764", "line": 80, "column": 6, "nodeType": "715", "endLine": 80, "endColumn": 14, "suggestions": "769"}, {"ruleId": "770", "severity": 2, "message": "771", "line": 195, "column": 16, "nodeType": "772", "messageId": "773", "endLine": 195, "endColumn": 27}, {"ruleId": "696", "severity": 1, "message": "754", "line": 15, "column": 3, "nodeType": null, "messageId": "698", "endLine": 15, "endColumn": 7}, {"ruleId": "723", "severity": 2, "message": "724", "line": 111, "column": 17, "nodeType": "725", "messageId": "726", "suggestions": "774"}, {"ruleId": "723", "severity": 2, "message": "724", "line": 266, "column": 73, "nodeType": "725", "messageId": "726", "suggestions": "775"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "708", "severity": 2, "message": "709", "line": 19, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 19, "endColumn": 21, "suggestions": "776"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "777", "line": 30, "column": 11, "nodeType": null, "messageId": "698", "endLine": 30, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "778", "line": 6, "column": 27, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 34}, {"ruleId": "708", "severity": 2, "message": "709", "line": 19, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 19, "endColumn": 21, "suggestions": "779"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 19, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 19, "endColumn": 21, "suggestions": "780"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 32, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 32, "endColumn": 21, "suggestions": "781"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 7, "column": 58, "nodeType": "710", "messageId": "711", "endLine": 7, "endColumn": 61, "suggestions": "782"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 176, "column": 17, "nodeType": "710", "messageId": "711", "endLine": 176, "endColumn": 20, "suggestions": "783"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 184, "column": 43, "nodeType": "710", "messageId": "711", "endLine": 184, "endColumn": 46, "suggestions": "784"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 187, "column": 35, "nodeType": "710", "messageId": "711", "endLine": 187, "endColumn": 38, "suggestions": "785"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 27, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 27, "endColumn": 21, "suggestions": "786"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 140, "column": 15, "nodeType": "710", "messageId": "711", "endLine": 140, "endColumn": 18, "suggestions": "787"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 164, "column": 37, "nodeType": "710", "messageId": "711", "endLine": 164, "endColumn": 40, "suggestions": "788"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 204, "column": 40, "nodeType": "710", "messageId": "711", "endLine": 204, "endColumn": 43, "suggestions": "789"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 205, "column": 24, "nodeType": "710", "messageId": "711", "endLine": 205, "endColumn": 27, "suggestions": "790"}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 23}, {"ruleId": "696", "severity": 1, "message": "732", "line": 2, "column": 10, "nodeType": null, "messageId": "698", "endLine": 2, "endColumn": 26}, {"ruleId": "696", "severity": 1, "message": "734", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 21}, {"ruleId": "696", "severity": 1, "message": "735", "line": 6, "column": 10, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 23}, {"ruleId": "708", "severity": 2, "message": "709", "line": 155, "column": 27, "nodeType": "710", "messageId": "711", "endLine": 155, "endColumn": 30, "suggestions": "791"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 162, "column": 30, "nodeType": "710", "messageId": "711", "endLine": 162, "endColumn": 33, "suggestions": "792"}, {"ruleId": "696", "severity": 1, "message": "793", "line": 172, "column": 11, "nodeType": null, "messageId": "698", "endLine": 172, "endColumn": 22}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "708", "severity": 2, "message": "709", "line": 32, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 32, "endColumn": 21, "suggestions": "794"}, {"ruleId": "696", "severity": 1, "message": "777", "line": 38, "column": 11, "nodeType": null, "messageId": "698", "endLine": 38, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "735", "line": 5, "column": 10, "nodeType": null, "messageId": "698", "endLine": 5, "endColumn": 23}, {"ruleId": "708", "severity": 2, "message": "709", "line": 24, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 24, "endColumn": 21, "suggestions": "795"}, {"ruleId": "696", "severity": 1, "message": "778", "line": 6, "column": 27, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 34}, {"ruleId": "708", "severity": 2, "message": "709", "line": 29, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 29, "endColumn": 21, "suggestions": "796"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 28, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 28, "endColumn": 21, "suggestions": "797"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 36, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 36, "endColumn": 21, "suggestions": "798"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 257, "column": 38, "nodeType": "710", "messageId": "711", "endLine": 257, "endColumn": 41, "suggestions": "799"}, {"ruleId": "696", "severity": 1, "message": "778", "line": 6, "column": 27, "nodeType": null, "messageId": "698", "endLine": 6, "endColumn": 34}, {"ruleId": "708", "severity": 2, "message": "709", "line": 29, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 29, "endColumn": 21, "suggestions": "800"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 36, "column": 18, "nodeType": "710", "messageId": "711", "endLine": 36, "endColumn": 21, "suggestions": "801"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 16, "column": 40, "nodeType": "710", "messageId": "711", "endLine": 16, "endColumn": 43, "suggestions": "802"}, {"ruleId": "723", "severity": 2, "message": "724", "line": 22, "column": 20, "nodeType": "725", "messageId": "726", "suggestions": "803"}, {"ruleId": "723", "severity": 2, "message": "724", "line": 27, "column": 25, "nodeType": "725", "messageId": "726", "suggestions": "804"}, {"ruleId": "723", "severity": 2, "message": "724", "line": 27, "column": 99, "nodeType": "725", "messageId": "726", "suggestions": "805"}, {"ruleId": "696", "severity": 1, "message": "806", "line": 10, "column": 10, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 15}, {"ruleId": "708", "severity": 2, "message": "709", "line": 114, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 114, "endColumn": 22, "suggestions": "807"}, {"ruleId": "696", "severity": 1, "message": "808", "line": 7, "column": 10, "nodeType": null, "messageId": "698", "endLine": 7, "endColumn": 19}, {"ruleId": "708", "severity": 2, "message": "709", "line": 103, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 103, "endColumn": 22, "suggestions": "809"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 153, "column": 75, "nodeType": "710", "messageId": "711", "endLine": 153, "endColumn": 78, "suggestions": "810"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 12, "column": 15, "nodeType": "710", "messageId": "711", "endLine": 12, "endColumn": 18, "suggestions": "811"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 127, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 127, "endColumn": 22, "suggestions": "812"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 86, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 86, "endColumn": 24, "suggestions": "813"}, {"ruleId": "696", "severity": 1, "message": "814", "line": 8, "column": 29, "nodeType": null, "messageId": "698", "endLine": 8, "endColumn": 51}, {"ruleId": "696", "severity": 1, "message": "815", "line": 141, "column": 17, "nodeType": null, "messageId": "698", "endLine": 141, "endColumn": 18}, {"ruleId": "696", "severity": 1, "message": "816", "line": 9, "column": 3, "nodeType": null, "messageId": "698", "endLine": 9, "endColumn": 6}, {"ruleId": "696", "severity": 1, "message": "817", "line": 3, "column": 20, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 29}, {"ruleId": "696", "severity": 1, "message": "806", "line": 10, "column": 10, "nodeType": null, "messageId": "698", "endLine": 10, "endColumn": 15}, {"ruleId": "708", "severity": 2, "message": "709", "line": 13, "column": 13, "nodeType": "710", "messageId": "711", "endLine": 13, "endColumn": 16, "suggestions": "818"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 22, "column": 50, "nodeType": "710", "messageId": "711", "endLine": 22, "endColumn": 53, "suggestions": "819"}, {"ruleId": "708", "severity": 2, "message": "709", "line": 104, "column": 19, "nodeType": "710", "messageId": "711", "endLine": 104, "endColumn": 22, "suggestions": "820"}, {"ruleId": "696", "severity": 1, "message": "817", "line": 3, "column": 20, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 29}, {"ruleId": "696", "severity": 1, "message": "821", "line": 9, "column": 10, "nodeType": null, "messageId": "698", "endLine": 9, "endColumn": 15}, {"ruleId": "696", "severity": 1, "message": "822", "line": 9, "column": 17, "nodeType": null, "messageId": "698", "endLine": 9, "endColumn": 33}, {"ruleId": "708", "severity": 2, "message": "709", "line": 96, "column": 21, "nodeType": "710", "messageId": "711", "endLine": 96, "endColumn": 24, "suggestions": "823"}, {"ruleId": "696", "severity": 1, "message": "824", "line": 103, "column": 9, "nodeType": null, "messageId": "698", "endLine": 103, "endColumn": 19}, {"ruleId": "825", "severity": 2, "message": "826", "line": 4, "column": 18, "nodeType": "827", "messageId": "828", "endLine": 4, "endColumn": 28, "suggestions": "829"}, {"ruleId": "696", "severity": 1, "message": "817", "line": 3, "column": 10, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 19}, {"ruleId": "696", "severity": 1, "message": "830", "line": 3, "column": 21, "nodeType": null, "messageId": "698", "endLine": 3, "endColumn": 29}, {"ruleId": "696", "severity": 1, "message": "831", "line": 14, "column": 11, "nodeType": null, "messageId": "698", "endLine": 14, "endColumn": 16}, {"ruleId": "708", "severity": 2, "message": "709", "line": 101, "column": 10, "nodeType": "710", "messageId": "711", "endLine": 101, "endColumn": 13, "suggestions": "832"}, {"ruleId": "696", "severity": 1, "message": "833", "line": 47, "column": 48, "nodeType": null, "messageId": "698", "endLine": 47, "endColumn": 54}, {"ruleId": "696", "severity": 1, "message": "833", "line": 73, "column": 3, "nodeType": null, "messageId": "698", "endLine": 73, "endColumn": 9}, {"ruleId": "696", "severity": 1, "message": "834", "line": 120, "column": 28, "nodeType": null, "messageId": "698", "endLine": 120, "endColumn": 33}, {"ruleId": "696", "severity": 1, "message": "835", "line": 70, "column": 67, "nodeType": null, "messageId": "698", "endLine": 70, "endColumn": 76}, {"ruleId": "696", "severity": 1, "message": "836", "line": 70, "column": 87, "nodeType": null, "messageId": "698", "endLine": 70, "endColumn": 101}, {"ruleId": "696", "severity": 1, "message": "835", "line": 83, "column": 65, "nodeType": null, "messageId": "698", "endLine": 83, "endColumn": 74}, {"ruleId": "696", "severity": 1, "message": "837", "line": 83, "column": 85, "nodeType": null, "messageId": "698", "endLine": 83, "endColumn": 92}, {"ruleId": "696", "severity": 1, "message": "838", "line": 1, "column": 8, "nodeType": null, "messageId": "698", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'Calendar' is defined but never used.", "unusedVar", "'Users' is defined but never used.", "'GraduationCap' is defined but never used.", "'BookOpen' is defined but never used.", "'FileText' is defined but never used.", "'BarChart3' is defined but never used.", "'Settings' is defined but never used.", "'UserPlus' is defined but never used.", "'ClipboardList' is defined but never used.", "'Award' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["839", "840"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchClasses'. Either include it or remove the dependency array.", "ArrayExpression", ["841"], "'Input' is defined but never used.", "'Label' is defined but never used.", "'Clock' is defined but never used.", "'showAddTerm' is assigned a value but never used.", "'showAddExam' is assigned a value but never used.", "'CardDescription' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["842", "843", "844", "845"], "'error' is defined but never used.", ["846", "847"], "'Upload' is defined but never used.", ["848", "849"], "'getServerSession' is defined but never used.", "'redirect' is defined but never used.", "'authOptions' is defined but never used.", "'hasPermission' is defined but never used.", "'Download' is defined but never used.", ["850", "851"], "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'MapPin' is defined but never used.", ["852", "853"], "React Hook useEffect has a missing dependency: 'fetchSubjects'. Either include it or remove the dependency array.", ["854"], ["855", "856"], "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["857"], "'err' is defined but never used.", ["858", "859"], ["860", "861"], ["862", "863"], "'session' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMarks'. Either include it or remove the dependency array.", ["864"], "'Bell' is defined but never used.", "'Target' is defined but never used.", ["865", "866", "867", "868"], ["869", "870"], "React Hook useEffect has a missing dependency: 'fetchAttendance'. Either include it or remove the dependency array.", ["871"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["872"], "'router' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExamData'. Either include it or remove the dependency array.", ["873"], ["874", "875"], ["876", "877"], ["878", "879"], ["880"], "react/jsx-no-undef", "'CheckCircle' is not defined.", "JSXIdentifier", "undefined", ["881", "882", "883", "884"], ["885", "886", "887", "888"], ["889", "890"], "'isActive' is assigned a value but never used.", "'request' is defined but never used.", ["891", "892"], ["893", "894"], ["895", "896"], ["897", "898"], ["899", "900"], ["901", "902"], ["903", "904"], ["905", "906"], ["907", "908"], ["909", "910"], ["911", "912"], ["913", "914"], ["915", "916"], ["917", "918"], "'updatedUser' is assigned a value but never used.", ["919", "920"], ["921", "922"], ["923", "924"], ["925", "926"], ["927", "928"], ["929", "930"], ["931", "932"], ["933", "934"], ["935", "936"], ["937", "938", "939", "940"], ["941", "942", "943", "944"], ["945", "946", "947", "948"], "'Badge' is defined but never used.", ["949", "950"], "'checkAuth' is defined but never used.", ["951", "952"], ["953", "954"], ["955", "956"], ["957", "958"], ["959", "960"], "'formatValidationErrors' is defined but never used.", "'_' is defined but never used.", "'Eye' is defined but never used.", "'useEffect' is defined but never used.", ["961", "962"], ["963", "964"], ["965", "966"], "'Alert' is defined but never used.", "'AlertDescription' is defined but never used.", ["967", "968"], "'formatDate' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["969"], "'useState' is defined but never used.", "'theme' is assigned a value but never used.", ["970", "971"], "'config' is assigned a value but never used.", "'index' is defined but never used.", "'teacherId' is defined but never used.", "'studentClassId' is defined but never used.", "'classId' is defined but never used.", "'NextAuth' is defined but never used.", {"messageId": "972", "fix": "973", "desc": "974"}, {"messageId": "975", "fix": "976", "desc": "977"}, {"desc": "978", "fix": "979"}, {"messageId": "980", "data": "981", "fix": "982", "desc": "983"}, {"messageId": "980", "data": "984", "fix": "985", "desc": "986"}, {"messageId": "980", "data": "987", "fix": "988", "desc": "989"}, {"messageId": "980", "data": "990", "fix": "991", "desc": "992"}, {"messageId": "972", "fix": "993", "desc": "974"}, {"messageId": "975", "fix": "994", "desc": "977"}, {"messageId": "972", "fix": "995", "desc": "974"}, {"messageId": "975", "fix": "996", "desc": "977"}, {"messageId": "972", "fix": "997", "desc": "974"}, {"messageId": "975", "fix": "998", "desc": "977"}, {"messageId": "972", "fix": "999", "desc": "974"}, {"messageId": "975", "fix": "1000", "desc": "977"}, {"desc": "1001", "fix": "1002"}, {"messageId": "972", "fix": "1003", "desc": "974"}, {"messageId": "975", "fix": "1004", "desc": "977"}, {"desc": "1005", "fix": "1006"}, {"messageId": "972", "fix": "1007", "desc": "974"}, {"messageId": "975", "fix": "1008", "desc": "977"}, {"messageId": "972", "fix": "1009", "desc": "974"}, {"messageId": "975", "fix": "1010", "desc": "977"}, {"messageId": "972", "fix": "1011", "desc": "974"}, {"messageId": "975", "fix": "1012", "desc": "977"}, {"desc": "1013", "fix": "1014"}, {"messageId": "980", "data": "1015", "fix": "1016", "desc": "983"}, {"messageId": "980", "data": "1017", "fix": "1018", "desc": "986"}, {"messageId": "980", "data": "1019", "fix": "1020", "desc": "989"}, {"messageId": "980", "data": "1021", "fix": "1022", "desc": "992"}, {"messageId": "972", "fix": "1023", "desc": "974"}, {"messageId": "975", "fix": "1024", "desc": "977"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"messageId": "972", "fix": "1031", "desc": "974"}, {"messageId": "975", "fix": "1032", "desc": "977"}, {"messageId": "972", "fix": "1033", "desc": "974"}, {"messageId": "975", "fix": "1034", "desc": "977"}, {"messageId": "972", "fix": "1035", "desc": "974"}, {"messageId": "975", "fix": "1036", "desc": "977"}, {"desc": "1029", "fix": "1037"}, {"messageId": "980", "data": "1038", "fix": "1039", "desc": "983"}, {"messageId": "980", "data": "1040", "fix": "1041", "desc": "986"}, {"messageId": "980", "data": "1042", "fix": "1043", "desc": "989"}, {"messageId": "980", "data": "1044", "fix": "1045", "desc": "992"}, {"messageId": "980", "data": "1046", "fix": "1047", "desc": "983"}, {"messageId": "980", "data": "1048", "fix": "1049", "desc": "986"}, {"messageId": "980", "data": "1050", "fix": "1051", "desc": "989"}, {"messageId": "980", "data": "1052", "fix": "1053", "desc": "992"}, {"messageId": "972", "fix": "1054", "desc": "974"}, {"messageId": "975", "fix": "1055", "desc": "977"}, {"messageId": "972", "fix": "1056", "desc": "974"}, {"messageId": "975", "fix": "1057", "desc": "977"}, {"messageId": "972", "fix": "1058", "desc": "974"}, {"messageId": "975", "fix": "1059", "desc": "977"}, {"messageId": "972", "fix": "1060", "desc": "974"}, {"messageId": "975", "fix": "1061", "desc": "977"}, {"messageId": "972", "fix": "1062", "desc": "974"}, {"messageId": "975", "fix": "1063", "desc": "977"}, {"messageId": "972", "fix": "1064", "desc": "974"}, {"messageId": "975", "fix": "1065", "desc": "977"}, {"messageId": "972", "fix": "1066", "desc": "974"}, {"messageId": "975", "fix": "1067", "desc": "977"}, {"messageId": "972", "fix": "1068", "desc": "974"}, {"messageId": "975", "fix": "1069", "desc": "977"}, {"messageId": "972", "fix": "1070", "desc": "974"}, {"messageId": "975", "fix": "1071", "desc": "977"}, {"messageId": "972", "fix": "1072", "desc": "974"}, {"messageId": "975", "fix": "1073", "desc": "977"}, {"messageId": "972", "fix": "1074", "desc": "974"}, {"messageId": "975", "fix": "1075", "desc": "977"}, {"messageId": "972", "fix": "1076", "desc": "974"}, {"messageId": "975", "fix": "1077", "desc": "977"}, {"messageId": "972", "fix": "1078", "desc": "974"}, {"messageId": "975", "fix": "1079", "desc": "977"}, {"messageId": "972", "fix": "1080", "desc": "974"}, {"messageId": "975", "fix": "1081", "desc": "977"}, {"messageId": "972", "fix": "1082", "desc": "974"}, {"messageId": "975", "fix": "1083", "desc": "977"}, {"messageId": "972", "fix": "1084", "desc": "974"}, {"messageId": "975", "fix": "1085", "desc": "977"}, {"messageId": "972", "fix": "1086", "desc": "974"}, {"messageId": "975", "fix": "1087", "desc": "977"}, {"messageId": "972", "fix": "1088", "desc": "974"}, {"messageId": "975", "fix": "1089", "desc": "977"}, {"messageId": "972", "fix": "1090", "desc": "974"}, {"messageId": "975", "fix": "1091", "desc": "977"}, {"messageId": "972", "fix": "1092", "desc": "974"}, {"messageId": "975", "fix": "1093", "desc": "977"}, {"messageId": "972", "fix": "1094", "desc": "974"}, {"messageId": "975", "fix": "1095", "desc": "977"}, {"messageId": "972", "fix": "1096", "desc": "974"}, {"messageId": "975", "fix": "1097", "desc": "977"}, {"messageId": "972", "fix": "1098", "desc": "974"}, {"messageId": "975", "fix": "1099", "desc": "977"}, {"messageId": "972", "fix": "1100", "desc": "974"}, {"messageId": "975", "fix": "1101", "desc": "977"}, {"messageId": "980", "data": "1102", "fix": "1103", "desc": "983"}, {"messageId": "980", "data": "1104", "fix": "1105", "desc": "986"}, {"messageId": "980", "data": "1106", "fix": "1107", "desc": "989"}, {"messageId": "980", "data": "1108", "fix": "1109", "desc": "992"}, {"messageId": "980", "data": "1110", "fix": "1111", "desc": "983"}, {"messageId": "980", "data": "1112", "fix": "1113", "desc": "986"}, {"messageId": "980", "data": "1114", "fix": "1115", "desc": "989"}, {"messageId": "980", "data": "1116", "fix": "1117", "desc": "992"}, {"messageId": "980", "data": "1118", "fix": "1119", "desc": "983"}, {"messageId": "980", "data": "1120", "fix": "1121", "desc": "986"}, {"messageId": "980", "data": "1122", "fix": "1123", "desc": "989"}, {"messageId": "980", "data": "1124", "fix": "1125", "desc": "992"}, {"messageId": "972", "fix": "1126", "desc": "974"}, {"messageId": "975", "fix": "1127", "desc": "977"}, {"messageId": "972", "fix": "1128", "desc": "974"}, {"messageId": "975", "fix": "1129", "desc": "977"}, {"messageId": "972", "fix": "1130", "desc": "974"}, {"messageId": "975", "fix": "1131", "desc": "977"}, {"messageId": "972", "fix": "1132", "desc": "974"}, {"messageId": "975", "fix": "1133", "desc": "977"}, {"messageId": "972", "fix": "1134", "desc": "974"}, {"messageId": "975", "fix": "1135", "desc": "977"}, {"messageId": "972", "fix": "1136", "desc": "974"}, {"messageId": "975", "fix": "1137", "desc": "977"}, {"messageId": "972", "fix": "1138", "desc": "974"}, {"messageId": "975", "fix": "1139", "desc": "977"}, {"messageId": "972", "fix": "1140", "desc": "974"}, {"messageId": "975", "fix": "1141", "desc": "977"}, {"messageId": "972", "fix": "1142", "desc": "974"}, {"messageId": "975", "fix": "1143", "desc": "977"}, {"messageId": "972", "fix": "1144", "desc": "974"}, {"messageId": "975", "fix": "1145", "desc": "977"}, {"messageId": "1146", "fix": "1147", "desc": "1148"}, {"messageId": "972", "fix": "1149", "desc": "974"}, {"messageId": "975", "fix": "1150", "desc": "977"}, "suggestUnknown", {"range": "1151", "text": "1152"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1153", "text": "1154"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchClasses]", {"range": "1155", "text": "1156"}, "replaceWithAlt", {"alt": "1157"}, {"range": "1158", "text": "1159"}, "Replace with `&apos;`.", {"alt": "1160"}, {"range": "1161", "text": "1162"}, "Replace with `&lsquo;`.", {"alt": "1163"}, {"range": "1164", "text": "1165"}, "Replace with `&#39;`.", {"alt": "1166"}, {"range": "1167", "text": "1168"}, "Replace with `&rsquo;`.", {"range": "1169", "text": "1152"}, {"range": "1170", "text": "1154"}, {"range": "1171", "text": "1152"}, {"range": "1172", "text": "1154"}, {"range": "1173", "text": "1152"}, {"range": "1174", "text": "1154"}, {"range": "1175", "text": "1152"}, {"range": "1176", "text": "1154"}, "Update the dependencies array to be: [fetchSubjects, pagination.page, searchTerm]", {"range": "1177", "text": "1178"}, {"range": "1179", "text": "1152"}, {"range": "1180", "text": "1154"}, "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchTeachers]", {"range": "1181", "text": "1182"}, {"range": "1183", "text": "1152"}, {"range": "1184", "text": "1154"}, {"range": "1185", "text": "1152"}, {"range": "1186", "text": "1154"}, {"range": "1187", "text": "1152"}, {"range": "1188", "text": "1154"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchMarks]", {"range": "1189", "text": "1190"}, {"alt": "1157"}, {"range": "1191", "text": "1192"}, {"alt": "1160"}, {"range": "1193", "text": "1194"}, {"alt": "1163"}, {"range": "1195", "text": "1196"}, {"alt": "1166"}, {"range": "1197", "text": "1198"}, {"range": "1199", "text": "1152"}, {"range": "1200", "text": "1154"}, "Update the dependencies array to be: [fetchAttendance, pagination.page, selectedClass, selectedDate]", {"range": "1201", "text": "1202"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchExams]", {"range": "1203", "text": "1204"}, "Update the dependencies array to be: [examId, fetchExamData]", {"range": "1205", "text": "1206"}, {"range": "1207", "text": "1152"}, {"range": "1208", "text": "1154"}, {"range": "1209", "text": "1152"}, {"range": "1210", "text": "1154"}, {"range": "1211", "text": "1152"}, {"range": "1212", "text": "1154"}, {"range": "1213", "text": "1206"}, {"alt": "1157"}, {"range": "1214", "text": "1215"}, {"alt": "1160"}, {"range": "1216", "text": "1217"}, {"alt": "1163"}, {"range": "1218", "text": "1219"}, {"alt": "1166"}, {"range": "1220", "text": "1221"}, {"alt": "1157"}, {"range": "1222", "text": "1223"}, {"alt": "1160"}, {"range": "1224", "text": "1225"}, {"alt": "1163"}, {"range": "1226", "text": "1227"}, {"alt": "1166"}, {"range": "1228", "text": "1229"}, {"range": "1230", "text": "1152"}, {"range": "1231", "text": "1154"}, {"range": "1232", "text": "1152"}, {"range": "1233", "text": "1154"}, {"range": "1234", "text": "1152"}, {"range": "1235", "text": "1154"}, {"range": "1236", "text": "1152"}, {"range": "1237", "text": "1154"}, {"range": "1238", "text": "1152"}, {"range": "1239", "text": "1154"}, {"range": "1240", "text": "1152"}, {"range": "1241", "text": "1154"}, {"range": "1242", "text": "1152"}, {"range": "1243", "text": "1154"}, {"range": "1244", "text": "1152"}, {"range": "1245", "text": "1154"}, {"range": "1246", "text": "1152"}, {"range": "1247", "text": "1154"}, {"range": "1248", "text": "1152"}, {"range": "1249", "text": "1154"}, {"range": "1250", "text": "1152"}, {"range": "1251", "text": "1154"}, {"range": "1252", "text": "1152"}, {"range": "1253", "text": "1154"}, {"range": "1254", "text": "1152"}, {"range": "1255", "text": "1154"}, {"range": "1256", "text": "1152"}, {"range": "1257", "text": "1154"}, {"range": "1258", "text": "1152"}, {"range": "1259", "text": "1154"}, {"range": "1260", "text": "1152"}, {"range": "1261", "text": "1154"}, {"range": "1262", "text": "1152"}, {"range": "1263", "text": "1154"}, {"range": "1264", "text": "1152"}, {"range": "1265", "text": "1154"}, {"range": "1266", "text": "1152"}, {"range": "1267", "text": "1154"}, {"range": "1268", "text": "1152"}, {"range": "1269", "text": "1154"}, {"range": "1270", "text": "1152"}, {"range": "1271", "text": "1154"}, {"range": "1272", "text": "1152"}, {"range": "1273", "text": "1154"}, {"range": "1274", "text": "1152"}, {"range": "1275", "text": "1154"}, {"range": "1276", "text": "1152"}, {"range": "1277", "text": "1154"}, {"alt": "1157"}, {"range": "1278", "text": "1279"}, {"alt": "1160"}, {"range": "1280", "text": "1281"}, {"alt": "1163"}, {"range": "1282", "text": "1283"}, {"alt": "1166"}, {"range": "1284", "text": "1285"}, {"alt": "1157"}, {"range": "1286", "text": "1287"}, {"alt": "1160"}, {"range": "1288", "text": "1289"}, {"alt": "1163"}, {"range": "1290", "text": "1291"}, {"alt": "1166"}, {"range": "1292", "text": "1293"}, {"alt": "1157"}, {"range": "1294", "text": "1295"}, {"alt": "1160"}, {"range": "1296", "text": "1297"}, {"alt": "1163"}, {"range": "1298", "text": "1299"}, {"alt": "1166"}, {"range": "1300", "text": "1301"}, {"range": "1302", "text": "1152"}, {"range": "1303", "text": "1154"}, {"range": "1304", "text": "1152"}, {"range": "1305", "text": "1154"}, {"range": "1306", "text": "1152"}, {"range": "1307", "text": "1154"}, {"range": "1308", "text": "1152"}, {"range": "1309", "text": "1154"}, {"range": "1310", "text": "1152"}, {"range": "1311", "text": "1154"}, {"range": "1312", "text": "1152"}, {"range": "1313", "text": "1154"}, {"range": "1314", "text": "1152"}, {"range": "1315", "text": "1154"}, {"range": "1316", "text": "1152"}, {"range": "1317", "text": "1154"}, {"range": "1318", "text": "1152"}, {"range": "1319", "text": "1154"}, {"range": "1320", "text": "1152"}, {"range": "1321", "text": "1154"}, "replaceEmptyInterfaceWithSuper", {"range": "1322", "text": "1323"}, "Replace empty interface with a type alias.", {"range": "1324", "text": "1152"}, {"range": "1325", "text": "1154"}, [2001, 2004], "unknown", [2001, 2004], "never", [2135, 2178], "[pagination.page, searchTerm, filterActive, fetchClasses]", "&apos;", [3042, 3122], "\n              Here&apos;s an overview of your school management system.\n            ", "&lsquo;", [3042, 3122], "\n              Here&lsquo;s an overview of your school management system.\n            ", "&#39;", [3042, 3122], "\n              Here&#39;s an overview of your school management system.\n            ", "&rsquo;", [3042, 3122], "\n              Here&rsquo;s an overview of your school management system.\n            ", [17422, 17425], [17422, 17425], [572, 575], [572, 575], [1346, 1349], [1346, 1349], [1635, 1638], [1635, 1638], [1770, 1799], "[fetchSubjects, pagination.page, searchTerm]", [2181, 2184], [2181, 2184], [2316, 2359], "[pagination.page, searchTerm, filterActive, fetchTeachers]", [1915, 1918], [1915, 1918], [2028, 2031], [2028, 2031], [1420, 1423], [1420, 1423], [1366, 1397], "[selectedTerm, selectedSubject, fetchMarks]", [3136, 3213], "\r\n            Here&apos;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&lsquo;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&#39;s your academic overview and progress summary.\r\n          ", [3136, 3213], "\r\n            Here&rsquo;s your academic overview and progress summary.\r\n          ", [1913, 1916], [1913, 1916], [2059, 2105], "[fetchAttendance, pagination.page, selectedClass, selectedDate]", [1137, 1168], "[selectedTerm, selectedSubject, fetchExams]", [1610, 1618], "[examId, fetchExamData]", [2596, 2599], [2596, 2599], [2675, 2678], [2675, 2678], [3030, 3033], [3030, 3033], [1618, 1626], [2935, 3019], "\r\n            Here&apos;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&lsquo;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&#39;s your teaching overview and quick actions for today.\r\n          ", [2935, 3019], "\r\n            Here&rsquo;s your teaching overview and quick actions for today.\r\n          ", [9961, 9977], "Today&apos;s Schedule", [9961, 9977], "Today&lsquo;s Schedule", [9961, 9977], "Today&#39;s Schedule", [9961, 9977], "Today&rsquo;s Schedule", [653, 656], [653, 656], [663, 666], [663, 666], [657, 660], [657, 660], [1174, 1177], [1174, 1177], [322, 325], [322, 325], [7537, 7540], [7537, 7540], [7844, 7847], [7844, 7847], [7934, 7937], [7934, 7937], [965, 968], [965, 968], [4091, 4094], [4091, 4094], [4919, 4922], [4919, 4922], [6025, 6028], [6025, 6028], [6082, 6085], [6082, 6085], [4765, 4768], [4765, 4768], [5169, 5172], [5169, 5172], [1172, 1175], [1172, 1175], [931, 934], [931, 934], [900, 903], [900, 903], [847, 850], [847, 850], [1380, 1383], [1380, 1383], [7663, 7666], [7663, 7666], [872, 875], [872, 875], [1053, 1056], [1053, 1056], [569, 572], [569, 572], [836, 907], "\r\n            You don&apos;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&lsquo;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&#39;t have permission to access this page\r\n          ", [836, 907], "\r\n            You don&rsquo;t have permission to access this page\r\n          ", [1034, 1154], "\r\n            The page you&apos;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&lsquo;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&#39;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you&rsquo;re trying to access requires specific permissions that your account doesn't have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&apos;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&lsquo;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&#39;t have.\r\n          ", [1034, 1154], "\r\n            The page you're trying to access requires specific permissions that your account doesn&rsquo;t have.\r\n          ", [3342, 3345], [3342, 3345], [2649, 2652], [2649, 2652], [3844, 3847], [3844, 3847], [447, 450], [447, 450], [3509, 3512], [3509, 3512], [2126, 2129], [2126, 2129], [495, 498], [495, 498], [830, 833], [830, 833], [3217, 3220], [3217, 3220], [2298, 2301], [2298, 2301], [75, 153], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [2431, 2434], [2431, 2434]]