(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>i,"CardDescription",()=>n,"CardHeader",()=>d,"CardTitle",()=>l]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...d})});s.displayName="Card";let d=a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...d})});d.displayName="CardHeader";let l=a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...d})});l.displayName="CardTitle";let n=a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",s),...d})});n.displayName="CardDescription";let i=a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",s),...d})});i.displayName="CardContent",a.forwardRef((e,a)=>{let{className:s,...d}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",s),...d})}).displayName="CardFooter"},70307,e=>{"use strict";e.s(["Alert",()=>l,"AlertDescription",()=>n]);var t=e.i(53379),a=e.i(46686),r=e.i(94323),s=e.i(36946);let d=(0,r.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),l=a.forwardRef((e,a)=>{let{className:r,variant:l,...n}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,s.cn)(d({variant:l}),r),...n})});l.displayName="Alert",a.forwardRef((e,a)=>{let{className:r,...d}=e;return(0,t.jsx)("h5",{ref:a,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",r),...d})}).displayName="AlertTitle";let n=a.forwardRef((e,a)=>{let{className:r,...d}=e;return(0,t.jsx)("div",{ref:a,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",r),...d})});n.displayName="AlertDescription"},90285,e=>{"use strict";e.s(["Badge",()=>d]);var t=e.i(53379),a=e.i(94323),r=e.i(36946);let s=(0,a.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:d,...l}=e;return(0,t.jsx)("div",{className:(0,r.cn)(s({variant:d}),a),...l})}},64323,e=>{"use strict";e.s(["default",()=>l]);var t=e.i(53379),a=e.i(46686),r=e.i(90285),s=e.i(32668),d=e.i(70307);function l(){let[e,l]=(0,a.useState)([]),[n,i]=(0,a.useState)({total:0,present:0,absent:0,late:0,halfDay:0,percentage:0}),[c,x]=(0,a.useState)(!0),[o,m]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{try{x(!0);let e=await fetch("/api/student/attendance");if(!e.ok)throw Error("Failed to fetch attendance");let t=await e.json();l(t.attendanceRecords),i(t.statistics)}catch(e){m(e.message)}finally{x(!1)}})()},[]);let g=e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},h=e=>new Date(e).toLocaleDateString();return c?(0,t.jsx)("div",{className:"flex justify-center p-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold",children:"My Attendance"}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"View your attendance records and statistics."})]}),o&&(0,t.jsx)(d.Alert,{variant:"destructive",children:(0,t.jsx)(d.AlertDescription,{children:o})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4",children:[(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{className:"pb-2",children:(0,t.jsx)(s.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Total Days"})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold",children:n.total})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{className:"pb-2",children:(0,t.jsx)(s.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Present"})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600",children:n.present})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{className:"pb-2",children:(0,t.jsx)(s.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Absent"})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-red-600",children:n.absent})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{className:"pb-2",children:(0,t.jsx)(s.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Late"})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-yellow-600",children:n.late})})]}),(0,t.jsxs)(s.Card,{className:"col-span-2 sm:col-span-1",children:[(0,t.jsx)(s.CardHeader,{className:"pb-2",children:(0,t.jsx)(s.CardTitle,{className:"text-xs sm:text-sm font-medium",children:"Attendance %"})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"text-xl sm:text-2xl font-bold text-blue-600",children:[n.percentage,"%"]})})]})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsx)(s.CardHeader,{children:(0,t.jsx)(s.CardTitle,{children:"Recent Attendance Records"})}),(0,t.jsx)(s.CardContent,{children:0===e.length?(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No attendance records found."})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Class"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Status"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Remarks"})]})}),(0,t.jsx)("tbody",{children:e.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("div",{className:"font-medium",children:h(e.date)})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)(r.Badge,{variant:"secondary",children:[e.class.name," ",e.class.section.name]})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(r.Badge,{className:g(e.status),children:e.status})}),(0,t.jsx)("td",{className:"p-2",children:e.remarks||"-"})]},e.id))})]})}),(0,t.jsx)("div",{className:"md:hidden space-y-4",children:e.map(e=>(0,t.jsx)(s.Card,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:h(e.date)}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsxs)(r.Badge,{variant:"secondary",className:"text-xs",children:[e.class.name," ",e.class.section.name]})})]}),(0,t.jsx)("div",{className:"ml-4",children:(0,t.jsx)(r.Badge,{className:g(e.status),children:e.status})})]}),e.remarks&&(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Remarks:"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e.remarks})]})]})},e.id))})]})})]})]})}}]);