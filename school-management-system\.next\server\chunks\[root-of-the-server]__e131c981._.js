module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},80467,(e,t,r)=>{},16691,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>N,"routeModule",()=>E,"serverHooks",()=>C,"workAsyncStorage",()=>S,"workUnitAsyncStorage",()=>A],16691);var t=e.i(6137),r=e.i(11365),a=e.i(9638),s=e.i(15243),n=e.i(66378),o=e.i(92101),i=e.i(50012),l=e.i(62885),p=e.i(31409),d=e.i(78448),c=e.i(28015),u=e.i(72721),h=e.i(75714),m=e.i(12634),y=e.i(93695);e.i(74732);var x=e.i(66662);e.s(["GET",()=>R,"POST",()=>b,"buildSettingsUpdates",()=>w],71604);var g=e.i(2835),v=e.i(58356),_=e.i(43382),f=e.i(31279);function w(e,t){let r=[];switch(e){case"general":r.push({key:"school_name",value:t.name},{key:"school_address",value:t.address},{key:"school_phone",value:t.phone},{key:"school_email",value:t.email},{key:"school_website",value:t.website},{key:"school_principal",value:t.principal},{key:"school_established_year",value:t.establishedYear});break;case"academic":r.push({key:"academic_year",value:t.academicYear},{key:"current_term",value:t.currentTerm},{key:"grading_system",value:t.gradingSystem},{key:"pass_percentage",value:t.passPercentage?.toString()},{key:"max_attendance_percentage",value:t.maxAttendancePercentage?.toString()});break;case"notifications":r.push({key:"attendance_alerts",value:t.attendanceAlerts?.toString()},{key:"exam_results",value:t.examResults?.toString()},{key:"report_card_generation",value:t.reportCardGeneration?.toString()},{key:"system_updates",value:t.systemUpdates?.toString()});break;case"security":r.push({key:"session_timeout",value:t.sessionTimeout?.toString()},{key:"password_policy",value:t.passwordPolicy},{key:"two_factor_auth",value:t.twoFactorAuth?.toString()},{key:"login_attempts",value:t.loginAttempts?.toString()});break;default:throw Error("Invalid settings type")}for(let e of r)if(void 0===e.value)throw Error(`Missing value for setting key: ${e.key}`);return r}async function R(e){try{let t=await (0,v.getServerSession)(_.authOptions);if(!t||"ADMIN"!==t.user.role)return g.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("type"),s=(await f.prisma.setting.findMany()).reduce((e,t)=>(e[t.key]=t.value,e),{});switch(a){case"general":return g.NextResponse.json({schoolName:s.school_name||"Advance School",address:s.school_address||"123 Education Street, City, State 12345",phone:s.school_phone||"+****************",email:s.school_email||"<EMAIL>",website:s.school_website||"www.advanceschool.edu",principal:s.school_principal||"Dr. John Smith",establishedYear:s.school_established_year||"1995"});case"academic":return g.NextResponse.json({academicYear:s.academic_year||"2024-2025",currentTerm:s.current_term||"Term 1",gradingSystem:s.grading_system||"LETTER",passPercentage:parseInt(s.pass_percentage)||40,maxAttendancePercentage:parseInt(s.max_attendance_percentage)||75});case"notifications":return g.NextResponse.json({attendanceAlerts:"true"===s.attendance_alerts,examResults:"true"===s.exam_results,reportCardGeneration:"true"===s.report_card_generation,systemUpdates:"true"===s.system_updates});case"security":return g.NextResponse.json({sessionTimeout:parseInt(s.session_timeout)||30,passwordPolicy:s.password_policy||"strong",twoFactorAuth:"true"===s.two_factor_auth,loginAttempts:"true"===s.login_attempts});default:return g.NextResponse.json({general:{schoolName:s.school_name||"Advance School",address:s.school_address||"123 Education Street, City, State 12345",phone:s.school_phone||"+****************",email:s.school_email||"<EMAIL>",website:s.school_website||"www.advanceschool.edu",principal:s.school_principal||"Dr. John Smith",establishedYear:s.school_established_year||"1995"},academic:{academicYear:s.academic_year||"2024-2025",currentTerm:s.current_term||"Term 1",gradingSystem:s.grading_system||"LETTER",passPercentage:parseInt(s.pass_percentage)||40,maxAttendancePercentage:parseInt(s.max_attendance_percentage)||75},notifications:{attendanceAlerts:"true"===s.attendance_alerts,examResults:"true"===s.exam_results,reportCardGeneration:"true"===s.report_card_generation,systemUpdates:"true"===s.system_updates},security:{sessionTimeout:parseInt(s.session_timeout)||30,passwordPolicy:s.password_policy||"strong",twoFactorAuth:"true"===s.two_factor_auth,loginAttempts:"true"===s.login_attempts}})}}catch(e){return console.error("Error fetching settings:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}async function b(e){try{let t,r=await (0,v.getServerSession)(_.authOptions);if(!r||"ADMIN"!==r.user.role)return g.NextResponse.json({error:"Unauthorized"},{status:401});let{type:a,data:s}=await e.json();try{t=w(a,s)}catch(e){return g.NextResponse.json({error:e.message||"Invalid payload"},{status:400})}for(let e of t)await f.prisma.setting.upsert({where:{key:e.key},update:{value:e.value},create:{key:e.key,value:e.value}});return await f.prisma.auditLog.create({data:{action:"SETTINGS_UPDATE",entity:"SETTING",entityId:a,userId:r.user.id,meta:{details:`Updated ${a} settings`,ipAddress:e.headers.get("x-forwarded-for")||"unknown"}}}),g.NextResponse.json({message:`${a} settings updated successfully`,type:a,data:s})}catch(e){return console.error("Error updating settings:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}var k=e.i(71604);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/settings/route.ts",nextConfigOutput:"",userland:k}),{workAsyncStorage:S,workUnitAsyncStorage:A,serverHooks:C}=E;function N(){return(0,a.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:A})}async function T(e,t,a){var g;let v="/api/admin/settings/route";v=v.replace(/\/index$/,"")||"/";let _=await E.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!_)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:f,params:w,nextConfig:R,isDraftMode:b,prerenderManifest:k,routerServerContext:S,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:N}=_,T=(0,o.normalizeAppPath)(v),j=!!(k.dynamicRoutes[T]||k.routes[N]);if(j&&!b){let e=!!k.routes[N],t=k.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new y.NoFallbackError}let q=null;!j||E.isDev||b||(q="/index"===(q=N)?"/":q);let P=!0===E.isDev||!j,I=j&&!P,O=e.method||"GET",U=(0,n.getTracer)(),D=U.getActiveScopeSpan(),M={params:w,prerenderManifest:k,renderOpts:{experimental:{cacheComponents:!!R.experimental.cacheComponents,authInterrupts:!!R.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=R.experimental)?void 0:g.cacheLife,isRevalidate:I,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,S)},sharedContext:{buildId:f}},H=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let o=async r=>E.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${O} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),i=async n=>{var i,l;let p=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&A&&C&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(n);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let p=M.renderOpts.collectedTags;if(!j)return await (0,c.sendResponse)(H,F,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,u.toNodeOutgoingHttpHeaders)(i.headers);p&&(t[m.NEXT_CACHE_TAGS_HEADER]=p),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:A})},S),t}},y=await E.handleResponse({req:e,nextConfig:R,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:p,waitUntil:a.waitUntil});if(!j)return null;if((null==y||null==(i=y.value)?void 0:i.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==y||null==(l=y.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":y.isMiss?"MISS":y.isStale?"STALE":"HIT"),b&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,u.fromNodeOutgoingHttpHeaders)(y.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&j||g.delete(m.NEXT_CACHE_TAGS_HEADER),!y.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,h.getCacheControlHeader)(y.cacheControl)),await (0,c.sendResponse)(H,F,new Response(y.value.body,{headers:g,status:y.value.status||200})),null};D?await i(D):await U.withPropagatedContext(e.headers,()=>U.trace(p.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},i))}catch(t){if(D||t instanceof y.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:A})}),j)throw t;return await (0,c.sendResponse)(H,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__e131c981._.js.map