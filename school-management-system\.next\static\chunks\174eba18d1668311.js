(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,r,t)=>{r.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>i,"CardHeader",()=>l,"CardTitle",()=>n]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let s=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...l})});s.displayName="Card";let l=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});l.displayName="CardHeader";let n=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});n.displayName="CardTitle";let i=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...l})});i.displayName="CardDescription";let d=t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...l})});d.displayName="CardContent",t.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>p],30151);var r=e.i(53379),t=e.i(46686);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,s=r.map(r=>{let s=a(r,e);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let e=0;e<s.length;e++){let t=s[e];"function"==typeof t?t():a(r[e],null)}}}}function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t.useCallback(s(...r),r)}function n(e){let a=function(e){let r=t.forwardRef((e,r)=>{let{children:a,...l}=e;if(t.isValidElement(a)){var n,i,d;let e,o,c=(o=(e=null==(i=Object.getOwnPropertyDescriptor((n=a).props,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=null==(d=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref,u=function(e,r){let t={...r};for(let a in r){let s=e[a],l=r[a];/^on[A-Z]/.test(a)?s&&l?t[a]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let a=l(...r);return s(...r),a}:s&&(t[a]=s):"style"===a?t[a]={...s,...l}:"className"===a&&(t[a]=[s,l].filter(Boolean).join(" "))}return{...e,...t}}(l,a.props);return a.type!==t.Fragment&&(u.ref=r?s(r,c):c),t.cloneElement(a,u)}return t.Children.count(a)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),l=t.forwardRef((e,s)=>{let{children:l,...n}=e,i=t.Children.toArray(l),d=i.find(o);if(d){let e=d.props.children,l=i.map(r=>r!==d?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(a,{...n,ref:s,children:t.isValidElement(e)?t.cloneElement(e,void 0,l):null})}return(0,r.jsx)(a,{...n,ref:s,children:l})});return l.displayName="".concat(e,".Slot"),l}e.s(["Slot",()=>i,"createSlot",()=>n],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>l],35952);var i=n("Slot"),d=Symbol("radix.slottable");function o(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var c=e.i(94323),u=e.i(36946);let m=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=t.forwardRef((e,t)=>{let{className:a,variant:s,size:l,asChild:n=!1,...d}=e;return(0,r.jsx)(n?i:"button",{className:(0,u.cn)(m({variant:s,size:l,className:a})),ref:t,...d})});p.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>l,"dispatchDiscreteCustomEvent",()=>n]);var r=e.i(46686),t=e.i(50321),a=e.i(88338),s=e.i(53379),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let l=(0,a.createSlot)("Primitive.".concat(t)),n=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?l:t,{...n,ref:r})});return n.displayName="Primitive.".concat(t),{...e,[t]:n}},{});function n(e,r){e&&t.flushSync(()=>e.dispatchEvent(r))}},80873,e=>{"use strict";e.s(["User",()=>r],80873);let r=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4741,e=>{"use strict";e.s(["default",()=>n],4741);var r=e.i(46686);let t=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:l="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:l,strokeWidth:d?24*Number(i)/Number(n):i,className:a("lucide",o),...!c&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),n=(e,s)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:d,...o}=n;return(0,r.createElement)(l,{ref:i,iconNode:s,className:a("lucide-".concat(t(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...o})});return n.displayName=t(e),n}},18498,e=>{"use strict";e.s(["Home",()=>r],18498);let r=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},92521,e=>{"use strict";e.s(["Label",()=>d],92521);var r=e.i(53379),t=e.i(46686),a=e.i(62521),s=t.forwardRef((e,t)=>(0,r.jsx)(a.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));s.displayName="Label";var l=e.i(94323),n=e.i(36946);let i=(0,l.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(s,{ref:t,className:(0,n.cn)(i(),a),...l})});d.displayName=s.displayName},96487,e=>{"use strict";e.s(["Input",()=>s]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let s=t.forwardRef((e,t)=>{let{className:s,type:l,...n}=e;return(0,r.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});s.displayName="Input"},70307,e=>{"use strict";e.s(["Alert",()=>n,"AlertDescription",()=>i]);var r=e.i(53379),t=e.i(46686),a=e.i(94323),s=e.i(36946);let l=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),n=t.forwardRef((e,t)=>{let{className:a,variant:n,...i}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(l({variant:n}),a),...i})});n.displayName="Alert",t.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",a),...l})}).displayName="AlertTitle";let i=t.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",a),...l})});i.displayName="AlertDescription"},44670,e=>{"use strict";e.s(["Loader2",()=>r],44670);let r=(0,e.i(4741).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},33,e=>{"use strict";e.s(["Save",()=>r],33);let r=(0,e.i(4741).default)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},83947,e=>{"use strict";e.s(["StudentForm",()=>m]);var r=e.i(53379),t=e.i(46686),a=e.i(18125),s=e.i(30151),l=e.i(96487),n=e.i(92521),i=e.i(32668),d=e.i(70307),o=e.i(44670),c=e.i(33),u=e.i(57606);function m(e){let{student:m,classes:p,mode:h}=e,g=(0,a.useRouter)(),[f,x]=(0,t.useState)(!1),[v,y]=(0,t.useState)(null),[b,N]=(0,t.useState)(null),[j,w]=(0,t.useState)({firstName:"",lastName:"",email:"",dateOfBirth:"",gender:"OTHER",phoneNumber:"",address:"",emergencyContact:"",emergencyPhone:"",admissionDate:"",classId:"",parentName:"",parentPhone:"",parentEmail:""});(0,t.useEffect)(()=>{m&&w({...m,dateOfBirth:m.dateOfBirth?new Date(m.dateOfBirth).toISOString().split("T")[0]:"",admissionDate:m.admissionDate?new Date(m.admissionDate).toISOString().split("T")[0]:""})},[m]);let C=(e,r)=>{w(t=>({...t,[e]:r})),y(null)},k=async e=>{if(e.preventDefault(),j.firstName.trim()?j.lastName.trim()?j.email.trim()?j.email.includes("@")?j.dateOfBirth?j.admissionDate?!!j.classId||(y("Please select a class"),!1):(y("Admission date is required"),!1):(y("Date of birth is required"),!1):(y("Please enter a valid email address"),!1):(y("Email is required"),!1):(y("Last name is required"),!1):(y("First name is required"),!1)){x(!0),y(null),N(null);try{let e="create"===h?"/api/admin/students":"/api/admin/students/".concat(null==m?void 0:m.id),r=await fetch(e,{method:"create"===h?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(j)}),t=await r.json();if(!r.ok)throw Error(t.error||"Something went wrong");N("create"===h?"Student created successfully!":"Student updated successfully!"),setTimeout(()=>{g.push("/admin/students")},1500)}catch(e){y(e instanceof Error?e.message:"Something went wrong")}finally{x(!1)}}};return(0,r.jsxs)(i.Card,{className:"w-full max-w-4xl mx-auto",children:[(0,r.jsxs)(i.CardHeader,{className:"px-4 sm:px-6",children:[(0,r.jsx)(i.CardTitle,{className:"text-xl sm:text-2xl",children:"create"===h?"Add New Student":"Edit Student"}),(0,r.jsx)(i.CardDescription,{className:"text-sm sm:text-base",children:"create"===h?"Enter student information to create a new student account":"Update student information"})]}),(0,r.jsx)(i.CardContent,{className:"px-4 sm:px-6",children:(0,r.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[v&&(0,r.jsx)(d.Alert,{variant:"destructive",children:(0,r.jsx)(d.AlertDescription,{children:v})}),b&&(0,r.jsx)(d.Alert,{children:(0,r.jsx)(d.AlertDescription,{children:b})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"firstName",children:"First Name *"}),(0,r.jsx)(l.Input,{id:"firstName",value:j.firstName,onChange:e=>C("firstName",e.target.value),placeholder:"Enter first name",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"lastName",children:"Last Name *"}),(0,r.jsx)(l.Input,{id:"lastName",value:j.lastName,onChange:e=>C("lastName",e.target.value),placeholder:"Enter last name",required:!0})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"email",children:"Email *"}),(0,r.jsx)(l.Input,{id:"email",type:"email",value:j.email,onChange:e=>C("email",e.target.value),placeholder:"Enter email address",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"phoneNumber",children:"Phone Number"}),(0,r.jsx)(l.Input,{id:"phoneNumber",value:j.phoneNumber,onChange:e=>C("phoneNumber",e.target.value),placeholder:"Enter phone number"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"dateOfBirth",children:"Date of Birth *"}),(0,r.jsx)(l.Input,{id:"dateOfBirth",type:"date",value:j.dateOfBirth,onChange:e=>C("dateOfBirth",e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"gender",children:"Gender *"}),(0,r.jsxs)("select",{id:"gender",value:j.gender,onChange:e=>C("gender",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[44px] text-base",required:!0,children:[(0,r.jsx)("option",{value:"OTHER",children:"Select Gender"}),(0,r.jsx)("option",{value:"MALE",children:"Male"}),(0,r.jsx)("option",{value:"FEMALE",children:"Female"}),(0,r.jsx)("option",{value:"OTHER",children:"Other"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"admissionDate",children:"Admission Date *"}),(0,r.jsx)(l.Input,{id:"admissionDate",type:"date",value:j.admissionDate,onChange:e=>C("admissionDate",e.target.value),required:!0})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"address",children:"Address"}),(0,r.jsx)(l.Input,{id:"address",value:j.address,onChange:e=>C("address",e.target.value),placeholder:"Enter address"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"classId",children:"Class *"}),(0,r.jsxs)("select",{id:"classId",value:j.classId,onChange:e=>C("classId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[44px] text-base",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select Class"}),p.map(e=>e.sections.map(t=>(0,r.jsxs)("option",{value:"".concat(e.id,"-").concat(t.id),children:[e.name," - ",t.name]},"".concat(e.id,"-").concat(t.id))))]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"emergencyContact",children:"Emergency Contact"}),(0,r.jsx)(l.Input,{id:"emergencyContact",value:j.emergencyContact,onChange:e=>C("emergencyContact",e.target.value),placeholder:"Enter emergency contact name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"emergencyPhone",children:"Emergency Phone"}),(0,r.jsx)(l.Input,{id:"emergencyPhone",value:j.emergencyPhone,onChange:e=>C("emergencyPhone",e.target.value),placeholder:"Enter emergency phone number"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"parentName",children:"Parent Name"}),(0,r.jsx)(l.Input,{id:"parentName",value:j.parentName,onChange:e=>C("parentName",e.target.value),placeholder:"Enter parent name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"parentPhone",children:"Parent Phone"}),(0,r.jsx)(l.Input,{id:"parentPhone",value:j.parentPhone,onChange:e=>C("parentPhone",e.target.value),placeholder:"Enter parent phone number"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Label,{htmlFor:"parentEmail",children:"Parent Email"}),(0,r.jsx)(l.Input,{id:"parentEmail",type:"email",value:j.parentEmail,onChange:e=>C("parentEmail",e.target.value),placeholder:"Enter parent email"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-4 pt-6",children:[(0,r.jsxs)(s.Button,{type:"button",variant:"outline",onClick:()=>{g.push("/admin/students")},disabled:f,className:"w-full sm:w-auto min-h-[44px]",children:[(0,r.jsx)(u.X,{className:"w-4 h-4 mr-2"}),"Cancel"]}),(0,r.jsxs)(s.Button,{type:"submit",disabled:f,className:"w-full sm:w-auto min-h-[44px]",children:[f?(0,r.jsx)(o.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}):(0,r.jsx)(c.Save,{className:"w-4 h-4 mr-2"}),"create"===h?"Create Student":"Update Student"]})]})]})})]})}}]);