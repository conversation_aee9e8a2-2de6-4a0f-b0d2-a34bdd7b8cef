(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,56873,e=>{"use strict";e.s(["default",()=>lP],56873);var t,n=e.i(53379),r=e.i(46686),i=e.i(18125),a=e.i(69758),o=e.i(32668),c=e.i(30151),u=e.i(89559),l=e.i(84633),s=e.i(96487),d=e.i(92521);function m(e){return e}function f(e){return e}function v(e){}function p(e){throw Error()}function g(e){}function h(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(e=>{let[n,r]=e;return -1===t.indexOf(+n)}).map(e=>{let[t,n]=e;return n})}function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"|";return e.map(e=>G(e)).join(t)}function _(e,t){return"bigint"==typeof t?t.toString():t}function b(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function k(e){return null==e}function x(e){let t=+!!e.startsWith("^"),n=e.endsWith("$")?e.length-1:e.length;return e.slice(t,n)}function I(e,t){let n=(e.toString().split(".")[1]||"").length,r=t.toString(),i=(r.split(".")[1]||"").length;if(0===i&&/\d?e-\d?/.test(r)){let e=r.match(/\d?e-(\d?)/);(null==e?void 0:e[1])&&(i=Number.parseInt(e[1]))}let a=n>i?n:i;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}e.s(["$brand",()=>nn,"$input",()=>i$,"$output",()=>iD,"NEVER",()=>ne,"TimePrecision",()=>i5,"ZodAny",()=>cX,"ZodArray",()=>c9,"ZodBase64",()=>cp,"ZodBase64URL",()=>ch,"ZodBigInt",()=>cC,"ZodBigIntFormat",()=>cR,"ZodBoolean",()=>cA,"ZodCIDRv4",()=>cd,"ZodCIDRv6",()=>cf,"ZodCUID",()=>o5,"ZodCUID2",()=>o7,"ZodCatch",()=>uL,"ZodCodec",()=>uW,"ZodCustom",()=>u1,"ZodCustomStringFormat",()=>cI,"ZodDate",()=>c1,"ZodDefault",()=>uN,"ZodDiscriminatedUnion",()=>ui,"ZodE164",()=>c_,"ZodEmail",()=>oW,"ZodEmoji",()=>o1,"ZodEnum",()=>uh,"ZodError",()=>oS,"ZodFile",()=>ux,"ZodFirstPartyTypeKind",()=>t,"ZodFunction",()=>u4,"ZodGUID",()=>oG,"ZodIPv4",()=>cc,"ZodIPv6",()=>cl,"ZodISODate",()=>li,"ZodISODateTime",()=>ln,"ZodISODuration",()=>lu,"ZodISOTime",()=>lo,"ZodIntersection",()=>uo,"ZodIssueCode",()=>ly,"ZodJWT",()=>ck,"ZodKSUID",()=>ca,"ZodLazy",()=>uY,"ZodLiteral",()=>ub,"ZodMap",()=>uf,"ZodNaN",()=>uM,"ZodNanoID",()=>o9,"ZodNever",()=>cQ,"ZodNonOptional",()=>uE,"ZodNull",()=>cG,"ZodNullable",()=>uZ,"ZodNumber",()=>cZ,"ZodNumberFormat",()=>cO,"ZodObject",()=>c8,"ZodOptional",()=>uS,"ZodPipe",()=>uJ,"ZodPrefault",()=>u$,"ZodPromise",()=>uQ,"ZodReadonly",()=>uG,"ZodRealError",()=>oj,"ZodRecord",()=>us,"ZodSet",()=>up,"ZodString",()=>oF,"ZodStringFormat",()=>oB,"ZodSuccess",()=>uT,"ZodSymbol",()=>cJ,"ZodTemplateLiteral",()=>uX,"ZodTransform",()=>uw,"ZodTuple",()=>uu,"ZodType",()=>oR,"ZodULID",()=>ct,"ZodURL",()=>o0,"ZodUUID",()=>oX,"ZodUndefined",()=>cW,"ZodUnion",()=>un,"ZodUnknown",()=>cY,"ZodVoid",()=>c4,"ZodXID",()=>cr,"_ZodString",()=>oM,"_default",()=>uD,"_function",()=>u6,"any",()=>cq,"array",()=>c3,"base64",()=>cg,"base64url",()=>cy,"bigint",()=>cL,"boolean",()=>cT,"catch",()=>uR,"check",()=>u2,"cidrv4",()=>cm,"cidrv6",()=>cv,"clone",()=>B,"codec",()=>uV,"coerce",()=>lI,"config",()=>no,"core",()=>lh,"cuid",()=>o8,"cuid2",()=>ce,"custom",()=>u9,"date",()=>c2,"decode",()=>o$,"decodeAsync",()=>oE,"discriminatedUnion",()=>ua,"e164",()=>cb,"email",()=>oV,"emoji",()=>o2,"encode",()=>oD,"encodeAsync",()=>oP,"endsWith",()=>aW,"enum",()=>uy,"file",()=>uI,"flattenError",()=>ns,"float32",()=>cD,"float64",()=>c$,"formatError",()=>nd,"function",()=>u6,"getErrorMap",()=>lb,"globalRegistry",()=>iA,"gt",()=>aj,"gte",()=>aZ,"guid",()=>oK,"hash",()=>cj,"hex",()=>cS,"hostname",()=>cz,"httpUrl",()=>o6,"includes",()=>aJ,"instanceof",()=>u8,"int",()=>cN,"int32",()=>cP,"int64",()=>cM,"intersection",()=>uc,"ipv4",()=>cu,"ipv6",()=>cs,"iso",()=>lx,"json",()=>le,"jwt",()=>cx,"keyof",()=>c5,"ksuid",()=>co,"lazy",()=>uH,"length",()=>aL,"literal",()=>uk,"locales",()=>lk,"looseObject",()=>ut,"lowercase",()=>aM,"lt",()=>az,"lte",()=>aS,"map",()=>uv,"maxLength",()=>aT,"maxSize",()=>aP,"mime",()=>aG,"minLength",()=>aC,"minSize",()=>aE,"multipleOf",()=>a$,"nan",()=>uF,"nanoid",()=>o3,"nativeEnum",()=>u_,"negative",()=>aO,"never",()=>c0,"nonnegative",()=>aD,"nonoptional",()=>uA,"nonpositive",()=>aN,"normalize",()=>aX,"null",()=>cK,"nullable",()=>uU,"nullish",()=>uO,"number",()=>cU,"object",()=>c7,"optional",()=>uj,"overwrite",()=>aK,"parse",()=>oZ,"parseAsync",()=>oU,"partialRecord",()=>um,"pipe",()=>uB,"positive",()=>aU,"prefault",()=>uP,"preprocess",()=>lt,"prettifyError",()=>nv,"promise",()=>u0,"property",()=>aV,"readonly",()=>uK,"record",()=>ud,"refine",()=>u3,"regex",()=>aR,"regexes",()=>oI,"registry",()=>iE,"safeDecode",()=>oT,"safeDecodeAsync",()=>oL,"safeEncode",()=>oA,"safeEncodeAsync",()=>oC,"safeParse",()=>oO,"safeParseAsync",()=>oN,"set",()=>ug,"setErrorMap",()=>l_,"size",()=>aA,"startsWith",()=>aB,"strictObject",()=>ue,"string",()=>oJ,"stringFormat",()=>cw,"stringbool",()=>u7,"success",()=>uC,"superRefine",()=>u5,"symbol",()=>cB,"templateLiteral",()=>uq,"toJSONSchema",()=>ob,"toLowerCase",()=>aY,"toUpperCase",()=>aH,"transform",()=>uz,"treeifyError",()=>nm,"trim",()=>aq,"tuple",()=>ul,"uint32",()=>cE,"uint64",()=>cF,"ulid",()=>cn,"undefined",()=>cV,"union",()=>ur,"unknown",()=>cH,"uppercase",()=>aF,"url",()=>o4,"util",()=>ow,"uuid",()=>oq,"uuidv4",()=>oY,"uuidv6",()=>oH,"uuidv7",()=>oQ,"void",()=>c6,"xid",()=>ci],49984),e.s([],23513),e.s(["$ZodAny",()=>rL,"$ZodArray",()=>rW,"$ZodAsyncError",()=>nr,"$ZodBase64",()=>rw,"$ZodBase64URL",()=>rS,"$ZodBigInt",()=>rP,"$ZodBigIntFormat",()=>rE,"$ZodBoolean",()=>r$,"$ZodCIDRv4",()=>rk,"$ZodCIDRv6",()=>rx,"$ZodCUID",()=>rs,"$ZodCUID2",()=>rd,"$ZodCatch",()=>iv,"$ZodCheck",()=>nL,"$ZodCheckBigIntFormat",()=>nW,"$ZodCheckEndsWith",()=>n2,"$ZodCheckGreaterThan",()=>nF,"$ZodCheckIncludes",()=>n6,"$ZodCheckLengthEquals",()=>nY,"$ZodCheckLessThan",()=>nM,"$ZodCheckLowerCase",()=>n0,"$ZodCheckMaxLength",()=>nX,"$ZodCheckMaxSize",()=>nV,"$ZodCheckMimeType",()=>n5,"$ZodCheckMinLength",()=>nq,"$ZodCheckMinSize",()=>nG,"$ZodCheckMultipleOf",()=>nJ,"$ZodCheckNumberFormat",()=>nB,"$ZodCheckOverwrite",()=>n8,"$ZodCheckProperty",()=>n3,"$ZodCheckRegex",()=>nQ,"$ZodCheckSizeEquals",()=>nK,"$ZodCheckStartsWith",()=>n1,"$ZodCheckStringFormat",()=>nH,"$ZodCheckUpperCase",()=>n4,"$ZodCodec",()=>iy,"$ZodCustom",()=>ij,"$ZodCustomStringFormat",()=>rO,"$ZodDate",()=>rJ,"$ZodDefault",()=>ic,"$ZodDiscriminatedUnion",()=>rQ,"$ZodE164",()=>rj,"$ZodEmail",()=>ro,"$ZodEmoji",()=>ru,"$ZodEncodeError",()=>ni,"$ZodEnum",()=>r7,"$ZodError",()=>nu,"$ZodFile",()=>it,"$ZodFunction",()=>iw,"$ZodGUID",()=>ri,"$ZodIPv4",()=>r_,"$ZodIPv6",()=>rb,"$ZodISODate",()=>rg,"$ZodISODateTime",()=>rp,"$ZodISODuration",()=>ry,"$ZodISOTime",()=>rh,"$ZodIntersection",()=>r0,"$ZodJWT",()=>rU,"$ZodKSUID",()=>rv,"$ZodLazy",()=>iS,"$ZodLiteral",()=>ie,"$ZodMap",()=>r9,"$ZodNaN",()=>ip,"$ZodNanoID",()=>rl,"$ZodNever",()=>rM,"$ZodNonOptional",()=>is,"$ZodNull",()=>rC,"$ZodNullable",()=>io,"$ZodNumber",()=>rN,"$ZodNumberFormat",()=>rD,"$ZodObject",()=>rX,"$ZodObjectJIT",()=>rq,"$ZodOptional",()=>ia,"$ZodPipe",()=>ig,"$ZodPrefault",()=>il,"$ZodPromise",()=>iz,"$ZodReadonly",()=>ik,"$ZodRealError",()=>nl,"$ZodRecord",()=>r2,"$ZodRegistry",()=>iP,"$ZodSet",()=>r5,"$ZodString",()=>rn,"$ZodStringFormat",()=>rr,"$ZodSuccess",()=>im,"$ZodSymbol",()=>rA,"$ZodTemplateLiteral",()=>iI,"$ZodTransform",()=>ir,"$ZodTuple",()=>r6,"$ZodType",()=>rt,"$ZodULID",()=>rm,"$ZodURL",()=>rc,"$ZodUUID",()=>ra,"$ZodUndefined",()=>rT,"$ZodUnion",()=>rH,"$ZodUnknown",()=>rR,"$ZodVoid",()=>rF,"$ZodXID",()=>rf,"$brand",()=>nn,"$constructor",()=>nt,"$input",()=>i$,"$output",()=>iD,"Doc",()=>n7,"JSONSchema",()=>ok,"JSONSchemaGenerator",()=>o_,"NEVER",()=>ne,"TimePrecision",()=>i5,"_any",()=>ay,"_array",()=>aQ,"_base64",()=>i1,"_base64url",()=>i2,"_bigint",()=>ad,"_boolean",()=>al,"_catch",()=>oc,"_check",()=>og,"_cidrv4",()=>i4,"_cidrv6",()=>i6,"_coercedBigint",()=>am,"_coercedBoolean",()=>as,"_coercedDate",()=>aI,"_coercedNumber",()=>ar,"_coercedString",()=>iC,"_cuid",()=>iK,"_cuid2",()=>iX,"_custom",()=>of,"_date",()=>ax,"_decode",()=>nz,"_decodeAsync",()=>nU,"_default",()=>oi,"_discriminatedUnion",()=>a4,"_e164",()=>i9,"_email",()=>iL,"_emoji",()=>iV,"_encode",()=>nI,"_encodeAsync",()=>nj,"_endsWith",()=>aW,"_enum",()=>a5,"_file",()=>oe,"_float32",()=>aa,"_float64",()=>ao,"_gt",()=>aj,"_gte",()=>aZ,"_guid",()=>iR,"_includes",()=>aJ,"_int",()=>ai,"_int32",()=>ac,"_int64",()=>af,"_intersection",()=>a6,"_ipv4",()=>iQ,"_ipv6",()=>i0,"_isoDate",()=>i7,"_isoDateTime",()=>i8,"_isoDuration",()=>at,"_isoTime",()=>ae,"_jwt",()=>i3,"_ksuid",()=>iH,"_lazy",()=>od,"_length",()=>aL,"_literal",()=>a7,"_lowercase",()=>aM,"_lt",()=>az,"_lte",()=>aS,"_map",()=>a9,"_max",()=>aS,"_maxLength",()=>aT,"_maxSize",()=>aP,"_mime",()=>aG,"_min",()=>aZ,"_minLength",()=>aC,"_minSize",()=>aE,"_multipleOf",()=>a$,"_nan",()=>aw,"_nanoid",()=>iG,"_nativeEnum",()=>a8,"_negative",()=>aO,"_never",()=>ab,"_nonnegative",()=>aD,"_nonoptional",()=>oa,"_nonpositive",()=>aN,"_normalize",()=>aX,"_null",()=>ah,"_nullable",()=>or,"_number",()=>an,"_optional",()=>on,"_overwrite",()=>aK,"_parse",()=>np,"_parseAsync",()=>nh,"_pipe",()=>ou,"_positive",()=>aU,"_promise",()=>om,"_property",()=>aV,"_readonly",()=>ol,"_record",()=>a2,"_refine",()=>ov,"_regex",()=>aR,"_safeDecode",()=>n$,"_safeDecodeAsync",()=>nT,"_safeEncode",()=>nN,"_safeEncodeAsync",()=>nE,"_safeParse",()=>n_,"_safeParseAsync",()=>nk,"_set",()=>a3,"_size",()=>aA,"_startsWith",()=>aB,"_string",()=>iT,"_stringFormat",()=>oy,"_stringbool",()=>oh,"_success",()=>oo,"_superRefine",()=>op,"_symbol",()=>ap,"_templateLiteral",()=>os,"_toLowerCase",()=>aY,"_toUpperCase",()=>aH,"_transform",()=>ot,"_trim",()=>aq,"_tuple",()=>a1,"_uint32",()=>au,"_uint64",()=>av,"_ulid",()=>iq,"_undefined",()=>ag,"_union",()=>a0,"_unknown",()=>a_,"_uppercase",()=>aF,"_url",()=>iW,"_uuid",()=>iM,"_uuidv4",()=>iF,"_uuidv6",()=>iJ,"_uuidv7",()=>iB,"_void",()=>ak,"_xid",()=>iY,"clone",()=>B,"config",()=>no,"decode",()=>nS,"decodeAsync",()=>nO,"encode",()=>nw,"encodeAsync",()=>nZ,"flattenError",()=>ns,"formatError",()=>nd,"globalConfig",()=>na,"globalRegistry",()=>iA,"isValidBase64",()=>rI,"isValidBase64URL",()=>rz,"isValidJWT",()=>rZ,"locales",()=>iN,"parse",()=>ng,"parseAsync",()=>ny,"prettifyError",()=>nv,"regexes",()=>iO,"registry",()=>iE,"safeDecode",()=>nP,"safeDecodeAsync",()=>nC,"safeEncode",()=>nD,"safeEncodeAsync",()=>nA,"safeParse",()=>nb,"safeParseAsync",()=>nx,"toDotPath",()=>nf,"toJSONSchema",()=>ob,"treeifyError",()=>nm,"util",()=>iU,"version",()=>re],36891),e.s([],80370),e.s(["BIGINT_FORMAT_RANGES",()=>q,"Class",()=>ey,"NUMBER_FORMAT_RANGES",()=>X,"aborted",()=>ei,"allowsEval",()=>A,"assert",()=>g,"assertEqual",()=>m,"assertIs",()=>v,"assertNever",()=>p,"assertNotEqual",()=>f,"assignProp",()=>j,"base64ToUint8Array",()=>em,"base64urlToUint8Array",()=>ev,"cached",()=>b,"captureStackTrace",()=>P,"cleanEnum",()=>ed,"cleanRegex",()=>x,"clone",()=>B,"cloneDef",()=>U,"createTransparentProxy",()=>V,"defineLazy",()=>z,"esc",()=>$,"escapeRegex",()=>J,"extend",()=>Q,"finalizeIssue",()=>ec,"floatSafeRemainder",()=>I,"getElementAtPath",()=>O,"getEnumValues",()=>h,"getLengthableOrigin",()=>el,"getParsedType",()=>R,"getSizableOrigin",()=>eu,"hexToUint8Array",()=>eg,"isObject",()=>E,"isPlainObject",()=>T,"issue",()=>es,"joinValues",()=>y,"jsonStringifyReplacer",()=>_,"merge",()=>et,"mergeDefs",()=>Z,"normalizeParams",()=>W,"nullish",()=>k,"numKeys",()=>L,"objectClone",()=>S,"omit",()=>H,"optionalKeys",()=>K,"partial",()=>en,"pick",()=>Y,"prefixIssues",()=>ea,"primitiveTypes",()=>F,"promiseAllObject",()=>N,"propertyKeyTypes",()=>M,"randomString",()=>D,"required",()=>er,"safeExtend",()=>ee,"shallowClone",()=>C,"stringifyPrimitive",()=>G,"uint8ArrayToBase64",()=>ef,"uint8ArrayToBase64url",()=>ep,"uint8ArrayToHex",()=>eh,"unwrapMessage",()=>eo],25452);let w=Symbol("evaluating");function z(e,t,n){let r;Object.defineProperty(e,t,{get(){if(r!==w)return void 0===r&&(r=w,r=n()),r},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function S(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function j(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0,configurable:!0})}function Z(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r={};for(let e of t)Object.assign(r,Object.getOwnPropertyDescriptors(e));return Object.defineProperties({},r)}function U(e){return Z(e._zod.def)}function O(e,t){return t?t.reduce((e,t)=>null==e?void 0:e[t],e):e}function N(e){let t=Object.keys(e);return Promise.all(t.map(t=>e[t])).then(e=>{let n={};for(let r=0;r<t.length;r++)n[t[r]]=e[r];return n})}function D(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t="abcdefghijklmnopqrstuvwxyz",n="";for(let r=0;r<e;r++)n+=t[Math.floor(Math.random()*t.length)];return n}function $(e){return JSON.stringify(e)}let P="captureStackTrace"in Error?Error.captureStackTrace:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]};function E(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let A=b(()=>{var e,t;if("undefined"!=typeof navigator&&(null==(t=navigator)||null==(e=t.userAgent)?void 0:e.includes("Cloudflare")))return!1;try{return Function(""),!0}catch(e){return!1}});function T(e){if(!1===E(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!1!==E(n)&&!1!==Object.prototype.hasOwnProperty.call(n,"isPrototypeOf")}function C(e){return T(e)?{...e}:e}function L(e){let t=0;for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t++;return t}let R=e=>{let t=typeof e;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(e)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return"promise";if("undefined"!=typeof Map&&e instanceof Map)return"map";if("undefined"!=typeof Set&&e instanceof Set)return"set";if("undefined"!=typeof Date&&e instanceof Date)return"date";if("undefined"!=typeof File&&e instanceof File)return"file";return"object";default:throw Error("Unknown data type: ".concat(t))}},M=new Set(["string","number","symbol"]),F=new Set(["string","number","bigint","boolean","symbol","undefined"]);function J(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function B(e,t,n){let r=new e._zod.constr(null!=t?t:e._zod.def);return(!t||(null==n?void 0:n.parent))&&(r._zod.parent=e),r}function W(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if((null==e?void 0:e.message)!==void 0){if((null==e?void 0:e.error)!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function V(e){let t;return new Proxy({},{get:(n,r,i)=>(null!=t||(t=e()),Reflect.get(t,r,i)),set:(n,r,i,a)=>(null!=t||(t=e()),Reflect.set(t,r,i,a)),has:(n,r)=>(null!=t||(t=e()),Reflect.has(t,r)),deleteProperty:(n,r)=>(null!=t||(t=e()),Reflect.deleteProperty(t,r)),ownKeys:n=>(null!=t||(t=e()),Reflect.ownKeys(t)),getOwnPropertyDescriptor:(n,r)=>(null!=t||(t=e()),Reflect.getOwnPropertyDescriptor(t,r)),defineProperty:(n,r,i)=>(null!=t||(t=e()),Reflect.defineProperty(t,r,i))})}function G(e){return"bigint"==typeof e?e.toString()+"n":"string"==typeof e?'"'.concat(e,'"'):"".concat(e)}function K(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}let X={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},q={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function Y(e,t){let n=e._zod.def,r=Z(e._zod.def,{get shape(){let e={};for(let r in t){if(!(r in n.shape))throw Error('Unrecognized key: "'.concat(r,'"'));t[r]&&(e[r]=n.shape[r])}return j(this,"shape",e),e},checks:[]});return B(e,r)}function H(e,t){let n=e._zod.def,r=Z(e._zod.def,{get shape(){let r={...e._zod.def.shape};for(let e in t){if(!(e in n.shape))throw Error('Unrecognized key: "'.concat(e,'"'));t[e]&&delete r[e]}return j(this,"shape",r),r},checks:[]});return B(e,r)}function Q(e,t){if(!T(t))throw Error("Invalid input to extend: expected a plain object");let n=e._zod.def.checks;if(n&&n.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let r=Z(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t};return j(this,"shape",n),n},checks:[]});return B(e,r)}function ee(e,t){if(!T(t))throw Error("Invalid input to safeExtend: expected a plain object");let n={...e._zod.def,get shape(){let n={...e._zod.def.shape,...t};return j(this,"shape",n),n},checks:e._zod.def.checks};return B(e,n)}function et(e,t){let n=Z(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t._zod.def.shape};return j(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return B(e,n)}function en(e,t,n){let r=Z(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in r))throw Error('Unrecognized key: "'.concat(t,'"'));n[t]&&(i[t]=e?new e({type:"optional",innerType:r[t]}):r[t])}else for(let t in r)i[t]=e?new e({type:"optional",innerType:r[t]}):r[t];return j(this,"shape",i),i},checks:[]});return B(t,r)}function er(e,t,n){let r=Z(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in i))throw Error('Unrecognized key: "'.concat(t,'"'));n[t]&&(i[t]=new e({type:"nonoptional",innerType:r[t]}))}else for(let t in r)i[t]=new e({type:"nonoptional",innerType:r[t]});return j(this,"shape",i),i},checks:[]});return B(t,r)}function ei(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!0===e.aborted)return!0;for(let r=t;r<e.issues.length;r++){var n;if((null==(n=e.issues[r])?void 0:n.continue)!==!0)return!0}return!1}function ea(e,t){return t.map(t=>(null!=t.path||(t.path=[]),t.path.unshift(e),t))}function eo(e){return"string"==typeof e?e:null==e?void 0:e.message}function ec(e,t,n){var r,i,a,o,c,u,l,s,d,m,f;let v={...e,path:null!=(r=e.path)?r:[]};return e.message||(v.message=null!=(f=null!=(m=null!=(d=null!=(s=eo(null==(o=e.inst)||null==(a=o._zod.def)||null==(i=a.error)?void 0:i.call(a,e)))?s:eo(null==t||null==(c=t.error)?void 0:c.call(t,e)))?d:eo(null==(u=n.customError)?void 0:u.call(n,e)))?m:eo(null==(l=n.localeError)?void 0:l.call(n,e)))?f:"Invalid input"),delete v.inst,delete v.continue,(null==t?void 0:t.reportInput)||delete v.input,v}function eu(e){return e instanceof Set?"set":e instanceof Map?"map":e instanceof File?"file":"unknown"}function el(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function es(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,a]=t;return"string"==typeof r?{message:r,code:"custom",input:i,inst:a}:{...r}}function ed(e){return Object.entries(e).filter(e=>{let[t,n]=e;return Number.isNaN(Number.parseInt(t,10))}).map(e=>e[1])}function em(e){let t=atob(e),n=new Uint8Array(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return n}function ef(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return btoa(t)}function ev(e){let t=e.replace(/-/g,"+").replace(/_/g,"/"),n="=".repeat((4-t.length%4)%4);return em(t+n)}function ep(e){return ef(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function eg(e){let t=e.replace(/^0x/,"");if(t.length%2!=0)throw Error("Invalid hex string length");let n=new Uint8Array(t.length/2);for(let e=0;e<t.length;e+=2)n[e/2]=Number.parseInt(t.slice(e,e+2),16);return n}function eh(e){return Array.from(e).map(e=>e.toString(16).padStart(2,"0")).join("")}class ey{constructor(...e){}}var e_=e.i(25452);e.s(["base64",()=>eB,"base64url",()=>eW,"bigint",()=>e4,"boolean",()=>e2,"browserEmail",()=>eC,"cidrv4",()=>eF,"cidrv6",()=>eJ,"cuid",()=>eb,"cuid2",()=>ek,"date",()=>eq,"datetime",()=>eQ,"domain",()=>eG,"duration",()=>eS,"e164",()=>eK,"email",()=>e$,"emoji",()=>eL,"extendedDuration",()=>ej,"guid",()=>eZ,"hex",()=>e7,"hostname",()=>eV,"html5Email",()=>eP,"idnEmail",()=>eT,"integer",()=>e6,"ipv4",()=>eR,"ipv6",()=>eM,"ksuid",()=>ew,"lowercase",()=>e5,"md5_base64",()=>tr,"md5_base64url",()=>ti,"md5_hex",()=>tn,"nanoid",()=>ez,"null",()=>e9,"number",()=>e1,"rfc5322Email",()=>eE,"sha1_base64",()=>to,"sha1_base64url",()=>tc,"sha1_hex",()=>ta,"sha256_base64",()=>tl,"sha256_base64url",()=>ts,"sha256_hex",()=>tu,"sha384_base64",()=>tm,"sha384_base64url",()=>tf,"sha384_hex",()=>td,"sha512_base64",()=>tp,"sha512_base64url",()=>tg,"sha512_hex",()=>tv,"string",()=>e0,"time",()=>eH,"ulid",()=>ex,"undefined",()=>e3,"unicodeEmail",()=>eA,"uppercase",()=>e8,"uuid",()=>eU,"uuid4",()=>eO,"uuid6",()=>eN,"uuid7",()=>eD,"xid",()=>eI],37601);let eb=/^[cC][^\s-]{8,}$/,ek=/^[0-9a-z]+$/,ex=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,eI=/^[0-9a-vA-V]{20}$/,ew=/^[A-Za-z0-9]{27}$/,ez=/^[a-zA-Z0-9_-]{21}$/,eS=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,ej=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,eZ=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,eU=e=>e?new RegExp("^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-".concat(e,"[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$")):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,eO=eU(4),eN=eU(6),eD=eU(7),e$=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,eP=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,eE=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,eA=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,eT=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,eC=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function eL(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let eR=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,eM=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,eF=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,eJ=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,eB=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,eW=/^[A-Za-z0-9_-]*$/,eV=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,eG=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,eK=/^\+(?:[0-9]){6,14}[0-9]$/,eX="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",eq=new RegExp("^".concat(eX,"$"));function eY(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?"".concat(t):0===e.precision?"".concat(t,":[0-5]\\d"):"".concat(t,":[0-5]\\d\\.\\d{").concat(e.precision,"}"):"".concat(t,"(?::[0-5]\\d(?:\\.\\d+)?)?")}function eH(e){return new RegExp("^".concat(eY(e),"$"))}function eQ(e){let t=eY({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let r="".concat(t,"(?:").concat(n.join("|"),")");return new RegExp("^".concat(eX,"T(?:").concat(r,")$"))}let e0=e=>{var t,n;let r=e?"[\\s\\S]{".concat(null!=(t=null==e?void 0:e.minimum)?t:0,",").concat(null!=(n=null==e?void 0:e.maximum)?n:"","}"):"[\\s\\S]*";return new RegExp("^".concat(r,"$"))},e4=/^\d+n?$/,e6=/^\d+$/,e1=/^-?\d+(?:\.\d+)?/i,e2=/true|false/i,e9=/null/i,e3=/undefined/i,e5=/^[^A-Z]*$/,e8=/^[^a-z]*$/,e7=/^[0-9a-fA-F]*$/;function te(e,t){return new RegExp("^[A-Za-z0-9+/]{".concat(e,"}").concat(t,"$"))}function tt(e){return new RegExp("^[A-Za-z0-9-_]{".concat(e,"}$"))}let tn=/^[0-9a-fA-F]{32}$/,tr=te(22,"=="),ti=tt(22),ta=/^[0-9a-fA-F]{40}$/,to=te(27,"="),tc=tt(27),tu=/^[0-9a-fA-F]{64}$/,tl=te(43,"="),ts=tt(43),td=/^[0-9a-fA-F]{96}$/,tm=te(64,""),tf=tt(64),tv=/^[0-9a-fA-F]{128}$/,tp=te(86,"=="),tg=tt(86);var th=e.i(37601);function ty(){return{localeError:(()=>{let e={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"مدخلات غير مقبولة: يفترض إدخال ".concat(e.expected,"، ولكن تم إدخال ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"مدخلات غير مقبولة: يفترض إدخال ".concat(G(e.values[0]));return"اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return" أكبر من اللازم: يفترض أن تكون ".concat(null!=(r=e.origin)?r:"القيمة"," ").concat(n," ").concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"عنصر");return"أكبر من اللازم: يفترض أن تكون ".concat(null!=(a=e.origin)?a:"القيمة"," ").concat(n," ").concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"أصغر من اللازم: يفترض لـ ".concat(e.origin," أن يكون ").concat(n," ").concat(e.minimum.toString()," ").concat(r.unit);return"أصغر من اللازم: يفترض لـ ".concat(e.origin," أن يكون ").concat(n," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'نَص غير مقبول: يجب أن يبدأ بـ "'.concat(e.prefix,'"');if("ends_with"===e.format)return'نَص غير مقبول: يجب أن ينتهي بـ "'.concat(e.suffix,'"');if("includes"===e.format)return'نَص غير مقبول: يجب أن يتضمَّن "'.concat(e.includes,'"');if("regex"===e.format)return"نَص غير مقبول: يجب أن يطابق النمط ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," غير مقبول");case"not_multiple_of":return"رقم غير مقبول: يجب أن يكون من مضاعفات ".concat(e.divisor);case"unrecognized_keys":return"معرف".concat(e.keys.length>1?"ات":""," غريب").concat(e.keys.length>1?"ة":"",": ").concat(y(e.keys,"، "));case"invalid_key":return"معرف غير مقبول في ".concat(e.origin);case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return"مدخل غير مقبول في ".concat(e.origin)}}})()}}function t_(){return{localeError:(()=>{let e={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Yanlış dəyər: gözlənilən ".concat(e.expected,", daxil olan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Yanlış dəyər: gözlənilən ".concat(G(e.values[0]));return"Yanlış seçim: aşağıdakılardan biri olmalıdır: ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Çox böyük: gözlənilən ".concat(null!=(r=e.origin)?r:"dəyər"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"element");return"Çox böyük: gözlənilən ".concat(null!=(a=e.origin)?a:"dəyər"," ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Çox kiçik: gözlənilən ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Çox kiçik: gözlənilən ".concat(e.origin," ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Yanlış mətn: "'.concat(e.prefix,'" ilə başlamalıdır');if("ends_with"===e.format)return'Yanlış mətn: "'.concat(e.suffix,'" ilə bitməlidir');if("includes"===e.format)return'Yanlış mətn: "'.concat(e.includes,'" daxil olmalıdır');if("regex"===e.format)return"Yanlış mətn: ".concat(e.pattern," şablonuna uyğun olmalıdır");return"Yanlış ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Yanlış ədəd: ".concat(e.divisor," ilə bölünə bilən olmalıdır");case"unrecognized_keys":return"Tanınmayan açar".concat(e.keys.length>1?"lar":"",": ").concat(y(e.keys,", "));case"invalid_key":return"".concat(e.origin," daxilində yanlış açar");case"invalid_union":default:return"Yanlış dəyər";case"invalid_element":return"".concat(e.origin," daxilində yanlış dəyər")}}})()}}function tb(e,t,n,r){let i=Math.abs(e),a=i%10,o=i%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?n:r}function tk(){return{localeError:(()=>{let e={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return e=>{var r,i,a;switch(e.code){case"invalid_type":return"Няправільны ўвод: чакаўся ".concat(e.expected,", атрымана ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"лік";case"object":if(Array.isArray(e))return"масіў";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Няправільны ўвод: чакалася ".concat(G(e.values[0]));return"Няправільны варыянт: чакаўся адзін з ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",a=t(e.origin);if(a){let t=tb(Number(e.maximum),a.unit.one,a.unit.few,a.unit.many);return"Занадта вялікі: чакалася, што ".concat(null!=(r=e.origin)?r:"значэнне"," павінна ").concat(a.verb," ").concat(n).concat(e.maximum.toString()," ").concat(t)}return"Занадта вялікі: чакалася, што ".concat(null!=(i=e.origin)?i:"значэнне"," павінна быць ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r){let t=tb(Number(e.minimum),r.unit.one,r.unit.few,r.unit.many);return"Занадта малы: чакалася, што ".concat(e.origin," павінна ").concat(r.verb," ").concat(n).concat(e.minimum.toString()," ").concat(t)}return"Занадта малы: чакалася, што ".concat(e.origin," павінна быць ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Няправільны радок: павінен пачынацца з "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Няправільны радок: павінен заканчвацца на "'.concat(e.suffix,'"');if("includes"===e.format)return'Няправільны радок: павінен змяшчаць "'.concat(e.includes,'"');if("regex"===e.format)return"Няправільны радок: павінен адпавядаць шаблону ".concat(e.pattern);return"Няправільны ".concat(null!=(a=n[e.format])?a:e.format);case"not_multiple_of":return"Няправільны лік: павінен быць кратным ".concat(e.divisor);case"unrecognized_keys":return"Нераспазнаны ".concat(e.keys.length>1?"ключы":"ключ",": ").concat(y(e.keys,", "));case"invalid_key":return"Няправільны ключ у ".concat(e.origin);case"invalid_union":default:return"Няправільны ўвод";case"invalid_element":return"Няправільнае значэнне ў ".concat(e.origin)}}})()}}function tx(){return{localeError:(()=>{let e={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Tipus invàlid: s'esperava ".concat(e.expected,", s'ha rebut ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Valor invàlid: s'esperava ".concat(G(e.values[0]));return"Opció invàlida: s'esperava una de ".concat(y(e.values," o "));case"too_big":{let n=e.inclusive?"com a màxim":"menys de",o=t(e.origin);if(o)return"Massa gran: s'esperava que ".concat(null!=(r=e.origin)?r:"el valor"," contingués ").concat(n," ").concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elements");return"Massa gran: s'esperava que ".concat(null!=(a=e.origin)?a:"el valor"," fos ").concat(n," ").concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?"com a mínim":"més de",r=t(e.origin);if(r)return"Massa petit: s'esperava que ".concat(e.origin," contingués ").concat(n," ").concat(e.minimum.toString()," ").concat(r.unit);return"Massa petit: s'esperava que ".concat(e.origin," fos ").concat(n," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Format invàlid: ha de començar amb "'.concat(e.prefix,'"');if("ends_with"===e.format)return"Format invàlid: ha d'acabar amb \"".concat(e.suffix,'"');if("includes"===e.format)return"Format invàlid: ha d'incloure \"".concat(e.includes,'"');if("regex"===e.format)return"Format invàlid: ha de coincidir amb el patró ".concat(e.pattern);return"Format invàlid per a ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Número invàlid: ha de ser múltiple de ".concat(e.divisor);case"unrecognized_keys":return"Clau".concat(e.keys.length>1?"s":""," no reconeguda").concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Clau invàlida a ".concat(e.origin);case"invalid_union":default:return"Entrada invàlida";case"invalid_element":return"Element invàlid a ".concat(e.origin)}}})()}}function tI(){return{localeError:(()=>{let e={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return e=>{var r,i,a,o,c,u,l;switch(e.code){case"invalid_type":return"Neplatný vstup: očekáváno ".concat(e.expected,", obdrženo ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(e))return"pole";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Neplatný vstup: očekáváno ".concat(G(e.values[0]));return"Neplatná možnost: očekávána jedna z hodnot ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Hodnota je příliš velká: ".concat(null!=(r=e.origin)?r:"hodnota"," musí mít ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"prvků");return"Hodnota je příliš velká: ".concat(null!=(a=e.origin)?a:"hodnota"," musí být ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Hodnota je příliš malá: ".concat(null!=(o=e.origin)?o:"hodnota"," musí mít ").concat(n).concat(e.minimum.toString()," ").concat(null!=(c=r.unit)?c:"prvků");return"Hodnota je příliš malá: ".concat(null!=(u=e.origin)?u:"hodnota"," musí být ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Neplatný řetězec: musí začínat na "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Neplatný řetězec: musí končit na "'.concat(e.suffix,'"');if("includes"===e.format)return'Neplatný řetězec: musí obsahovat "'.concat(e.includes,'"');if("regex"===e.format)return"Neplatný řetězec: musí odpovídat vzoru ".concat(e.pattern);return"Neplatný formát ".concat(null!=(l=n[e.format])?l:e.format);case"not_multiple_of":return"Neplatné číslo: musí být násobkem ".concat(e.divisor);case"unrecognized_keys":return"Neznámé klíče: ".concat(y(e.keys,", "));case"invalid_key":return"Neplatný klíč v ".concat(e.origin);case"invalid_union":default:return"Neplatný vstup";case"invalid_element":return"Neplatná hodnota v ".concat(e.origin)}}})()}}function tw(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},t={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"};function n(t){var n;return null!=(n=e[t])?n:null}function r(e){var n;return null!=(n=t[e])?n:e}let i={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return e=>{var t,a;switch(e.code){case"invalid_type":return"Ugyldigt input: forventede ".concat(r(e.expected),", fik ").concat(r((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tal";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name;return"objekt"}return t})(e.input)));case"invalid_value":if(1===e.values.length)return"Ugyldig værdi: forventede ".concat(G(e.values[0]));return"Ugyldigt valg: forventede en af følgende ".concat(y(e.values,"|"));case"too_big":{let i=e.inclusive?"<=":"<",a=n(e.origin),o=r(e.origin);if(a)return"For stor: forventede ".concat(null!=o?o:"value"," ").concat(a.verb," ").concat(i," ").concat(e.maximum.toString()," ").concat(null!=(t=a.unit)?t:"elementer");return"For stor: forventede ".concat(null!=o?o:"value"," havde ").concat(i," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",i=n(e.origin),a=r(e.origin);if(i)return"For lille: forventede ".concat(a," ").concat(i.verb," ").concat(t," ").concat(e.minimum.toString()," ").concat(i.unit);return"For lille: forventede ".concat(a," havde ").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ugyldig streng: skal starte med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ugyldig streng: skal ende med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ugyldig streng: skal indeholde "'.concat(e.includes,'"');if("regex"===e.format)return"Ugyldig streng: skal matche mønsteret ".concat(e.pattern);return"Ugyldig ".concat(null!=(a=i[e.format])?a:e.format);case"not_multiple_of":return"Ugyldigt tal: skal være deleligt med ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Ukendte nøgler":"Ukendt nøgle",": ").concat(y(e.keys,", "));case"invalid_key":return"Ugyldig nøgle i ".concat(e.origin);case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return"Ugyldig værdi i ".concat(e.origin);default:return"Ugyldigt input"}}})()}}function tz(){return{localeError:(()=>{let e={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Ungültige Eingabe: erwartet ".concat(e.expected,", erhalten ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"Zahl";case"object":if(Array.isArray(e))return"Array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ungültige Eingabe: erwartet ".concat(G(e.values[0]));return"Ungültige Option: erwartet eine von ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Zu groß: erwartet, dass ".concat(null!=(r=e.origin)?r:"Wert"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"Elemente"," hat");return"Zu groß: erwartet, dass ".concat(null!=(a=e.origin)?a:"Wert"," ").concat(n).concat(e.maximum.toString()," ist")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Zu klein: erwartet, dass ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," hat");return"Zu klein: erwartet, dass ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ist")}case"invalid_format":if("starts_with"===e.format)return'Ungültiger String: muss mit "'.concat(e.prefix,'" beginnen');if("ends_with"===e.format)return'Ungültiger String: muss mit "'.concat(e.suffix,'" enden');if("includes"===e.format)return'Ungültiger String: muss "'.concat(e.includes,'" enthalten');if("regex"===e.format)return"Ungültiger String: muss dem Muster ".concat(e.pattern," entsprechen");return"Ungültig: ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Ungültige Zahl: muss ein Vielfaches von ".concat(e.divisor," sein");case"unrecognized_keys":return"".concat(e.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel",": ").concat(y(e.keys,", "));case"invalid_key":return"Ungültiger Schlüssel in ".concat(e.origin);case"invalid_union":default:return"Ungültige Eingabe";case"invalid_element":return"Ungültiger Wert in ".concat(e.origin)}}})()}}function tS(){return{localeError:(()=>{let e={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Invalid input: expected ".concat(e.expected,", received ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Invalid input: expected ".concat(G(e.values[0]));return"Invalid option: expected one of ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Too big: expected ".concat(null!=(r=e.origin)?r:"value"," to have ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elements");return"Too big: expected ".concat(null!=(a=e.origin)?a:"value"," to be ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Too small: expected ".concat(e.origin," to have ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Too small: expected ".concat(e.origin," to be ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Invalid string: must start with "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Invalid string: must end with "'.concat(e.suffix,'"');if("includes"===e.format)return'Invalid string: must include "'.concat(e.includes,'"');if("regex"===e.format)return"Invalid string: must match pattern ".concat(e.pattern);return"Invalid ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Invalid number: must be a multiple of ".concat(e.divisor);case"unrecognized_keys":return"Unrecognized key".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Invalid key in ".concat(e.origin);case"invalid_union":default:return"Invalid input";case"invalid_element":return"Invalid value in ".concat(e.origin)}}})()}}function tj(){return{localeError:(()=>{let e={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Nevalida enigo: atendiĝis ".concat(e.expected,", riceviĝis ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombro";case"object":if(Array.isArray(e))return"tabelo";if(null===e)return"senvalora";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Nevalida enigo: atendiĝis ".concat(G(e.values[0]));return"Nevalida opcio: atendiĝis unu el ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Tro granda: atendiĝis ke ".concat(null!=(r=e.origin)?r:"valoro"," havu ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementojn");return"Tro granda: atendiĝis ke ".concat(null!=(a=e.origin)?a:"valoro"," havu ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Tro malgranda: atendiĝis ke ".concat(e.origin," havu ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Tro malgranda: atendiĝis ke ".concat(e.origin," estu ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Nevalida karaktraro: devas komenciĝi per "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Nevalida karaktraro: devas finiĝi per "'.concat(e.suffix,'"');if("includes"===e.format)return'Nevalida karaktraro: devas inkluzivi "'.concat(e.includes,'"');if("regex"===e.format)return"Nevalida karaktraro: devas kongrui kun la modelo ".concat(e.pattern);return"Nevalida ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Nevalida nombro: devas esti oblo de ".concat(e.divisor);case"unrecognized_keys":return"Nekonata".concat(e.keys.length>1?"j":""," ŝlosilo").concat(e.keys.length>1?"j":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Nevalida ŝlosilo en ".concat(e.origin);case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return"Nevalida valoro en ".concat(e.origin)}}})()}}function tZ(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Entrada inválida: se esperaba ".concat(e.expected,", recibido ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"arreglo";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrada inválida: se esperaba ".concat(G(e.values[0]));return"Opción inválida: se esperaba una de ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Demasiado grande: se esperaba que ".concat(null!=(r=e.origin)?r:"valor"," tuviera ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementos");return"Demasiado grande: se esperaba que ".concat(null!=(a=e.origin)?a:"valor"," fuera ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Demasiado pequeño: se esperaba que ".concat(e.origin," tuviera ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Demasiado pequeño: se esperaba que ".concat(e.origin," fuera ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Cadena inválida: debe comenzar con "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Cadena inválida: debe terminar en "'.concat(e.suffix,'"');if("includes"===e.format)return'Cadena inválida: debe incluir "'.concat(e.includes,'"');if("regex"===e.format)return"Cadena inválida: debe coincidir con el patrón ".concat(e.pattern);return"Inválido ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Número inválido: debe ser múltiplo de ".concat(e.divisor);case"unrecognized_keys":return"Llave".concat(e.keys.length>1?"s":""," desconocida").concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Llave inválida en ".concat(e.origin);case"invalid_union":default:return"Entrada inválida";case"invalid_element":return"Valor inválido en ".concat(e.origin)}}})()}}function tU(){return{localeError:(()=>{let e={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"ورودی نامعتبر: می‌بایست ".concat(e.expected," می‌بود، ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"آرایه";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," دریافت شد");case"invalid_value":if(1===e.values.length)return"ورودی نامعتبر: می‌بایست ".concat(G(e.values[0])," می‌بود");return"گزینه نامعتبر: می‌بایست یکی از ".concat(y(e.values,"|")," می‌بود");case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"خیلی بزرگ: ".concat(null!=(r=e.origin)?r:"مقدار"," باید ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"عنصر"," باشد");return"خیلی بزرگ: ".concat(null!=(a=e.origin)?a:"مقدار"," باید ").concat(n).concat(e.maximum.toString()," باشد")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"خیلی کوچک: ".concat(e.origin," باید ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," باشد");return"خیلی کوچک: ".concat(e.origin," باید ").concat(n).concat(e.minimum.toString()," باشد")}case"invalid_format":if("starts_with"===e.format)return'رشته نامعتبر: باید با "'.concat(e.prefix,'" شروع شود');if("ends_with"===e.format)return'رشته نامعتبر: باید با "'.concat(e.suffix,'" تمام شود');if("includes"===e.format)return'رشته نامعتبر: باید شامل "'.concat(e.includes,'" باشد');if("regex"===e.format)return"رشته نامعتبر: باید با الگوی ".concat(e.pattern," مطابقت داشته باشد");return"".concat(null!=(o=n[e.format])?o:e.format," نامعتبر");case"not_multiple_of":return"عدد نامعتبر: باید مضرب ".concat(e.divisor," باشد");case"unrecognized_keys":return"کلید".concat(e.keys.length>1?"های":""," ناشناس: ").concat(y(e.keys,", "));case"invalid_key":return"کلید ناشناس در ".concat(e.origin);case"invalid_union":default:return"ورودی نامعتبر";case"invalid_element":return"مقدار نامعتبر در ".concat(e.origin)}}})()}}function tO(){return{localeError:(()=>{let e={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return e=>{switch(e.code){case"invalid_type":return"Virheellinen tyyppi: odotettiin ".concat(e.expected,", oli ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Virheellinen syöte: täytyy olla ".concat(G(e.values[0]));return"Virheellinen valinta: täytyy olla yksi seuraavista: ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",r=t(e.origin);if(r)return"Liian suuri: ".concat(r.subject," täytyy olla ").concat(n).concat(e.maximum.toString()," ").concat(r.unit).trim();return"Liian suuri: arvon täytyy olla ".concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Liian pieni: ".concat(r.subject," täytyy olla ").concat(n).concat(e.minimum.toString()," ").concat(r.unit).trim();return"Liian pieni: arvon täytyy olla ".concat(n).concat(e.minimum.toString())}case"invalid_format":var r;if("starts_with"===e.format)return'Virheellinen syöte: täytyy alkaa "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Virheellinen syöte: täytyy loppua "'.concat(e.suffix,'"');if("includes"===e.format)return'Virheellinen syöte: täytyy sisältää "'.concat(e.includes,'"');if("regex"===e.format)return"Virheellinen syöte: täytyy vastata säännöllistä lauseketta ".concat(e.pattern);return"Virheellinen ".concat(null!=(r=n[e.format])?r:e.format);case"not_multiple_of":return"Virheellinen luku: täytyy olla luvun ".concat(e.divisor," monikerta");case"unrecognized_keys":return"".concat(e.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain",": ").concat(y(e.keys,", "));case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return"Virheellinen syöte"}}})()}}function tN(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Entrée invalide : ".concat(e.expected," attendu, ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombre";case"object":if(Array.isArray(e))return"tableau";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," reçu");case"invalid_value":if(1===e.values.length)return"Entrée invalide : ".concat(G(e.values[0])," attendu");return"Option invalide : une valeur parmi ".concat(y(e.values,"|")," attendue");case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Trop grand : ".concat(null!=(r=e.origin)?r:"valeur"," doit ").concat(o.verb," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"élément(s)");return"Trop grand : ".concat(null!=(a=e.origin)?a:"valeur"," doit être ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Trop petit : ".concat(e.origin," doit ").concat(r.verb," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Trop petit : ".concat(e.origin," doit être ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chaîne invalide : doit commencer par "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chaîne invalide : doit se terminer par "'.concat(e.suffix,'"');if("includes"===e.format)return'Chaîne invalide : doit inclure "'.concat(e.includes,'"');if("regex"===e.format)return"Chaîne invalide : doit correspondre au modèle ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," invalide");case"not_multiple_of":return"Nombre invalide : doit être un multiple de ".concat(e.divisor);case"unrecognized_keys":return"Clé".concat(e.keys.length>1?"s":""," non reconnue").concat(e.keys.length>1?"s":""," : ").concat(y(e.keys,", "));case"invalid_key":return"Clé invalide dans ".concat(e.origin);case"invalid_union":default:return"Entrée invalide";case"invalid_element":return"Valeur invalide dans ".concat(e.origin)}}})()}}function tD(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{var r,i,a;switch(e.code){case"invalid_type":return"Entrée invalide : attendu ".concat(e.expected,", reçu ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrée invalide : attendu ".concat(G(e.values[0]));return"Option invalide : attendu l'une des valeurs suivantes ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"≤":"<",a=t(e.origin);if(a)return"Trop grand : attendu que ".concat(null!=(r=e.origin)?r:"la valeur"," ait ").concat(n).concat(e.maximum.toString()," ").concat(a.unit);return"Trop grand : attendu que ".concat(null!=(i=e.origin)?i:"la valeur"," soit ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?"≥":">",r=t(e.origin);if(r)return"Trop petit : attendu que ".concat(e.origin," ait ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Trop petit : attendu que ".concat(e.origin," soit ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chaîne invalide : doit commencer par "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chaîne invalide : doit se terminer par "'.concat(e.suffix,'"');if("includes"===e.format)return'Chaîne invalide : doit inclure "'.concat(e.includes,'"');if("regex"===e.format)return"Chaîne invalide : doit correspondre au motif ".concat(e.pattern);return"".concat(null!=(a=n[e.format])?a:e.format," invalide");case"not_multiple_of":return"Nombre invalide : doit être un multiple de ".concat(e.divisor);case"unrecognized_keys":return"Clé".concat(e.keys.length>1?"s":""," non reconnue").concat(e.keys.length>1?"s":""," : ").concat(y(e.keys,", "));case"invalid_key":return"Clé invalide dans ".concat(e.origin);case"invalid_union":default:return"Entrée invalide";case"invalid_element":return"Valeur invalide dans ".concat(e.origin)}}})()}}function t$(){return{localeError:(()=>{let e={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"קלט לא תקין: צריך ".concat(e.expected,", התקבל ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"קלט לא תקין: צריך ".concat(G(e.values[0]));return"קלט לא תקין: צריך אחת מהאפשרויות  ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"גדול מדי: ".concat(null!=(r=e.origin)?r:"value"," צריך להיות ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elements");return"גדול מדי: ".concat(null!=(a=e.origin)?a:"value"," צריך להיות ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"קטן מדי: ".concat(e.origin," צריך להיות ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"קטן מדי: ".concat(e.origin," צריך להיות ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'מחרוזת לא תקינה: חייבת להתחיל ב"'.concat(e.prefix,'"');if("ends_with"===e.format)return'מחרוזת לא תקינה: חייבת להסתיים ב "'.concat(e.suffix,'"');if("includes"===e.format)return'מחרוזת לא תקינה: חייבת לכלול "'.concat(e.includes,'"');if("regex"===e.format)return"מחרוזת לא תקינה: חייבת להתאים לתבנית ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," לא תקין");case"not_multiple_of":return"מספר לא תקין: חייב להיות מכפלה של ".concat(e.divisor);case"unrecognized_keys":return"מפתח".concat(e.keys.length>1?"ות":""," לא מזוה").concat(e.keys.length>1?"ים":"ה",": ").concat(y(e.keys,", "));case"invalid_key":return"מפתח לא תקין ב".concat(e.origin);case"invalid_union":default:return"קלט לא תקין";case"invalid_element":return"ערך לא תקין ב".concat(e.origin)}}})()}}function tP(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Érvénytelen bemenet: a várt érték ".concat(e.expected,", a kapott érték ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"szám";case"object":if(Array.isArray(e))return"tömb";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Érvénytelen bemenet: a várt érték ".concat(G(e.values[0]));return"Érvénytelen opció: valamelyik érték várt ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Túl nagy: ".concat(null!=(r=e.origin)?r:"érték"," mérete túl nagy ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elem");return"Túl nagy: a bemeneti érték ".concat(null!=(a=e.origin)?a:"érték"," túl nagy: ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Túl kicsi: a bemeneti érték ".concat(e.origin," mérete túl kicsi ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Túl kicsi: a bemeneti érték ".concat(e.origin," túl kicsi ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Érvénytelen string: "'.concat(e.prefix,'" értékkel kell kezdődnie');if("ends_with"===e.format)return'Érvénytelen string: "'.concat(e.suffix,'" értékkel kell végződnie');if("includes"===e.format)return'Érvénytelen string: "'.concat(e.includes,'" értéket kell tartalmaznia');if("regex"===e.format)return"Érvénytelen string: ".concat(e.pattern," mintának kell megfelelnie");return"Érvénytelen ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Érvénytelen szám: ".concat(e.divisor," többszörösének kell lennie");case"unrecognized_keys":return"Ismeretlen kulcs".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Érvénytelen kulcs ".concat(e.origin);case"invalid_union":default:return"Érvénytelen bemenet";case"invalid_element":return"Érvénytelen érték: ".concat(e.origin)}}})()}}function tE(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Input tidak valid: diharapkan ".concat(e.expected,", diterima ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input tidak valid: diharapkan ".concat(G(e.values[0]));return"Pilihan tidak valid: diharapkan salah satu dari ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Terlalu besar: diharapkan ".concat(null!=(r=e.origin)?r:"value"," memiliki ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elemen");return"Terlalu besar: diharapkan ".concat(null!=(a=e.origin)?a:"value"," menjadi ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Terlalu kecil: diharapkan ".concat(e.origin," memiliki ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Terlalu kecil: diharapkan ".concat(e.origin," menjadi ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'String tidak valid: harus dimulai dengan "'.concat(e.prefix,'"');if("ends_with"===e.format)return'String tidak valid: harus berakhir dengan "'.concat(e.suffix,'"');if("includes"===e.format)return'String tidak valid: harus menyertakan "'.concat(e.includes,'"');if("regex"===e.format)return"String tidak valid: harus sesuai pola ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," tidak valid");case"not_multiple_of":return"Angka tidak valid: harus kelipatan dari ".concat(e.divisor);case"unrecognized_keys":return"Kunci tidak dikenali ".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Kunci tidak valid di ".concat(e.origin);case"invalid_union":default:return"Input tidak valid";case"invalid_element":return"Nilai tidak valid di ".concat(e.origin)}}})()}}function tA(){return{localeError:(()=>{let e={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Rangt gildi: Þú slóst inn ".concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"númer";case"object":if(Array.isArray(e))return"fylki";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," þar sem á að vera ").concat(e.expected);case"invalid_value":if(1===e.values.length)return"Rangt gildi: gert ráð fyrir ".concat(G(e.values[0]));return"Ógilt val: má vera eitt af eftirfarandi ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Of stórt: gert er ráð fyrir að ".concat(null!=(r=e.origin)?r:"gildi"," hafi ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"hluti");return"Of stórt: gert er ráð fyrir að ".concat(null!=(a=e.origin)?a:"gildi"," sé ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Of lítið: gert er ráð fyrir að ".concat(e.origin," hafi ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Of lítið: gert er ráð fyrir að ".concat(e.origin," sé ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ógildur strengur: verður að byrja á "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ógildur strengur: verður að enda á "'.concat(e.suffix,'"');if("includes"===e.format)return'Ógildur strengur: verður að innihalda "'.concat(e.includes,'"');if("regex"===e.format)return"Ógildur strengur: verður að fylgja mynstri ".concat(e.pattern);return"Rangt ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Röng tala: verður að vera margfeldi af ".concat(e.divisor);case"unrecognized_keys":return"Óþekkt ".concat(e.keys.length>1?"ir lyklar":"ur lykill",": ").concat(y(e.keys,", "));case"invalid_key":return"Rangur lykill í ".concat(e.origin);case"invalid_union":default:return"Rangt gildi";case"invalid_element":return"Rangt gildi í ".concat(e.origin)}}})()}}function tT(){return{localeError:(()=>{let e={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Input non valido: atteso ".concat(e.expected,", ricevuto ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numero";case"object":if(Array.isArray(e))return"vettore";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input non valido: atteso ".concat(G(e.values[0]));return"Opzione non valida: atteso uno tra ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Troppo grande: ".concat(null!=(r=e.origin)?r:"valore"," deve avere ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementi");return"Troppo grande: ".concat(null!=(a=e.origin)?a:"valore"," deve essere ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Troppo piccolo: ".concat(e.origin," deve avere ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Troppo piccolo: ".concat(e.origin," deve essere ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Stringa non valida: deve iniziare con "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Stringa non valida: deve terminare con "'.concat(e.suffix,'"');if("includes"===e.format)return'Stringa non valida: deve includere "'.concat(e.includes,'"');if("regex"===e.format)return"Stringa non valida: deve corrispondere al pattern ".concat(e.pattern);return"Invalid ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Numero non valido: deve essere un multiplo di ".concat(e.divisor);case"unrecognized_keys":return"Chiav".concat(e.keys.length>1?"i":"e"," non riconosciut").concat(e.keys.length>1?"e":"a",": ").concat(y(e.keys,", "));case"invalid_key":return"Chiave non valida in ".concat(e.origin);case"invalid_union":default:return"Input non valido";case"invalid_element":return"Valore non valido in ".concat(e.origin)}}})()}}function tC(){return{localeError:(()=>{let e={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"無効な入力: ".concat(e.expected,"が期待されましたが、").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"数値";case"object":if(Array.isArray(e))return"配列";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input),"が入力されました");case"invalid_value":if(1===e.values.length)return"無効な入力: ".concat(G(e.values[0]),"が期待されました");return"無効な選択: ".concat(y(e.values,"、"),"のいずれかである必要があります");case"too_big":{let n=e.inclusive?"以下である":"より小さい",o=t(e.origin);if(o)return"大きすぎる値: ".concat(null!=(r=e.origin)?r:"値","は").concat(e.maximum.toString()).concat(null!=(i=o.unit)?i:"要素").concat(n,"必要があります");return"大きすぎる値: ".concat(null!=(a=e.origin)?a:"値","は").concat(e.maximum.toString()).concat(n,"必要があります")}case"too_small":{let n=e.inclusive?"以上である":"より大きい",r=t(e.origin);if(r)return"小さすぎる値: ".concat(e.origin,"は").concat(e.minimum.toString()).concat(r.unit).concat(n,"必要があります");return"小さすぎる値: ".concat(e.origin,"は").concat(e.minimum.toString()).concat(n,"必要があります")}case"invalid_format":if("starts_with"===e.format)return'無効な文字列: "'.concat(e.prefix,'"で始まる必要があります');if("ends_with"===e.format)return'無効な文字列: "'.concat(e.suffix,'"で終わる必要があります');if("includes"===e.format)return'無効な文字列: "'.concat(e.includes,'"を含む必要があります');if("regex"===e.format)return"無効な文字列: パターン".concat(e.pattern,"に一致する必要があります");return"無効な".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"無効な数値: ".concat(e.divisor,"の倍数である必要があります");case"unrecognized_keys":return"認識されていないキー".concat(e.keys.length>1?"群":"",": ").concat(y(e.keys,"、"));case"invalid_key":return"".concat(e.origin,"内の無効なキー");case"invalid_union":default:return"無効な入力";case"invalid_element":return"".concat(e.origin,"内の無効な値")}}})()}}function tL(){return{localeError:(()=>{let e={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ".concat(e.expected," ប៉ុន្តែទទួលបាន ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(e))return"អារេ (Array)";if(null===e)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ".concat(G(e.values[0]));return"ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"ធំពេក៖ ត្រូវការ ".concat(null!=(r=e.origin)?r:"តម្លៃ"," ").concat(n," ").concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"ធាតុ");return"ធំពេក៖ ត្រូវការ ".concat(null!=(a=e.origin)?a:"តម្លៃ"," ").concat(n," ").concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"តូចពេក៖ ត្រូវការ ".concat(e.origin," ").concat(n," ").concat(e.minimum.toString()," ").concat(r.unit);return"តូចពេក៖ ត្រូវការ ".concat(e.origin," ").concat(n," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "'.concat(e.prefix,'"');if("ends_with"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "'.concat(e.suffix,'"');if("includes"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "'.concat(e.includes,'"');if("regex"===e.format)return"ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ".concat(e.pattern);return"មិនត្រឹមត្រូវ៖ ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ".concat(e.divisor);case"unrecognized_keys":return"រកឃើញសោមិនស្គាល់៖ ".concat(y(e.keys,", "));case"invalid_key":return"សោមិនត្រឹមត្រូវនៅក្នុង ".concat(e.origin);case"invalid_union":default:return"ទិន្នន័យមិនត្រឹមត្រូវ";case"invalid_element":return"ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ".concat(e.origin)}}})()}}function tR(){return{localeError:(()=>{let e={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return e=>{var r,i,a,o,c,u,l;switch(e.code){case"invalid_type":return"잘못된 입력: 예상 타입은 ".concat(e.expected,", 받은 타입은 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input),"입니다");case"invalid_value":if(1===e.values.length)return"잘못된 입력: 값은 ".concat(G(e.values[0])," 이어야 합니다");return"잘못된 옵션: ".concat(y(e.values,"또는 ")," 중 하나여야 합니다");case"too_big":{let n=e.inclusive?"이하":"미만",o="미만"===n?"이어야 합니다":"여야 합니다",c=t(e.origin),u=null!=(r=null==c?void 0:c.unit)?r:"요소";if(c)return"".concat(null!=(i=e.origin)?i:"값","이 너무 큽니다: ").concat(e.maximum.toString()).concat(u," ").concat(n).concat(o);return"".concat(null!=(a=e.origin)?a:"값","이 너무 큽니다: ").concat(e.maximum.toString()," ").concat(n).concat(o)}case"too_small":{let n=e.inclusive?"이상":"초과",r="이상"===n?"이어야 합니다":"여야 합니다",i=t(e.origin),a=null!=(o=null==i?void 0:i.unit)?o:"요소";if(i)return"".concat(null!=(c=e.origin)?c:"값","이 너무 작습니다: ").concat(e.minimum.toString()).concat(a," ").concat(n).concat(r);return"".concat(null!=(u=e.origin)?u:"값","이 너무 작습니다: ").concat(e.minimum.toString()," ").concat(n).concat(r)}case"invalid_format":if("starts_with"===e.format)return'잘못된 문자열: "'.concat(e.prefix,'"(으)로 시작해야 합니다');if("ends_with"===e.format)return'잘못된 문자열: "'.concat(e.suffix,'"(으)로 끝나야 합니다');if("includes"===e.format)return'잘못된 문자열: "'.concat(e.includes,'"을(를) 포함해야 합니다');if("regex"===e.format)return"잘못된 문자열: 정규식 ".concat(e.pattern," 패턴과 일치해야 합니다");return"잘못된 ".concat(null!=(l=n[e.format])?l:e.format);case"not_multiple_of":return"잘못된 숫자: ".concat(e.divisor,"의 배수여야 합니다");case"unrecognized_keys":return"인식할 수 없는 키: ".concat(y(e.keys,", "));case"invalid_key":return"잘못된 키: ".concat(e.origin);case"invalid_union":default:return"잘못된 입력";case"invalid_element":return"잘못된 값: ".concat(e.origin)}}})()}}function tM(){return{localeError:(()=>{let e={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Грешен внес: се очекува ".concat(e.expected,", примено ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"број";case"object":if(Array.isArray(e))return"низа";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Invalid input: expected ".concat(G(e.values[0]));return"Грешана опција: се очекува една ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Премногу голем: се очекува ".concat(null!=(r=e.origin)?r:"вредноста"," да има ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"елементи");return"Премногу голем: се очекува ".concat(null!=(a=e.origin)?a:"вредноста"," да биде ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Премногу мал: се очекува ".concat(e.origin," да има ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Премногу мал: се очекува ".concat(e.origin," да биде ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неважечка низа: мора да започнува со "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неважечка низа: мора да завршува со "'.concat(e.suffix,'"');if("includes"===e.format)return'Неважечка низа: мора да вклучува "'.concat(e.includes,'"');if("regex"===e.format)return"Неважечка низа: мора да одгоара на патернот ".concat(e.pattern);return"Invalid ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Грешен број: мора да биде делив со ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч",": ").concat(y(e.keys,", "));case"invalid_key":return"Грешен клуч во ".concat(e.origin);case"invalid_union":default:return"Грешен внес";case"invalid_element":return"Грешна вредност во ".concat(e.origin)}}})()}}function tF(){return{localeError:(()=>{let e={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Input tidak sah: dijangka ".concat(e.expected,", diterima ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombor";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input tidak sah: dijangka ".concat(G(e.values[0]));return"Pilihan tidak sah: dijangka salah satu daripada ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Terlalu besar: dijangka ".concat(null!=(r=e.origin)?r:"nilai"," ").concat(o.verb," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elemen");return"Terlalu besar: dijangka ".concat(null!=(a=e.origin)?a:"nilai"," adalah ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Terlalu kecil: dijangka ".concat(e.origin," ").concat(r.verb," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Terlalu kecil: dijangka ".concat(e.origin," adalah ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'String tidak sah: mesti bermula dengan "'.concat(e.prefix,'"');if("ends_with"===e.format)return'String tidak sah: mesti berakhir dengan "'.concat(e.suffix,'"');if("includes"===e.format)return'String tidak sah: mesti mengandungi "'.concat(e.includes,'"');if("regex"===e.format)return"String tidak sah: mesti sepadan dengan corak ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," tidak sah");case"not_multiple_of":return"Nombor tidak sah: perlu gandaan ".concat(e.divisor);case"unrecognized_keys":return"Kunci tidak dikenali: ".concat(y(e.keys,", "));case"invalid_key":return"Kunci tidak sah dalam ".concat(e.origin);case"invalid_union":default:return"Input tidak sah";case"invalid_element":return"Nilai tidak sah dalam ".concat(e.origin)}}})()}}function tJ(){return{localeError:(()=>{let e={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Ongeldige invoer: verwacht ".concat(e.expected,", ontving ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"getal";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ongeldige invoer: verwacht ".concat(G(e.values[0]));return"Ongeldige optie: verwacht één van ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Te lang: verwacht dat ".concat(null!=(r=e.origin)?r:"waarde"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementen"," bevat");return"Te lang: verwacht dat ".concat(null!=(a=e.origin)?a:"waarde"," ").concat(n).concat(e.maximum.toString()," is")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Te kort: verwacht dat ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," bevat");return"Te kort: verwacht dat ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," is")}case"invalid_format":if("starts_with"===e.format)return'Ongeldige tekst: moet met "'.concat(e.prefix,'" beginnen');if("ends_with"===e.format)return'Ongeldige tekst: moet op "'.concat(e.suffix,'" eindigen');if("includes"===e.format)return'Ongeldige tekst: moet "'.concat(e.includes,'" bevatten');if("regex"===e.format)return"Ongeldige tekst: moet overeenkomen met patroon ".concat(e.pattern);return"Ongeldig: ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Ongeldig getal: moet een veelvoud van ".concat(e.divisor," zijn");case"unrecognized_keys":return"Onbekende key".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Ongeldige key in ".concat(e.origin);case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return"Ongeldige waarde in ".concat(e.origin)}}})()}}function tB(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Ugyldig input: forventet ".concat(e.expected,", fikk ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tall";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ugyldig verdi: forventet ".concat(G(e.values[0]));return"Ugyldig valg: forventet en av ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"For stor(t): forventet ".concat(null!=(r=e.origin)?r:"value"," til å ha ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementer");return"For stor(t): forventet ".concat(null!=(a=e.origin)?a:"value"," til å ha ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"For lite(n): forventet ".concat(e.origin," til å ha ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"For lite(n): forventet ".concat(e.origin," til å ha ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ugyldig streng: må starte med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ugyldig streng: må ende med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ugyldig streng: må inneholde "'.concat(e.includes,'"');if("regex"===e.format)return"Ugyldig streng: må matche mønsteret ".concat(e.pattern);return"Ugyldig ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Ugyldig tall: må være et multiplum av ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel",": ").concat(y(e.keys,", "));case"invalid_key":return"Ugyldig nøkkel i ".concat(e.origin);case"invalid_union":default:return"Ugyldig input";case"invalid_element":return"Ugyldig verdi i ".concat(e.origin)}}})()}}function tW(){return{localeError:(()=>{let e={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Fâsit giren: umulan ".concat(e.expected,", alınan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numara";case"object":if(Array.isArray(e))return"saf";if(null===e)return"gayb";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Fâsit giren: umulan ".concat(G(e.values[0]));return"Fâsit tercih: mûteberler ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Fazla büyük: ".concat(null!=(r=e.origin)?r:"value",", ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elements"," sahip olmalıydı.");return"Fazla büyük: ".concat(null!=(a=e.origin)?a:"value",", ").concat(n).concat(e.maximum.toString()," olmalıydı.")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Fazla küçük: ".concat(e.origin,", ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," sahip olmalıydı.");return"Fazla küçük: ".concat(e.origin,", ").concat(n).concat(e.minimum.toString()," olmalıydı.")}case"invalid_format":if("starts_with"===e.format)return'Fâsit metin: "'.concat(e.prefix,'" ile başlamalı.');if("ends_with"===e.format)return'Fâsit metin: "'.concat(e.suffix,'" ile bitmeli.');if("includes"===e.format)return'Fâsit metin: "'.concat(e.includes,'" ihtivâ etmeli.');if("regex"===e.format)return"Fâsit metin: ".concat(e.pattern," nakşına uymalı.");return"Fâsit ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Fâsit sayı: ".concat(e.divisor," katı olmalıydı.");case"unrecognized_keys":return"Tanınmayan anahtar ".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"".concat(e.origin," için tanınmayan anahtar var.");case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return"".concat(e.origin," için tanınmayan kıymet var.");default:return"Kıymet tanınamadı."}}})()}}function tV(){return{localeError:(()=>{let e={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"ناسم ورودي: باید ".concat(e.expected," وای, مګر ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"ارې";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," ترلاسه شو");case"invalid_value":if(1===e.values.length)return"ناسم ورودي: باید ".concat(G(e.values[0])," وای");return"ناسم انتخاب: باید یو له ".concat(y(e.values,"|")," څخه وای");case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"ډیر لوی: ".concat(null!=(r=e.origin)?r:"ارزښت"," باید ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"عنصرونه"," ولري");return"ډیر لوی: ".concat(null!=(a=e.origin)?a:"ارزښت"," باید ").concat(n).concat(e.maximum.toString()," وي")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"ډیر کوچنی: ".concat(e.origin," باید ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," ولري");return"ډیر کوچنی: ".concat(e.origin," باید ").concat(n).concat(e.minimum.toString()," وي")}case"invalid_format":if("starts_with"===e.format)return'ناسم متن: باید د "'.concat(e.prefix,'" سره پیل شي');if("ends_with"===e.format)return'ناسم متن: باید د "'.concat(e.suffix,'" سره پای ته ورسيږي');if("includes"===e.format)return'ناسم متن: باید "'.concat(e.includes,'" ولري');if("regex"===e.format)return"ناسم متن: باید د ".concat(e.pattern," سره مطابقت ولري");return"".concat(null!=(o=n[e.format])?o:e.format," ناسم دی");case"not_multiple_of":return"ناسم عدد: باید د ".concat(e.divisor," مضرب وي");case"unrecognized_keys":return"ناسم ".concat(e.keys.length>1?"کلیډونه":"کلیډ",": ").concat(y(e.keys,", "));case"invalid_key":return"ناسم کلیډ په ".concat(e.origin," کې");case"invalid_union":default:return"ناسمه ورودي";case"invalid_element":return"ناسم عنصر په ".concat(e.origin," کې")}}})()}}function tG(){return{localeError:(()=>{let e={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return e=>{var r,i,a,o,c,u,l;switch(e.code){case"invalid_type":return"Nieprawidłowe dane wejściowe: oczekiwano ".concat(e.expected,", otrzymano ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"liczba";case"object":if(Array.isArray(e))return"tablica";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Nieprawidłowe dane wejściowe: oczekiwano ".concat(G(e.values[0]));return"Nieprawidłowa opcja: oczekiwano jednej z wartości ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Za duża wartość: oczekiwano, że ".concat(null!=(r=e.origin)?r:"wartość"," będzie mieć ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementów");return"Zbyt duż(y/a/e): oczekiwano, że ".concat(null!=(a=e.origin)?a:"wartość"," będzie wynosić ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Za mała wartość: oczekiwano, że ".concat(null!=(o=e.origin)?o:"wartość"," będzie mieć ").concat(n).concat(e.minimum.toString()," ").concat(null!=(c=r.unit)?c:"elementów");return"Zbyt mał(y/a/e): oczekiwano, że ".concat(null!=(u=e.origin)?u:"wartość"," będzie wynosić ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Nieprawidłowy ciąg znaków: musi zaczynać się od "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Nieprawidłowy ciąg znaków: musi kończyć się na "'.concat(e.suffix,'"');if("includes"===e.format)return'Nieprawidłowy ciąg znaków: musi zawierać "'.concat(e.includes,'"');if("regex"===e.format)return"Nieprawidłowy ciąg znaków: musi odpowiadać wzorcowi ".concat(e.pattern);return"Nieprawidłow(y/a/e) ".concat(null!=(l=n[e.format])?l:e.format);case"not_multiple_of":return"Nieprawidłowa liczba: musi być wielokrotnością ".concat(e.divisor);case"unrecognized_keys":return"Nierozpoznane klucze".concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Nieprawidłowy klucz w ".concat(e.origin);case"invalid_union":default:return"Nieprawidłowe dane wejściowe";case"invalid_element":return"Nieprawidłowa wartość w ".concat(e.origin)}}})()}}function tK(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Tipo inválido: esperado ".concat(e.expected,", recebido ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"array";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrada inválida: esperado ".concat(G(e.values[0]));return"Opção inválida: esperada uma das ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Muito grande: esperado que ".concat(null!=(r=e.origin)?r:"valor"," tivesse ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementos");return"Muito grande: esperado que ".concat(null!=(a=e.origin)?a:"valor"," fosse ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Muito pequeno: esperado que ".concat(e.origin," tivesse ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Muito pequeno: esperado que ".concat(e.origin," fosse ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Texto inválido: deve começar com "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Texto inválido: deve terminar com "'.concat(e.suffix,'"');if("includes"===e.format)return'Texto inválido: deve incluir "'.concat(e.includes,'"');if("regex"===e.format)return"Texto inválido: deve corresponder ao padrão ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," inválido");case"not_multiple_of":return"Número inválido: deve ser múltiplo de ".concat(e.divisor);case"unrecognized_keys":return"Chave".concat(e.keys.length>1?"s":""," desconhecida").concat(e.keys.length>1?"s":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Chave inválida em ".concat(e.origin);case"invalid_union":return"Entrada inválida";case"invalid_element":return"Valor inválido em ".concat(e.origin);default:return"Campo inválido"}}})()}}function tX(e,t,n,r){let i=Math.abs(e),a=i%10,o=i%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?n:r}function tq(){return{localeError:(()=>{let e={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return e=>{var r,i,a;switch(e.code){case"invalid_type":return"Неверный ввод: ожидалось ".concat(e.expected,", получено ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"массив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Неверный ввод: ожидалось ".concat(G(e.values[0]));return"Неверный вариант: ожидалось одно из ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",a=t(e.origin);if(a){let t=tX(Number(e.maximum),a.unit.one,a.unit.few,a.unit.many);return"Слишком большое значение: ожидалось, что ".concat(null!=(r=e.origin)?r:"значение"," будет иметь ").concat(n).concat(e.maximum.toString()," ").concat(t)}return"Слишком большое значение: ожидалось, что ".concat(null!=(i=e.origin)?i:"значение"," будет ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r){let t=tX(Number(e.minimum),r.unit.one,r.unit.few,r.unit.many);return"Слишком маленькое значение: ожидалось, что ".concat(e.origin," будет иметь ").concat(n).concat(e.minimum.toString()," ").concat(t)}return"Слишком маленькое значение: ожидалось, что ".concat(e.origin," будет ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неверная строка: должна начинаться с "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неверная строка: должна заканчиваться на "'.concat(e.suffix,'"');if("includes"===e.format)return'Неверная строка: должна содержать "'.concat(e.includes,'"');if("regex"===e.format)return"Неверная строка: должна соответствовать шаблону ".concat(e.pattern);return"Неверный ".concat(null!=(a=n[e.format])?a:e.format);case"not_multiple_of":return"Неверное число: должно быть кратным ".concat(e.divisor);case"unrecognized_keys":return"Нераспознанн".concat(e.keys.length>1?"ые":"ый"," ключ").concat(e.keys.length>1?"и":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Неверный ключ в ".concat(e.origin);case"invalid_union":default:return"Неверные входные данные";case"invalid_element":return"Неверное значение в ".concat(e.origin)}}})()}}function tY(){return{localeError:(()=>{let e={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Neveljaven vnos: pričakovano ".concat(e.expected,", prejeto ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"število";case"object":if(Array.isArray(e))return"tabela";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Neveljaven vnos: pričakovano ".concat(G(e.values[0]));return"Neveljavna možnost: pričakovano eno izmed ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Preveliko: pričakovano, da bo ".concat(null!=(r=e.origin)?r:"vrednost"," imelo ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"elementov");return"Preveliko: pričakovano, da bo ".concat(null!=(a=e.origin)?a:"vrednost"," ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Premajhno: pričakovano, da bo ".concat(e.origin," imelo ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Premajhno: pričakovano, da bo ".concat(e.origin," ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Neveljaven niz: mora se začeti z "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Neveljaven niz: mora se končati z "'.concat(e.suffix,'"');if("includes"===e.format)return'Neveljaven niz: mora vsebovati "'.concat(e.includes,'"');if("regex"===e.format)return"Neveljaven niz: mora ustrezati vzorcu ".concat(e.pattern);return"Neveljaven ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Neveljavno število: mora biti večkratnik ".concat(e.divisor);case"unrecognized_keys":return"Neprepoznan".concat(e.keys.length>1?"i ključi":" ključ",": ").concat(y(e.keys,", "));case"invalid_key":return"Neveljaven ključ v ".concat(e.origin);case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return"Neveljavna vrednost v ".concat(e.origin)}}})()}}function tH(){return{localeError:(()=>{let e={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return e=>{var r,i,a,o,c,u,l,s;switch(e.code){case"invalid_type":return"Ogiltig inmatning: förväntat ".concat(e.expected,", fick ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"antal";case"object":if(Array.isArray(e))return"lista";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ogiltig inmatning: förväntat ".concat(G(e.values[0]));return"Ogiltigt val: förväntade en av ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"För stor(t): förväntade ".concat(null!=(r=e.origin)?r:"värdet"," att ha ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"element");return"För stor(t): förväntat ".concat(null!=(a=e.origin)?a:"värdet"," att ha ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"För lite(t): förväntade ".concat(null!=(o=e.origin)?o:"värdet"," att ha ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"För lite(t): förväntade ".concat(null!=(c=e.origin)?c:"värdet"," att ha ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ogiltig sträng: måste börja med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ogiltig sträng: måste sluta med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ogiltig sträng: måste innehålla "'.concat(e.includes,'"');if("regex"===e.format)return'Ogiltig sträng: måste matcha mönstret "'.concat(e.pattern,'"');return"Ogiltig(t) ".concat(null!=(u=n[e.format])?u:e.format);case"not_multiple_of":return"Ogiltigt tal: måste vara en multipel av ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Okända nycklar":"Okänd nyckel",": ").concat(y(e.keys,", "));case"invalid_key":return"Ogiltig nyckel i ".concat(null!=(l=e.origin)?l:"värdet");case"invalid_union":default:return"Ogiltig input";case"invalid_element":return"Ogiltigt värde i ".concat(null!=(s=e.origin)?s:"värdet")}}})()}}function tQ(){return{localeError:(()=>{let e={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ".concat(e.expected,", பெறப்பட்டது ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(e))return"அணி";if(null===e)return"வெறுமை";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ".concat(G(e.values[0]));return"தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ".concat(y(e.values,"|")," இல் ஒன்று");case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"மிக பெரியது: எதிர்பார்க்கப்பட்டது ".concat(null!=(r=e.origin)?r:"மதிப்பு"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"உறுப்புகள்"," ஆக இருக்க வேண்டும்");return"மிக பெரியது: எதிர்பார்க்கப்பட்டது ".concat(null!=(a=e.origin)?a:"மதிப்பு"," ").concat(n).concat(e.maximum.toString()," ஆக இருக்க வேண்டும்")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," ஆக இருக்க வேண்டும்");return"மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ஆக இருக்க வேண்டும்")}case"invalid_format":if("starts_with"===e.format)return'தவறான சரம்: "'.concat(e.prefix,'" இல் தொடங்க வேண்டும்');if("ends_with"===e.format)return'தவறான சரம்: "'.concat(e.suffix,'" இல் முடிவடைய வேண்டும்');if("includes"===e.format)return'தவறான சரம்: "'.concat(e.includes,'" ஐ உள்ளடக்க வேண்டும்');if("regex"===e.format)return"தவறான சரம்: ".concat(e.pattern," முறைபாட்டுடன் பொருந்த வேண்டும்");return"தவறான ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"தவறான எண்: ".concat(e.divisor," இன் பலமாக இருக்க வேண்டும்");case"unrecognized_keys":return"அடையாளம் தெரியாத விசை".concat(e.keys.length>1?"கள்":"",": ").concat(y(e.keys,", "));case"invalid_key":return"".concat(e.origin," இல் தவறான விசை");case"invalid_union":default:return"தவறான உள்ளீடு";case"invalid_element":return"".concat(e.origin," இல் தவறான மதிப்பு")}}})()}}function t0(){return{localeError:(()=>{let e={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ".concat(e.expected," แต่ได้รับ ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(e))return"อาร์เรย์ (Array)";if(null===e)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"ค่าไม่ถูกต้อง: ควรเป็น ".concat(G(e.values[0]));return"ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"ไม่เกิน":"น้อยกว่า",o=t(e.origin);if(o)return"เกินกำหนด: ".concat(null!=(r=e.origin)?r:"ค่า"," ควรมี").concat(n," ").concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"รายการ");return"เกินกำหนด: ".concat(null!=(a=e.origin)?a:"ค่า"," ควรมี").concat(n," ").concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?"อย่างน้อย":"มากกว่า",r=t(e.origin);if(r)return"น้อยกว่ากำหนด: ".concat(e.origin," ควรมี").concat(n," ").concat(e.minimum.toString()," ").concat(r.unit);return"น้อยกว่ากำหนด: ".concat(e.origin," ควรมี").concat(n," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "'.concat(e.prefix,'"');if("ends_with"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "'.concat(e.suffix,'"');if("includes"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องมี "'.concat(e.includes,'" อยู่ในข้อความ');if("regex"===e.format)return"รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ".concat(e.pattern);return"รูปแบบไม่ถูกต้อง: ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ".concat(e.divisor," ได้ลงตัว");case"unrecognized_keys":return"พบคีย์ที่ไม่รู้จัก: ".concat(y(e.keys,", "));case"invalid_key":return"คีย์ไม่ถูกต้องใน ".concat(e.origin);case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return"ข้อมูลไม่ถูกต้องใน ".concat(e.origin);default:return"ข้อมูลไม่ถูกต้อง"}}})()}}function t4(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Geçersiz değer: beklenen ".concat(e.expected,", alınan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Geçersiz değer: beklenen ".concat(G(e.values[0]));return"Geçersiz seçenek: aşağıdakilerden biri olmalı: ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Çok büyük: beklenen ".concat(null!=(r=e.origin)?r:"değer"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"öğe");return"Çok büyük: beklenen ".concat(null!=(a=e.origin)?a:"değer"," ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Çok küçük: beklenen ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Çok küçük: beklenen ".concat(e.origin," ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Geçersiz metin: "'.concat(e.prefix,'" ile başlamalı');if("ends_with"===e.format)return'Geçersiz metin: "'.concat(e.suffix,'" ile bitmeli');if("includes"===e.format)return'Geçersiz metin: "'.concat(e.includes,'" içermeli');if("regex"===e.format)return"Geçersiz metin: ".concat(e.pattern," desenine uymalı");return"Geçersiz ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Geçersiz sayı: ".concat(e.divisor," ile tam bölünebilmeli");case"unrecognized_keys":return"Tanınmayan anahtar".concat(e.keys.length>1?"lar":"",": ").concat(y(e.keys,", "));case"invalid_key":return"".concat(e.origin," içinde geçersiz anahtar");case"invalid_union":default:return"Geçersiz değer";case"invalid_element":return"".concat(e.origin," içinde geçersiz değer")}}})()}}function t6(){return{localeError:(()=>{let e={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Неправильні вхідні дані: очікується ".concat(e.expected,", отримано ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"масив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Неправильні вхідні дані: очікується ".concat(G(e.values[0]));return"Неправильна опція: очікується одне з ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Занадто велике: очікується, що ".concat(null!=(r=e.origin)?r:"значення"," ").concat(o.verb," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"елементів");return"Занадто велике: очікується, що ".concat(null!=(a=e.origin)?a:"значення"," буде ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Занадто мале: очікується, що ".concat(e.origin," ").concat(r.verb," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Занадто мале: очікується, що ".concat(e.origin," буде ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неправильний рядок: повинен починатися з "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неправильний рядок: повинен закінчуватися на "'.concat(e.suffix,'"');if("includes"===e.format)return'Неправильний рядок: повинен містити "'.concat(e.includes,'"');if("regex"===e.format)return"Неправильний рядок: повинен відповідати шаблону ".concat(e.pattern);return"Неправильний ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"Неправильне число: повинно бути кратним ".concat(e.divisor);case"unrecognized_keys":return"Нерозпізнаний ключ".concat(e.keys.length>1?"і":"",": ").concat(y(e.keys,", "));case"invalid_key":return"Неправильний ключ у ".concat(e.origin);case"invalid_union":default:return"Неправильні вхідні дані";case"invalid_element":return"Неправильне значення у ".concat(e.origin)}}})()}}function t1(){return{localeError:(()=>{let e={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"غلط ان پٹ: ".concat(e.expected," متوقع تھا، ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"نمبر";case"object":if(Array.isArray(e))return"آرے";if(null===e)return"نل";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," موصول ہوا");case"invalid_value":if(1===e.values.length)return"غلط ان پٹ: ".concat(G(e.values[0])," متوقع تھا");return"غلط آپشن: ".concat(y(e.values,"|")," میں سے ایک متوقع تھا");case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"بہت بڑا: ".concat(null!=(r=e.origin)?r:"ویلیو"," کے ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"عناصر"," ہونے متوقع تھے");return"بہت بڑا: ".concat(null!=(a=e.origin)?a:"ویلیو"," کا ").concat(n).concat(e.maximum.toString()," ہونا متوقع تھا")}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"بہت چھوٹا: ".concat(e.origin," کے ").concat(n).concat(e.minimum.toString()," ").concat(r.unit," ہونے متوقع تھے");return"بہت چھوٹا: ".concat(e.origin," کا ").concat(n).concat(e.minimum.toString()," ہونا متوقع تھا")}case"invalid_format":if("starts_with"===e.format)return'غلط سٹرنگ: "'.concat(e.prefix,'" سے شروع ہونا چاہیے');if("ends_with"===e.format)return'غلط سٹرنگ: "'.concat(e.suffix,'" پر ختم ہونا چاہیے');if("includes"===e.format)return'غلط سٹرنگ: "'.concat(e.includes,'" شامل ہونا چاہیے');if("regex"===e.format)return"غلط سٹرنگ: پیٹرن ".concat(e.pattern," سے میچ ہونا چاہیے");return"غلط ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"غلط نمبر: ".concat(e.divisor," کا مضاعف ہونا چاہیے");case"unrecognized_keys":return"غیر تسلیم شدہ کی".concat(e.keys.length>1?"ز":"",": ").concat(y(e.keys,"، "));case"invalid_key":return"".concat(e.origin," میں غلط کی");case"invalid_union":default:return"غلط ان پٹ";case"invalid_element":return"".concat(e.origin," میں غلط ویلیو")}}})()}}function t2(){return{localeError:(()=>{let e={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"Đầu vào không hợp lệ: mong đợi ".concat(e.expected,", nhận được ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"số";case"object":if(Array.isArray(e))return"mảng";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Đầu vào không hợp lệ: mong đợi ".concat(G(e.values[0]));return"Tùy chọn không hợp lệ: mong đợi một trong các giá trị ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"Quá lớn: mong đợi ".concat(null!=(r=e.origin)?r:"giá trị"," ").concat(o.verb," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"phần tử");return"Quá lớn: mong đợi ".concat(null!=(a=e.origin)?a:"giá trị"," ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Quá nhỏ: mong đợi ".concat(e.origin," ").concat(r.verb," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"Quá nhỏ: mong đợi ".concat(e.origin," ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chuỗi không hợp lệ: phải bắt đầu bằng "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chuỗi không hợp lệ: phải kết thúc bằng "'.concat(e.suffix,'"');if("includes"===e.format)return'Chuỗi không hợp lệ: phải bao gồm "'.concat(e.includes,'"');if("regex"===e.format)return"Chuỗi không hợp lệ: phải khớp với mẫu ".concat(e.pattern);return"".concat(null!=(o=n[e.format])?o:e.format," không hợp lệ");case"not_multiple_of":return"Số không hợp lệ: phải là bội số của ".concat(e.divisor);case"unrecognized_keys":return"Khóa không được nhận dạng: ".concat(y(e.keys,", "));case"invalid_key":return"Khóa không hợp lệ trong ".concat(e.origin);case"invalid_union":default:return"Đầu vào không hợp lệ";case"invalid_element":return"Giá trị không hợp lệ trong ".concat(e.origin)}}})()}}function t9(){return{localeError:(()=>{let e={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"无效输入：期望 ".concat(e.expected,"，实际接收 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"非数字(NaN)":"数字";case"object":if(Array.isArray(e))return"数组";if(null===e)return"空值(null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"无效输入：期望 ".concat(G(e.values[0]));return"无效选项：期望以下之一 ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"数值过大：期望 ".concat(null!=(r=e.origin)?r:"值"," ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"个元素");return"数值过大：期望 ".concat(null!=(a=e.origin)?a:"值"," ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"数值过小：期望 ".concat(e.origin," ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"数值过小：期望 ".concat(e.origin," ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'无效字符串：必须以 "'.concat(e.prefix,'" 开头');if("ends_with"===e.format)return'无效字符串：必须以 "'.concat(e.suffix,'" 结尾');if("includes"===e.format)return'无效字符串：必须包含 "'.concat(e.includes,'"');if("regex"===e.format)return"无效字符串：必须满足正则表达式 ".concat(e.pattern);return"无效".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"无效数字：必须是 ".concat(e.divisor," 的倍数");case"unrecognized_keys":return"出现未知的键(key): ".concat(y(e.keys,", "));case"invalid_key":return"".concat(e.origin," 中的键(key)无效");case"invalid_union":default:return"无效输入";case"invalid_element":return"".concat(e.origin," 中包含无效值(value)")}}})()}}function t3(){return{localeError:(()=>{let e={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return e=>{var r,i,a,o;switch(e.code){case"invalid_type":return"無效的輸入值：預期為 ".concat(e.expected,"，但收到 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"無效的輸入值：預期為 ".concat(G(e.values[0]));return"無效的選項：預期為以下其中之一 ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",o=t(e.origin);if(o)return"數值過大：預期 ".concat(null!=(r=e.origin)?r:"值"," 應為 ").concat(n).concat(e.maximum.toString()," ").concat(null!=(i=o.unit)?i:"個元素");return"數值過大：預期 ".concat(null!=(a=e.origin)?a:"值"," 應為 ").concat(n).concat(e.maximum.toString())}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"數值過小：預期 ".concat(e.origin," 應為 ").concat(n).concat(e.minimum.toString()," ").concat(r.unit);return"數值過小：預期 ".concat(e.origin," 應為 ").concat(n).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'無效的字串：必須以 "'.concat(e.prefix,'" 開頭');if("ends_with"===e.format)return'無效的字串：必須以 "'.concat(e.suffix,'" 結尾');if("includes"===e.format)return'無效的字串：必須包含 "'.concat(e.includes,'"');if("regex"===e.format)return"無效的字串：必須符合格式 ".concat(e.pattern);return"無效的 ".concat(null!=(o=n[e.format])?o:e.format);case"not_multiple_of":return"無效的數字：必須為 ".concat(e.divisor," 的倍數");case"unrecognized_keys":return"無法識別的鍵值".concat(e.keys.length>1?"們":"","：").concat(y(e.keys,"、"));case"invalid_key":return"".concat(e.origin," 中有無效的鍵值");case"invalid_union":default:return"無效的輸入值";case"invalid_element":return"".concat(e.origin," 中有無效的值")}}})()}}function t5(){return{localeError:(()=>{let e={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}};function t(t){var n;return null!=(n=e[t])?n:null}let n={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return e=>{var r,i;switch(e.code){case"invalid_type":return"Ìbáwọlé aṣìṣe: a ní láti fi ".concat(e.expected,", àmọ̀ a rí ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nọ́mbà";case"object":if(Array.isArray(e))return"akopọ";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ìbáwọlé aṣìṣe: a ní láti fi ".concat(G(e.values[0]));return"Àṣàyàn aṣìṣe: yan ọ̀kan lára ".concat(y(e.values,"|"));case"too_big":{let n=e.inclusive?"<=":"<",i=t(e.origin);if(i)return"Tó pọ̀ jù: a ní láti jẹ́ pé ".concat(null!=(r=e.origin)?r:"iye"," ").concat(i.verb," ").concat(n).concat(e.maximum," ").concat(i.unit);return"Tó pọ̀ jù: a ní láti jẹ́ ".concat(n).concat(e.maximum)}case"too_small":{let n=e.inclusive?">=":">",r=t(e.origin);if(r)return"Kéré ju: a ní láti jẹ́ pé ".concat(e.origin," ").concat(r.verb," ").concat(n).concat(e.minimum," ").concat(r.unit);return"Kéré ju: a ní láti jẹ́ ".concat(n).concat(e.minimum)}case"invalid_format":if("starts_with"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀lú "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ parí pẹ̀lú "'.concat(e.suffix,'"');if("includes"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ ní "'.concat(e.includes,'"');if("regex"===e.format)return"Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bá àpẹẹrẹ mu ".concat(e.pattern);return"Aṣìṣe: ".concat(null!=(i=n[e.format])?i:e.format);case"not_multiple_of":return"Nọ́mbà aṣìṣe: gbọ́dọ̀ jẹ́ èyà pípín ti ".concat(e.divisor);case"unrecognized_keys":return"Bọtìnì àìmọ̀: ".concat(y(e.keys,", "));case"invalid_key":return"Bọtìnì aṣìṣe nínú ".concat(e.origin);case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return"Iye aṣìṣe nínú ".concat(e.origin)}}})()}}e.s(["ar",()=>ty,"az",()=>t_,"be",()=>tk,"ca",()=>tx,"cs",()=>tI,"da",()=>tw,"de",()=>tz,"en",()=>tS,"eo",()=>tj,"es",()=>tZ,"fa",()=>tU,"fi",()=>tO,"fr",()=>tN,"frCA",()=>tD,"he",()=>t$,"hu",()=>tP,"id",()=>tE,"is",()=>tA,"it",()=>tT,"ja",()=>tC,"kh",()=>tL,"ko",()=>tR,"mk",()=>tM,"ms",()=>tF,"nl",()=>tJ,"no",()=>tB,"ota",()=>tW,"pl",()=>tG,"ps",()=>tV,"pt",()=>tK,"ru",()=>tq,"sl",()=>tY,"sv",()=>tH,"ta",()=>tQ,"th",()=>t0,"tr",()=>t4,"ua",()=>t6,"ur",()=>t1,"vi",()=>t2,"yo",()=>t5,"zhCN",()=>t9,"zhTW",()=>t3],28410),e.s([],81934),e.i(81934);var t8=e.i(28410);e.s([],80639);var t7=e.i(80639);e.i(80370),e.s(["$ZodAsyncError",()=>nr,"$ZodEncodeError",()=>ni,"$brand",()=>nn,"$constructor",()=>nt,"NEVER",()=>ne,"config",()=>no,"globalConfig",()=>na],22025);let ne=Object.freeze({status:"aborted"});function nt(e,t,n){var r;function i(n,r){var i,a;for(let o in Object.defineProperty(n,"_zod",{value:null!=(a=n._zod)?a:{},enumerable:!1}),null!=(i=n._zod).traits||(i.traits=new Set),n._zod.traits.add(e),t(n,r),c.prototype)o in n||Object.defineProperty(n,o,{value:c.prototype[o].bind(n)});n._zod.constr=c,n._zod.def=r}let a=null!=(r=null==n?void 0:n.Parent)?r:Object;class o extends a{}function c(e){var t;let r=(null==n?void 0:n.Parent)?new o:this;for(let n of(i(r,e),null!=(t=r._zod).deferred||(t.deferred=[]),r._zod.deferred))n();return r}return Object.defineProperty(o,"name",{value:e}),Object.defineProperty(c,"init",{value:i}),Object.defineProperty(c,Symbol.hasInstance,{value:t=>{var r,i;return null!=n&&!!n.Parent&&t instanceof n.Parent||(null==t||null==(i=t._zod)||null==(r=i.traits)?void 0:r.has(e))}}),Object.defineProperty(c,"name",{value:e}),c}let nn=Symbol("zod_brand");class nr extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class ni extends Error{constructor(e){super("Encountered unidirectional transform during encode: ".concat(e)),this.name="ZodEncodeError"}}let na={};function no(e){return e&&Object.assign(na,e),na}e.i(22025),e.s(["_decode",()=>nz,"_decodeAsync",()=>nU,"_encode",()=>nI,"_encodeAsync",()=>nj,"_parse",()=>np,"_parseAsync",()=>nh,"_safeDecode",()=>n$,"_safeDecodeAsync",()=>nT,"_safeEncode",()=>nN,"_safeEncodeAsync",()=>nE,"_safeParse",()=>n_,"_safeParseAsync",()=>nk,"decode",()=>nS,"decodeAsync",()=>nO,"encode",()=>nw,"encodeAsync",()=>nZ,"parse",()=>ng,"parseAsync",()=>ny,"safeDecode",()=>nP,"safeDecodeAsync",()=>nC,"safeEncode",()=>nD,"safeEncodeAsync",()=>nA,"safeParse",()=>nb,"safeParseAsync",()=>nx],56536),e.s(["$ZodError",()=>nu,"$ZodRealError",()=>nl,"flattenError",()=>ns,"formatError",()=>nd,"prettifyError",()=>nv,"toDotPath",()=>nf,"treeifyError",()=>nm],62639);let nc=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,_,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},nu=nt("$ZodError",nc),nl=nt("$ZodError",nc,{Parent:Error});function ns(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e.message,n={},r=[];for(let i of e.issues)i.path.length>0?(n[i.path[0]]=n[i.path[0]]||[],n[i.path[0]].push(t(i))):r.push(t(i));return{formErrors:r,fieldErrors:n}}function nd(e,t){let n=t||function(e){return e.message},r={_errors:[]},i=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>i({issues:e}));else if("invalid_key"===t.code)i({issues:t.issues});else if("invalid_element"===t.code)i({issues:t.issues});else if(0===t.path.length)r._errors.push(n(t));else{let e=r,i=0;for(;i<t.path.length;){let r=t.path[i];i===t.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(n(t))):e[r]=e[r]||{_errors:[]},e=e[r],i++}}};return i(e),r}function nm(e,t){let n=t||function(e){return e.message},r={errors:[]},i=function(e){var t,a;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let c of e.issues)if("invalid_union"===c.code&&c.errors.length)c.errors.map(e=>i({issues:e},c.path));else if("invalid_key"===c.code)i({issues:c.issues},c.path);else if("invalid_element"===c.code)i({issues:c.issues},c.path);else{let e=[...o,...c.path];if(0===e.length){r.errors.push(n(c));continue}let i=r,u=0;for(;u<e.length;){let r=e[u],o=u===e.length-1;"string"==typeof r?(null!=i.properties||(i.properties={}),null!=(t=i.properties)[r]||(t[r]={errors:[]}),i=i.properties[r]):(null!=i.items||(i.items=[]),null!=(a=i.items)[r]||(a[r]={errors:[]}),i=i.items[r]),o&&i.errors.push(n(c)),u++}}};return i(e),r}function nf(e){let t=[];for(let n of e.map(e=>"object"==typeof e?e.key:e))"number"==typeof n?t.push("[".concat(n,"]")):"symbol"==typeof n?t.push("[".concat(JSON.stringify(String(n)),"]")):/[^\w$]/.test(n)?t.push("[".concat(JSON.stringify(n),"]")):(t.length&&t.push("."),t.push(n));return t.join("")}function nv(e){let t=[];for(let r of[...e.issues].sort((e,t)=>{var n,r;return(null!=(n=e.path)?n:[]).length-(null!=(r=t.path)?r:[]).length})){var n;t.push("✖ ".concat(r.message)),(null==(n=r.path)?void 0:n.length)&&t.push("  → at ".concat(nf(r.path)))}return t.join("\n")}let np=e=>(t,n,r,i)=>{let a=r?Object.assign(r,{async:!1}):{async:!1},o=t._zod.run({value:n,issues:[]},a);if(o instanceof Promise)throw new nr;if(o.issues.length){var c;let t=new(null!=(c=null==i?void 0:i.Err)?c:e)(o.issues.map(e=>ec(e,a,no())));throw P(t,null==i?void 0:i.callee),t}return o.value},ng=np(nl),nh=e=>async(t,n,r,i)=>{let a=r?Object.assign(r,{async:!0}):{async:!0},o=t._zod.run({value:n,issues:[]},a);if(o instanceof Promise&&(o=await o),o.issues.length){var c;let t=new(null!=(c=null==i?void 0:i.Err)?c:e)(o.issues.map(e=>ec(e,a,no())));throw P(t,null==i?void 0:i.callee),t}return o.value},ny=nh(nl),n_=e=>(t,n,r)=>{let i=r?{...r,async:!1}:{async:!1},a=t._zod.run({value:n,issues:[]},i);if(a instanceof Promise)throw new nr;return a.issues.length?{success:!1,error:new(null!=e?e:nu)(a.issues.map(e=>ec(e,i,no())))}:{success:!0,data:a.value}},nb=n_(nl),nk=e=>async(t,n,r)=>{let i=r?Object.assign(r,{async:!0}):{async:!0},a=t._zod.run({value:n,issues:[]},i);return a instanceof Promise&&(a=await a),a.issues.length?{success:!1,error:new e(a.issues.map(e=>ec(e,i,no())))}:{success:!0,data:a.value}},nx=nk(nl),nI=e=>(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return np(e)(t,n,i)},nw=nI(nl),nz=e=>(t,n,r)=>np(e)(t,n,r),nS=nz(nl),nj=e=>async(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return nh(e)(t,n,i)},nZ=nj(nl),nU=e=>async(t,n,r)=>nh(e)(t,n,r),nO=nU(nl),nN=e=>(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return n_(e)(t,n,i)},nD=nN(nl),n$=e=>(t,n,r)=>n_(e)(t,n,r),nP=n$(nl),nE=e=>async(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return nk(e)(t,n,i)},nA=nE(nl),nT=e=>async(t,n,r)=>nk(e)(t,n,r),nC=nT(nl);e.i(56536),e.i(62639),e.s(["$ZodAny",()=>rL,"$ZodArray",()=>rW,"$ZodBase64",()=>rw,"$ZodBase64URL",()=>rS,"$ZodBigInt",()=>rP,"$ZodBigIntFormat",()=>rE,"$ZodBoolean",()=>r$,"$ZodCIDRv4",()=>rk,"$ZodCIDRv6",()=>rx,"$ZodCUID",()=>rs,"$ZodCUID2",()=>rd,"$ZodCatch",()=>iv,"$ZodCodec",()=>iy,"$ZodCustom",()=>ij,"$ZodCustomStringFormat",()=>rO,"$ZodDate",()=>rJ,"$ZodDefault",()=>ic,"$ZodDiscriminatedUnion",()=>rQ,"$ZodE164",()=>rj,"$ZodEmail",()=>ro,"$ZodEmoji",()=>ru,"$ZodEnum",()=>r7,"$ZodFile",()=>it,"$ZodFunction",()=>iw,"$ZodGUID",()=>ri,"$ZodIPv4",()=>r_,"$ZodIPv6",()=>rb,"$ZodISODate",()=>rg,"$ZodISODateTime",()=>rp,"$ZodISODuration",()=>ry,"$ZodISOTime",()=>rh,"$ZodIntersection",()=>r0,"$ZodJWT",()=>rU,"$ZodKSUID",()=>rv,"$ZodLazy",()=>iS,"$ZodLiteral",()=>ie,"$ZodMap",()=>r9,"$ZodNaN",()=>ip,"$ZodNanoID",()=>rl,"$ZodNever",()=>rM,"$ZodNonOptional",()=>is,"$ZodNull",()=>rC,"$ZodNullable",()=>io,"$ZodNumber",()=>rN,"$ZodNumberFormat",()=>rD,"$ZodObject",()=>rX,"$ZodObjectJIT",()=>rq,"$ZodOptional",()=>ia,"$ZodPipe",()=>ig,"$ZodPrefault",()=>il,"$ZodPromise",()=>iz,"$ZodReadonly",()=>ik,"$ZodRecord",()=>r2,"$ZodSet",()=>r5,"$ZodString",()=>rn,"$ZodStringFormat",()=>rr,"$ZodSuccess",()=>im,"$ZodSymbol",()=>rA,"$ZodTemplateLiteral",()=>iI,"$ZodTransform",()=>ir,"$ZodTuple",()=>r6,"$ZodType",()=>rt,"$ZodULID",()=>rm,"$ZodURL",()=>rc,"$ZodUUID",()=>ra,"$ZodUndefined",()=>rT,"$ZodUnion",()=>rH,"$ZodUnknown",()=>rR,"$ZodVoid",()=>rF,"$ZodXID",()=>rf,"clone",()=>B,"isValidBase64",()=>rI,"isValidBase64URL",()=>rz,"isValidJWT",()=>rZ],57088),e.s(["$ZodAny",()=>rL,"$ZodArray",()=>rW,"$ZodBase64",()=>rw,"$ZodBase64URL",()=>rS,"$ZodBigInt",()=>rP,"$ZodBigIntFormat",()=>rE,"$ZodBoolean",()=>r$,"$ZodCIDRv4",()=>rk,"$ZodCIDRv6",()=>rx,"$ZodCUID",()=>rs,"$ZodCUID2",()=>rd,"$ZodCatch",()=>iv,"$ZodCodec",()=>iy,"$ZodCustom",()=>ij,"$ZodCustomStringFormat",()=>rO,"$ZodDate",()=>rJ,"$ZodDefault",()=>ic,"$ZodDiscriminatedUnion",()=>rQ,"$ZodE164",()=>rj,"$ZodEmail",()=>ro,"$ZodEmoji",()=>ru,"$ZodEnum",()=>r7,"$ZodFile",()=>it,"$ZodFunction",()=>iw,"$ZodGUID",()=>ri,"$ZodIPv4",()=>r_,"$ZodIPv6",()=>rb,"$ZodISODate",()=>rg,"$ZodISODateTime",()=>rp,"$ZodISODuration",()=>ry,"$ZodISOTime",()=>rh,"$ZodIntersection",()=>r0,"$ZodJWT",()=>rU,"$ZodKSUID",()=>rv,"$ZodLazy",()=>iS,"$ZodLiteral",()=>ie,"$ZodMap",()=>r9,"$ZodNaN",()=>ip,"$ZodNanoID",()=>rl,"$ZodNever",()=>rM,"$ZodNonOptional",()=>is,"$ZodNull",()=>rC,"$ZodNullable",()=>io,"$ZodNumber",()=>rN,"$ZodNumberFormat",()=>rD,"$ZodObject",()=>rX,"$ZodObjectJIT",()=>rq,"$ZodOptional",()=>ia,"$ZodPipe",()=>ig,"$ZodPrefault",()=>il,"$ZodPromise",()=>iz,"$ZodReadonly",()=>ik,"$ZodRecord",()=>r2,"$ZodSet",()=>r5,"$ZodString",()=>rn,"$ZodStringFormat",()=>rr,"$ZodSuccess",()=>im,"$ZodSymbol",()=>rA,"$ZodTemplateLiteral",()=>iI,"$ZodTransform",()=>ir,"$ZodTuple",()=>r6,"$ZodType",()=>rt,"$ZodULID",()=>rm,"$ZodURL",()=>rc,"$ZodUUID",()=>ra,"$ZodUndefined",()=>rT,"$ZodUnion",()=>rH,"$ZodUnknown",()=>rR,"$ZodVoid",()=>rF,"$ZodXID",()=>rf,"isValidBase64",()=>rI,"isValidBase64URL",()=>rz,"isValidJWT",()=>rZ],36187),e.s(["$ZodCheck",()=>nL,"$ZodCheckBigIntFormat",()=>nW,"$ZodCheckEndsWith",()=>n2,"$ZodCheckGreaterThan",()=>nF,"$ZodCheckIncludes",()=>n6,"$ZodCheckLengthEquals",()=>nY,"$ZodCheckLessThan",()=>nM,"$ZodCheckLowerCase",()=>n0,"$ZodCheckMaxLength",()=>nX,"$ZodCheckMaxSize",()=>nV,"$ZodCheckMimeType",()=>n5,"$ZodCheckMinLength",()=>nq,"$ZodCheckMinSize",()=>nG,"$ZodCheckMultipleOf",()=>nJ,"$ZodCheckNumberFormat",()=>nB,"$ZodCheckOverwrite",()=>n8,"$ZodCheckProperty",()=>n3,"$ZodCheckRegex",()=>nQ,"$ZodCheckSizeEquals",()=>nK,"$ZodCheckStartsWith",()=>n1,"$ZodCheckStringFormat",()=>nH,"$ZodCheckUpperCase",()=>n4],69751);let nL=nt("$ZodCheck",(e,t)=>{var n;null!=e._zod||(e._zod={}),e._zod.def=t,null!=(n=e._zod).onattach||(n.onattach=[])}),nR={number:"number",bigint:"bigint",object:"date"},nM=nt("$ZodCheckLessThan",(e,t)=>{nL.init(e,t);let n=nR[typeof t.value];e._zod.onattach.push(e=>{var n;let r=e._zod.bag,i=null!=(n=t.inclusive?r.maximum:r.exclusiveMaximum)?n:1/0;t.value<i&&(t.inclusive?r.maximum=t.value:r.exclusiveMaximum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value<=t.value:r.value<t.value)||r.issues.push({origin:n,code:"too_big",maximum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),nF=nt("$ZodCheckGreaterThan",(e,t)=>{nL.init(e,t);let n=nR[typeof t.value];e._zod.onattach.push(e=>{var n;let r=e._zod.bag,i=null!=(n=t.inclusive?r.minimum:r.exclusiveMinimum)?n:-1/0;t.value>i&&(t.inclusive?r.minimum=t.value:r.exclusiveMinimum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value>=t.value:r.value>t.value)||r.issues.push({origin:n,code:"too_small",minimum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),nJ=nt("$ZodCheckMultipleOf",(e,t)=>{nL.init(e,t),e._zod.onattach.push(e=>{var n;null!=(n=e._zod.bag).multipleOf||(n.multipleOf=t.value)}),e._zod.check=n=>{if(typeof n.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof n.value?n.value%t.value===BigInt(0):0===I(n.value,t.value))||n.issues.push({origin:typeof n.value,code:"not_multiple_of",divisor:t.value,input:n.value,inst:e,continue:!t.abort})}}),nB=nt("$ZodCheckNumberFormat",(e,t)=>{var n;nL.init(e,t),t.format=t.format||"float64";let r=null==(n=t.format)?void 0:n.includes("int"),i=r?"int":"number",[a,o]=X[t.format];e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,n.minimum=a,n.maximum=o,r&&(n.pattern=e6)}),e._zod.check=n=>{let c=n.value;if(r){if(!Number.isInteger(c))return void n.issues.push({expected:i,format:t.format,code:"invalid_type",continue:!1,input:c,inst:e});if(!Number.isSafeInteger(c))return void(c>0?n.issues.push({input:c,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):n.issues.push({input:c,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}))}c<a&&n.issues.push({origin:"number",input:c,code:"too_small",minimum:a,inclusive:!0,inst:e,continue:!t.abort}),c>o&&n.issues.push({origin:"number",input:c,code:"too_big",maximum:o,inst:e})}}),nW=nt("$ZodCheckBigIntFormat",(e,t)=>{nL.init(e,t);let[n,r]=q[t.format];e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,i.minimum=n,i.maximum=r}),e._zod.check=i=>{let a=i.value;a<n&&i.issues.push({origin:"bigint",input:a,code:"too_small",minimum:n,inclusive:!0,inst:e,continue:!t.abort}),a>r&&i.issues.push({origin:"bigint",input:a,code:"too_big",maximum:r,inst:e})}}),nV=nt("$ZodCheckMaxSize",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.maximum)?n:1/0;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;r.size<=t.maximum||n.issues.push({origin:eu(r),code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),nG=nt("$ZodCheckMinSize",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.minimum)?n:-1/0;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;r.size>=t.minimum||n.issues.push({origin:eu(r),code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),nK=nt("$ZodCheckSizeEquals",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.size,n.maximum=t.size,n.size=t.size}),e._zod.check=n=>{let r=n.value,i=r.size;if(i===t.size)return;let a=i>t.size;n.issues.push({origin:eu(r),...a?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),nX=nt("$ZodCheckMaxLength",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.maximum)?n:1/0;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;if(r.length<=t.maximum)return;let i=el(r);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),nq=nt("$ZodCheckMinLength",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.minimum)?n:-1/0;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;if(r.length>=t.minimum)return;let i=el(r);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),nY=nt("$ZodCheckLengthEquals",(e,t)=>{var n;nL.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!k(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let r=n.value,i=r.length;if(i===t.length)return;let a=el(r),o=i>t.length;n.issues.push({origin:a,...o?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),nH=nt("$ZodCheckStringFormat",(e,t)=>{var n,r;nL.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;(n.format=t.format,t.pattern)&&(null!=n.patterns||(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?null!=(n=e._zod).check||(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):null!=(r=e._zod).check||(r.check=()=>{})}),nQ=nt("$ZodCheckRegex",(e,t)=>{nH.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),n0=nt("$ZodCheckLowerCase",(e,t)=>{null!=t.pattern||(t.pattern=e5),nH.init(e,t)}),n4=nt("$ZodCheckUpperCase",(e,t)=>{null!=t.pattern||(t.pattern=e8),nH.init(e,t)}),n6=nt("$ZodCheckIncludes",(e,t)=>{nL.init(e,t);let n=J(t.includes),r=new RegExp("number"==typeof t.position?"^.{".concat(t.position,"}").concat(n):n);t.pattern=r,e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),n1=nt("$ZodCheckStartsWith",(e,t)=>{nL.init(e,t);let n=new RegExp("^".concat(J(t.prefix),".*"));null!=t.pattern||(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),n2=nt("$ZodCheckEndsWith",(e,t)=>{nL.init(e,t);let n=new RegExp(".*".concat(J(t.suffix),"$"));null!=t.pattern||(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}});function n9(e,t,n){e.issues.length&&t.issues.push(...ea(n,e.issues))}let n3=nt("$ZodCheckProperty",(e,t)=>{nL.init(e,t),e._zod.check=e=>{let n=t.schema._zod.run({value:e.value[t.property],issues:[]},{});if(n instanceof Promise)return n.then(n=>n9(n,e,t.property));n9(n,e,t.property)}}),n5=nt("$ZodCheckMimeType",(e,t)=>{nL.init(e,t);let n=new Set(t.mime);e._zod.onattach.push(e=>{e._zod.bag.mime=t.mime}),e._zod.check=r=>{n.has(r.value.type)||r.issues.push({code:"invalid_value",values:t.mime,input:r.value.type,inst:e,continue:!t.abort})}}),n8=nt("$ZodCheckOverwrite",(e,t)=>{nL.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});e.s(["Doc",()=>n7],49463);class n7{indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){var e;return Function(...null===this||void 0===this?void 0:this.args,[...(null!=(e=null===this||void 0===this?void 0:this.content)?e:[""]).map(e=>"  ".concat(e))].join("\n"))}constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}}e.s(["version",()=>re],68346);let re={major:4,minor:1,patch:5},rt=nt("$ZodType",(e,t)=>{var n,r,i;null!=e||(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=re;let a=[...null!=(r=e._zod.def.checks)?r:[]];for(let t of(e._zod.traits.has("$ZodCheck")&&a.unshift(e),a))for(let n of t._zod.onattach)n(e);if(0===a.length)null!=(n=e._zod).deferred||(n.deferred=[]),null==(i=e._zod.deferred)||i.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,n)=>{let r,i=ei(e);for(let a of t){if(a._zod.def.when){if(!a._zod.def.when(e))continue}else if(i)continue;let t=e.issues.length,o=a._zod.check(e);if(o instanceof Promise&&(null==n?void 0:n.async)===!1)throw new nr;if(r||o instanceof Promise)r=(null!=r?r:Promise.resolve()).then(async()=>{await o,e.issues.length!==t&&(i||(i=ei(e,t)))});else{if(e.issues.length===t)continue;i||(i=ei(e,t))}}return r?r.then(()=>e):e},n=(n,r,i)=>{if(ei(n))return n.aborted=!0,n;let o=t(r,a,i);if(o instanceof Promise){if(!1===i.async)throw new nr;return o.then(t=>e._zod.parse(t,i))}return e._zod.parse(o,i)};e._zod.run=(r,i)=>{if(i.skipChecks)return e._zod.parse(r,i);if("backward"===i.direction){let t=e._zod.parse({value:r.value,issues:[]},{...i,skipChecks:!0});return t instanceof Promise?t.then(e=>n(e,r,i)):n(t,r,i)}let o=e._zod.parse(r,i);if(o instanceof Promise){if(!1===i.async)throw new nr;return o.then(e=>t(e,a,i))}return t(o,a,i)}}e["~standard"]={validate:t=>{try{var n;let r=nb(e,t);return r.success?{value:r.data}:{issues:null==(n=r.error)?void 0:n.issues}}catch(n){return nx(e,t).then(e=>{var t;return e.success?{value:e.data}:{issues:null==(t=e.error)?void 0:t.issues}})}},vendor:"zod",version:1}}),rn=nt("$ZodString",(e,t)=>{var n,r,i;rt.init(e,t),e._zod.pattern=null!=(i=[...null!=(r=null==e||null==(n=e._zod.bag)?void 0:n.patterns)?r:[]].pop())?i:e0(e._zod.bag),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),rr=nt("$ZodStringFormat",(e,t)=>{nH.init(e,t),rn.init(e,t)}),ri=nt("$ZodGUID",(e,t)=>{null!=t.pattern||(t.pattern=eZ),rr.init(e,t)}),ra=nt("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error('Invalid UUID version: "'.concat(t.version,'"'));null!=t.pattern||(t.pattern=eU(e))}else null!=t.pattern||(t.pattern=eU());rr.init(e,t)}),ro=nt("$ZodEmail",(e,t)=>{null!=t.pattern||(t.pattern=e$),rr.init(e,t)}),rc=nt("$ZodURL",(e,t)=>{rr.init(e,t),e._zod.check=n=>{try{let r=n.value.trim(),i=new URL(r);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(i.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:eV.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(i.protocol.endsWith(":")?i.protocol.slice(0,-1):i.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),t.normalize?n.value=i.href:n.value=r;return}catch(r){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),ru=nt("$ZodEmoji",(e,t)=>{null!=t.pattern||(t.pattern=eL()),rr.init(e,t)}),rl=nt("$ZodNanoID",(e,t)=>{null!=t.pattern||(t.pattern=ez),rr.init(e,t)}),rs=nt("$ZodCUID",(e,t)=>{null!=t.pattern||(t.pattern=eb),rr.init(e,t)}),rd=nt("$ZodCUID2",(e,t)=>{null!=t.pattern||(t.pattern=ek),rr.init(e,t)}),rm=nt("$ZodULID",(e,t)=>{null!=t.pattern||(t.pattern=ex),rr.init(e,t)}),rf=nt("$ZodXID",(e,t)=>{null!=t.pattern||(t.pattern=eI),rr.init(e,t)}),rv=nt("$ZodKSUID",(e,t)=>{null!=t.pattern||(t.pattern=ew),rr.init(e,t)}),rp=nt("$ZodISODateTime",(e,t)=>{null!=t.pattern||(t.pattern=eQ(t)),rr.init(e,t)}),rg=nt("$ZodISODate",(e,t)=>{null!=t.pattern||(t.pattern=eq),rr.init(e,t)}),rh=nt("$ZodISOTime",(e,t)=>{null!=t.pattern||(t.pattern=eH(t)),rr.init(e,t)}),ry=nt("$ZodISODuration",(e,t)=>{null!=t.pattern||(t.pattern=eS),rr.init(e,t)}),r_=nt("$ZodIPv4",(e,t)=>{null!=t.pattern||(t.pattern=eR),rr.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),rb=nt("$ZodIPv6",(e,t)=>{null!=t.pattern||(t.pattern=eM),rr.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL("http://[".concat(n.value,"]"))}catch(r){n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),rk=nt("$ZodCIDRv4",(e,t)=>{null!=t.pattern||(t.pattern=eF),rr.init(e,t)}),rx=nt("$ZodCIDRv6",(e,t)=>{null!=t.pattern||(t.pattern=eJ),rr.init(e,t),e._zod.check=n=>{let[r,i]=n.value.split("/");try{if(!i)throw Error();let e=Number(i);if("".concat(e)!==i||e<0||e>128)throw Error();new URL("http://[".concat(r,"]"))}catch(r){n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function rI(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch(e){return!1}}let rw=nt("$ZodBase64",(e,t)=>{null!=t.pattern||(t.pattern=eB),rr.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{rI(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}});function rz(e){if(!eW.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return rI(t.padEnd(4*Math.ceil(t.length/4),"="))}let rS=nt("$ZodBase64URL",(e,t)=>{null!=t.pattern||(t.pattern=eW),rr.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{rz(n.value)||n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),rj=nt("$ZodE164",(e,t)=>{null!=t.pattern||(t.pattern=eK),rr.init(e,t)});function rZ(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let n=e.split(".");if(3!==n.length)return!1;let[r]=n;if(!r)return!1;let i=JSON.parse(atob(r));if("typ"in i&&(null==i?void 0:i.typ)!=="JWT"||!i.alg||t&&(!("alg"in i)||i.alg!==t))return!1;return!0}catch(e){return!1}}let rU=nt("$ZodJWT",(e,t)=>{rr.init(e,t),e._zod.check=n=>{rZ(n.value,t.alg)||n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),rO=nt("$ZodCustomStringFormat",(e,t)=>{rr.init(e,t),e._zod.check=n=>{t.fn(n.value)||n.issues.push({code:"invalid_format",format:t.format,input:n.value,inst:e,continue:!t.abort})}}),rN=nt("$ZodNumber",(e,t)=>{var n;rt.init(e,t),e._zod.pattern=null!=(n=e._zod.bag.pattern)?n:e1,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=Number(n.value)}catch(e){}let i=n.value;if("number"==typeof i&&!Number.isNaN(i)&&Number.isFinite(i))return n;let a="number"==typeof i?Number.isNaN(i)?"NaN":Number.isFinite(i)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:i,inst:e,...a?{received:a}:{}}),n}}),rD=nt("$ZodNumber",(e,t)=>{nB.init(e,t),rN.init(e,t)}),r$=nt("$ZodBoolean",(e,t)=>{rt.init(e,t),e._zod.pattern=e2,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=!!n.value}catch(e){}let i=n.value;return"boolean"==typeof i||n.issues.push({expected:"boolean",code:"invalid_type",input:i,inst:e}),n}}),rP=nt("$ZodBigInt",(e,t)=>{rt.init(e,t),e._zod.pattern=e4,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=BigInt(n.value)}catch(e){}return"bigint"==typeof n.value||n.issues.push({expected:"bigint",code:"invalid_type",input:n.value,inst:e}),n}}),rE=nt("$ZodBigInt",(e,t)=>{nW.init(e,t),rP.init(e,t)}),rA=nt("$ZodSymbol",(e,t)=>{rt.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return"symbol"==typeof r||t.issues.push({expected:"symbol",code:"invalid_type",input:r,inst:e}),t}}),rT=nt("$ZodUndefined",(e,t)=>{rt.init(e,t),e._zod.pattern=e3,e._zod.values=new Set([void 0]),e._zod.optin="optional",e._zod.optout="optional",e._zod.parse=(t,n)=>{let r=t.value;return void 0===r||t.issues.push({expected:"undefined",code:"invalid_type",input:r,inst:e}),t}}),rC=nt("$ZodNull",(e,t)=>{rt.init(e,t),e._zod.pattern=e9,e._zod.values=new Set([null]),e._zod.parse=(t,n)=>{let r=t.value;return null===r||t.issues.push({expected:"null",code:"invalid_type",input:r,inst:e}),t}}),rL=nt("$ZodAny",(e,t)=>{rt.init(e,t),e._zod.parse=e=>e}),rR=nt("$ZodUnknown",(e,t)=>{rt.init(e,t),e._zod.parse=e=>e}),rM=nt("$ZodNever",(e,t)=>{rt.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)}),rF=nt("$ZodVoid",(e,t)=>{rt.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return void 0===r||t.issues.push({expected:"void",code:"invalid_type",input:r,inst:e}),t}}),rJ=nt("$ZodDate",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=new Date(n.value)}catch(e){}let i=n.value,a=i instanceof Date;return a&&!Number.isNaN(i.getTime())||n.issues.push({expected:"date",code:"invalid_type",input:i,...a?{received:"Invalid Date"}:{},inst:e}),n}});function rB(e,t,n){e.issues.length&&t.issues.push(...ea(n,e.issues)),t.value[n]=e.value}let rW=nt("$ZodArray",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!Array.isArray(i))return n.issues.push({expected:"array",code:"invalid_type",input:i,inst:e}),n;n.value=Array(i.length);let a=[];for(let e=0;e<i.length;e++){let o=i[e],c=t.element._zod.run({value:o,issues:[]},r);c instanceof Promise?a.push(c.then(t=>rB(t,n,e))):rB(c,n,e)}return a.length?Promise.all(a).then(()=>n):n}});function rV(e,t,n,r){e.issues.length&&t.issues.push(...ea(n,e.issues)),void 0===e.value?n in r&&(t.value[n]=void 0):t.value[n]=e.value}function rG(e){let t=Object.keys(e.shape);for(let n of t)if(!e.shape[n]._zod.traits.has("$ZodType"))throw Error('Invalid element at key "'.concat(n,'": expected a Zod schema'));let n=K(e.shape);return{...e,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(n)}}function rK(e,t,n,r,i,a){let o=[],c=i.keySet,u=i.catchall._zod,l=u.def.type;for(let i of Object.keys(t)){if(c.has(i))continue;if("never"===l){o.push(i);continue}let a=u.run({value:t[i],issues:[]},r);a instanceof Promise?e.push(a.then(e=>rV(e,n,i,t))):rV(a,n,i,t)}return(o.length&&n.issues.push({code:"unrecognized_keys",keys:o,input:t,inst:a}),e.length)?Promise.all(e).then(()=>n):n}let rX=nt("$ZodObject",(e,t)=>{let n;rt.init(e,t);let r=b(()=>rG(t));z(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let r=e[t]._zod;if(r.values){for(let e of(null!=n[t]||(n[t]=new Set),r.values))n[t].add(e)}}return n});let i=t.catchall;e._zod.parse=(t,a)=>{null!=n||(n=r.value);let o=t.value;if(!E(o))return t.issues.push({expected:"object",code:"invalid_type",input:o,inst:e}),t;t.value={};let c=[],u=n.shape;for(let e of n.keys){let n=u[e]._zod.run({value:o[e],issues:[]},a);n instanceof Promise?c.push(n.then(n=>rV(n,t,e,o))):rV(n,t,e,o)}return i?rK(c,o,t,a,r.value,e):c.length?Promise.all(c).then(()=>t):t}}),rq=nt("$ZodObjectJIT",(e,t)=>{let n,r;rX.init(e,t);let i=e._zod.parse,a=b(()=>rG(t)),o=!na.jitless,c=o&&A.value,u=t.catchall;e._zod.parse=(l,s)=>{null!=r||(r=a.value);let d=l.value;return E(d)?o&&c&&(null==s?void 0:s.async)===!1&&!0!==s.jitless?(n||(n=(e=>{let t=new n7(["shape","payload","ctx"]),n=a.value,r=e=>{let t=$(e);return"shape[".concat(t,"]._zod.run({ value: input[").concat(t,"], issues: [] }, ctx)")};t.write("const input = payload.value;");let i=Object.create(null),o=0;for(let e of n.keys)i[e]="key_".concat(o++);for(let e of(t.write("const newResult = {}"),n.keys)){let n=i[e],a=$(e);t.write("const ".concat(n," = ").concat(r(e),";")),t.write("\n        if (".concat(n,".issues.length) {\n          payload.issues = payload.issues.concat(").concat(n,".issues.map(iss => ({\n            ...iss,\n            path: iss.path ? [").concat(a,", ...iss.path] : [").concat(a,"]\n          })));\n        }\n        \n        if (").concat(n,".value === undefined) {\n          if (").concat(a," in input) {\n            newResult[").concat(a,"] = undefined;\n          }\n        } else {\n          newResult[").concat(a,"] = ").concat(n,".value;\n        }\n      "))}t.write("payload.value = newResult;"),t.write("return payload;");let c=t.compile();return(t,n)=>c(e,t,n)})(t.shape)),l=n(l,s),u)?rK([],d,l,s,r,e):l:i(l,s):(l.issues.push({expected:"object",code:"invalid_type",input:d,inst:e}),l)}});function rY(e,t,n,r){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;let i=e.filter(e=>!ei(e));return 1===i.length?(t.value=i[0].value,i[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:n,errors:e.map(e=>e.issues.map(e=>ec(e,r,no())))}),t)}let rH=nt("$ZodUnion",(e,t)=>{rt.init(e,t),z(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),z(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),z(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),z(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return new RegExp("^(".concat(e.map(e=>x(e.source)).join("|"),")$"))}});let n=1===t.options.length,r=t.options[0]._zod.run;e._zod.parse=(i,a)=>{if(n)return r(i,a);let o=!1,c=[];for(let e of t.options){let t=e._zod.run({value:i.value,issues:[]},a);if(t instanceof Promise)c.push(t),o=!0;else{if(0===t.issues.length)return t;c.push(t)}}return o?Promise.all(c).then(t=>rY(t,i,e,a)):rY(c,i,e,a)}}),rQ=nt("$ZodDiscriminatedUnion",(e,t)=>{rH.init(e,t);let n=e._zod.parse;z(e._zod,"propValues",()=>{let e={};for(let n of t.options){let r=n._zod.propValues;if(!r||0===Object.keys(r).length)throw Error('Invalid discriminated union option at index "'.concat(t.options.indexOf(n),'"'));for(let[t,n]of Object.entries(r))for(let r of(e[t]||(e[t]=new Set),n))e[t].add(r)}return e});let r=b(()=>{let e=t.options,n=new Map;for(let i of e){var r;let e=null==(r=i._zod.propValues)?void 0:r[t.discriminator];if(!e||0===e.size)throw Error('Invalid discriminated union option at index "'.concat(t.options.indexOf(i),'"'));for(let t of e){if(n.has(t))throw Error('Duplicate discriminator value "'.concat(String(t),'"'));n.set(t,i)}}return n});e._zod.parse=(i,a)=>{let o=i.value;if(!E(o))return i.issues.push({code:"invalid_type",expected:"object",input:o,inst:e}),i;let c=r.value.get(null==o?void 0:o[t.discriminator]);return c?c._zod.run(i,a):t.unionFallback?n(i,a):(i.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:t.discriminator,input:o,path:[t.discriminator],inst:e}),i)}}),r0=nt("$ZodIntersection",(e,t)=>{rt.init(e,t),e._zod.parse=(e,n)=>{let r=e.value,i=t.left._zod.run({value:r,issues:[]},n),a=t.right._zod.run({value:r,issues:[]},n);return i instanceof Promise||a instanceof Promise?Promise.all([i,a]).then(t=>{let[n,r]=t;return r4(e,n,r)}):r4(e,i,a)}});function r4(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),ei(e))return e;let r=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(T(t)&&T(n)){let r=Object.keys(n),i=Object.keys(t).filter(e=>-1!==r.indexOf(e)),a={...t,...n};for(let r of i){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1,mergeErrorPath:[r,...i.mergeErrorPath]};a[r]=i.data}return{valid:!0,data:a}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let r=[];for(let i=0;i<t.length;i++){let a=e(t[i],n[i]);if(!a.valid)return{valid:!1,mergeErrorPath:[i,...a.mergeErrorPath]};r.push(a.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!r.valid)throw Error("Unmergable intersection. Error path: "+"".concat(JSON.stringify(r.mergeErrorPath)));return e.value=r.data,e}let r6=nt("$ZodTuple",(e,t)=>{rt.init(e,t);let n=t.items,r=n.length-[...n].reverse().findIndex(e=>"optional"!==e._zod.optin);e._zod.parse=(i,a)=>{let o=i.value;if(!Array.isArray(o))return i.issues.push({input:o,inst:e,expected:"tuple",code:"invalid_type"}),i;i.value=[];let c=[];if(!t.rest){let t=o.length>n.length,a=o.length<r-1;if(t||a)return i.issues.push({...t?{code:"too_big",maximum:n.length}:{code:"too_small",minimum:n.length},input:o,inst:e,origin:"array"}),i}let u=-1;for(let e of n){if(++u>=o.length&&u>=r)continue;let t=e._zod.run({value:o[u],issues:[]},a);t instanceof Promise?c.push(t.then(e=>r1(e,i,u))):r1(t,i,u)}if(t.rest)for(let e of o.slice(n.length)){u++;let n=t.rest._zod.run({value:e,issues:[]},a);n instanceof Promise?c.push(n.then(e=>r1(e,i,u))):r1(n,i,u)}return c.length?Promise.all(c).then(()=>i):i}});function r1(e,t,n){e.issues.length&&t.issues.push(...ea(n,e.issues)),t.value[n]=e.value}let r2=nt("$ZodRecord",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!T(i))return n.issues.push({expected:"record",code:"invalid_type",input:i,inst:e}),n;let a=[];if(t.keyType._zod.values){let o,c=t.keyType._zod.values;for(let e of(n.value={},c))if("string"==typeof e||"number"==typeof e||"symbol"==typeof e){let o=t.valueType._zod.run({value:i[e],issues:[]},r);o instanceof Promise?a.push(o.then(t=>{t.issues.length&&n.issues.push(...ea(e,t.issues)),n.value[e]=t.value})):(o.issues.length&&n.issues.push(...ea(e,o.issues)),n.value[e]=o.value)}for(let e in i)c.has(e)||(o=null!=o?o:[]).push(e);o&&o.length>0&&n.issues.push({code:"unrecognized_keys",input:i,inst:e,keys:o})}else for(let o of(n.value={},Reflect.ownKeys(i))){if("__proto__"===o)continue;let c=t.keyType._zod.run({value:o,issues:[]},r);if(c instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(c.issues.length){n.issues.push({code:"invalid_key",origin:"record",issues:c.issues.map(e=>ec(e,r,no())),input:o,path:[o],inst:e}),n.value[c.value]=c.value;continue}let u=t.valueType._zod.run({value:i[o],issues:[]},r);u instanceof Promise?a.push(u.then(e=>{e.issues.length&&n.issues.push(...ea(o,e.issues)),n.value[c.value]=e.value})):(u.issues.length&&n.issues.push(...ea(o,u.issues)),n.value[c.value]=u.value)}return a.length?Promise.all(a).then(()=>n):n}}),r9=nt("$ZodMap",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!(i instanceof Map))return n.issues.push({expected:"map",code:"invalid_type",input:i,inst:e}),n;let a=[];for(let[o,c]of(n.value=new Map,i)){let u=t.keyType._zod.run({value:o,issues:[]},r),l=t.valueType._zod.run({value:c,issues:[]},r);u instanceof Promise||l instanceof Promise?a.push(Promise.all([u,l]).then(t=>{let[a,c]=t;r3(a,c,n,o,i,e,r)})):r3(u,l,n,o,i,e,r)}return a.length?Promise.all(a).then(()=>n):n}});function r3(e,t,n,r,i,a,o){e.issues.length&&(M.has(typeof r)?n.issues.push(...ea(r,e.issues)):n.issues.push({code:"invalid_key",origin:"map",input:i,inst:a,issues:e.issues.map(e=>ec(e,o,no()))})),t.issues.length&&(M.has(typeof r)?n.issues.push(...ea(r,t.issues)):n.issues.push({origin:"map",code:"invalid_element",input:i,inst:a,key:r,issues:t.issues.map(e=>ec(e,o,no()))})),n.value.set(e.value,t.value)}let r5=nt("$ZodSet",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!(i instanceof Set))return n.issues.push({input:i,inst:e,expected:"set",code:"invalid_type"}),n;let a=[];for(let e of(n.value=new Set,i)){let i=t.valueType._zod.run({value:e,issues:[]},r);i instanceof Promise?a.push(i.then(e=>r8(e,n))):r8(i,n)}return a.length?Promise.all(a).then(()=>n):n}});function r8(e,t){e.issues.length&&t.issues.push(...e.issues),t.value.add(e.value)}let r7=nt("$ZodEnum",(e,t)=>{rt.init(e,t);let n=h(t.entries),r=new Set(n);e._zod.values=r,e._zod.pattern=new RegExp("^(".concat(n.filter(e=>M.has(typeof e)).map(e=>"string"==typeof e?J(e):e.toString()).join("|"),")$")),e._zod.parse=(t,i)=>{let a=t.value;return r.has(a)||t.issues.push({code:"invalid_value",values:n,input:a,inst:e}),t}}),ie=nt("$ZodLiteral",(e,t)=>{if(rt.init(e,t),0===t.values.length)throw Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=new RegExp("^(".concat(t.values.map(e=>"string"==typeof e?J(e):e?J(e.toString()):String(e)).join("|"),")$")),e._zod.parse=(n,r)=>{let i=n.value;return e._zod.values.has(i)||n.issues.push({code:"invalid_value",values:t.values,input:i,inst:e}),n}}),it=nt("$ZodFile",(e,t)=>{rt.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return r instanceof File||t.issues.push({expected:"file",code:"invalid_type",input:r,inst:e}),t}}),ir=nt("$ZodTransform",(e,t)=>{rt.init(e,t),e._zod.parse=(n,r)=>{if("backward"===r.direction)throw new ni(e.constructor.name);let i=t.transform(n.value,n);if(r.async)return(i instanceof Promise?i:Promise.resolve(i)).then(e=>(n.value=e,n));if(i instanceof Promise)throw new nr;return n.value=i,n}});function ii(e,t){return e.issues.length&&void 0===t?{issues:[],value:void 0}:e}let ia=nt("$ZodOptional",(e,t)=>{rt.init(e,t),e._zod.optin="optional",e._zod.optout="optional",z(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),z(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?new RegExp("^(".concat(x(e.source),")?$")):void 0}),e._zod.parse=(e,n)=>{if("optional"===t.innerType._zod.optin){let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(t=>ii(t,e.value)):ii(r,e.value)}return void 0===e.value?e:t.innerType._zod.run(e,n)}}),io=nt("$ZodNullable",(e,t)=>{rt.init(e,t),z(e._zod,"optin",()=>t.innerType._zod.optin),z(e._zod,"optout",()=>t.innerType._zod.optout),z(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?new RegExp("^(".concat(x(e.source),"|null)$")):void 0}),z(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),ic=nt("$ZodDefault",(e,t)=>{rt.init(e,t),e._zod.optin="optional",z(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if("backward"===n.direction)return t.innerType._zod.run(e,n);if(void 0===e.value)return e.value=t.defaultValue,e;let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e=>iu(e,t)):iu(r,t)}});function iu(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let il=nt("$ZodPrefault",(e,t)=>{rt.init(e,t),e._zod.optin="optional",z(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>("backward"===n.direction||void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),is=nt("$ZodNonOptional",(e,t)=>{rt.init(e,t),z(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,r)=>{let i=t.innerType._zod.run(n,r);return i instanceof Promise?i.then(t=>id(t,e)):id(i,e)}});function id(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let im=nt("$ZodSuccess",(e,t)=>{rt.init(e,t),e._zod.parse=(e,n)=>{if("backward"===n.direction)throw new ni("ZodSuccess");let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(t=>(e.value=0===t.issues.length,e)):(e.value=0===r.issues.length,e)}}),iv=nt("$ZodCatch",(e,t)=>{rt.init(e,t),z(e._zod,"optin",()=>t.innerType._zod.optin),z(e._zod,"optout",()=>t.innerType._zod.optout),z(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if("backward"===n.direction)return t.innerType._zod.run(e,n);let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(r=>(e.value=r.value,r.issues.length&&(e.value=t.catchValue({...e,error:{issues:r.issues.map(e=>ec(e,n,no()))},input:e.value}),e.issues=[]),e)):(e.value=r.value,r.issues.length&&(e.value=t.catchValue({...e,error:{issues:r.issues.map(e=>ec(e,n,no()))},input:e.value}),e.issues=[]),e)}}),ip=nt("$ZodNaN",(e,t)=>{rt.init(e,t),e._zod.parse=(t,n)=>("number"==typeof t.value&&Number.isNaN(t.value)||t.issues.push({input:t.value,inst:e,expected:"nan",code:"invalid_type"}),t)}),ig=nt("$ZodPipe",(e,t)=>{rt.init(e,t),z(e._zod,"values",()=>t.in._zod.values),z(e._zod,"optin",()=>t.in._zod.optin),z(e._zod,"optout",()=>t.out._zod.optout),z(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{if("backward"===n.direction){let r=t.out._zod.run(e,n);return r instanceof Promise?r.then(e=>ih(e,t.in,n)):ih(r,t.in,n)}let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>ih(e,t.out,n)):ih(r,t.out,n)}});function ih(e,t,n){return e.issues.length?(e.aborted=!0,e):t._zod.run({value:e.value,issues:e.issues},n)}let iy=nt("$ZodCodec",(e,t)=>{rt.init(e,t),z(e._zod,"values",()=>t.in._zod.values),z(e._zod,"optin",()=>t.in._zod.optin),z(e._zod,"optout",()=>t.out._zod.optout),z(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{if("forward"===(n.direction||"forward")){let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>i_(e,t,n)):i_(r,t,n)}{let r=t.out._zod.run(e,n);return r instanceof Promise?r.then(e=>i_(e,t,n)):i_(r,t,n)}}});function i_(e,t,n){if(e.issues.length)return e.aborted=!0,e;if("forward"===(n.direction||"forward")){let r=t.transform(e.value,e);return r instanceof Promise?r.then(r=>ib(e,r,t.out,n)):ib(e,r,t.out,n)}{let r=t.reverseTransform(e.value,e);return r instanceof Promise?r.then(r=>ib(e,r,t.in,n)):ib(e,r,t.in,n)}}function ib(e,t,n,r){return e.issues.length?(e.aborted=!0,e):n._zod.run({value:t,issues:e.issues},r)}let ik=nt("$ZodReadonly",(e,t)=>{rt.init(e,t),z(e._zod,"propValues",()=>t.innerType._zod.propValues),z(e._zod,"values",()=>t.innerType._zod.values),z(e._zod,"optin",()=>t.innerType._zod.optin),z(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{if("backward"===n.direction)return t.innerType._zod.run(e,n);let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(ix):ix(r)}});function ix(e){return e.value=Object.freeze(e.value),e}let iI=nt("$ZodTemplateLiteral",(e,t)=>{rt.init(e,t);let n=[];for(let e of t.parts)if("object"==typeof e&&null!==e){if(!e._zod.pattern)throw Error("Invalid template literal part, no pattern found: ".concat([...e._zod.traits].shift()));let t=e._zod.pattern instanceof RegExp?e._zod.pattern.source:e._zod.pattern;if(!t)throw Error("Invalid template literal part: ".concat(e._zod.traits));let r=+!!t.startsWith("^"),i=t.endsWith("$")?t.length-1:t.length;n.push(t.slice(r,i))}else if(null===e||F.has(typeof e))n.push(J("".concat(e)));else throw Error("Invalid template literal part: ".concat(e));e._zod.pattern=new RegExp("^".concat(n.join(""),"$")),e._zod.parse=(n,r)=>{if("string"!=typeof n.value)return n.issues.push({input:n.value,inst:e,expected:"template_literal",code:"invalid_type"}),n;if(e._zod.pattern.lastIndex=0,!e._zod.pattern.test(n.value)){var i;n.issues.push({input:n.value,inst:e,code:"invalid_format",format:null!=(i=t.format)?i:"template_literal",pattern:e._zod.pattern.source})}return n}}),iw=nt("$ZodFunction",(e,t)=>(rt.init(e,t),e._def=t,e._zod.def=t,e.implement=t=>{if("function"!=typeof t)throw Error("implement() must be called with a function");return function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];let a=Reflect.apply(t,this,e._def.input?ng(e._def.input,r):r);return e._def.output?ng(e._def.output,a):a}},e.implementAsync=t=>{if("function"!=typeof t)throw Error("implementAsync() must be called with a function");return async function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];let a=e._def.input?await ny(e._def.input,r):r,o=await Reflect.apply(t,this,a);return e._def.output?await ny(e._def.output,o):o}},e._zod.parse=(t,n)=>("function"!=typeof t.value?t.issues.push({code:"invalid_type",expected:"function",input:t.value,inst:e}):e._def.output&&"promise"===e._def.output._zod.def.type?t.value=e.implementAsync(t.value):t.value=e.implement(t.value),t),e.input=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=e.constructor;return new i(Array.isArray(n[0])?{type:"function",input:new r6({type:"tuple",items:n[0],rest:n[1]}),output:e._def.output}:{type:"function",input:n[0],output:e._def.output})},e.output=t=>new e.constructor({type:"function",input:e._def.input,output:t}),e)),iz=nt("$ZodPromise",(e,t)=>{rt.init(e,t),e._zod.parse=(e,n)=>Promise.resolve(e.value).then(e=>t.innerType._zod.run({value:e,issues:[]},n))}),iS=nt("$ZodLazy",(e,t)=>{rt.init(e,t),z(e._zod,"innerType",()=>t.getter()),z(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),z(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),z(e._zod,"optin",()=>{var t;return null!=(t=e._zod.innerType._zod.optin)?t:void 0}),z(e._zod,"optout",()=>{var t;return null!=(t=e._zod.innerType._zod.optout)?t:void 0}),e._zod.parse=(t,n)=>e._zod.innerType._zod.run(t,n)}),ij=nt("$ZodCustom",(e,t)=>{nL.init(e,t),rt.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let r=n.value,i=t.fn(r);if(i instanceof Promise)return i.then(t=>iZ(t,n,r,e));iZ(i,n,r,e)}});function iZ(e,t,n,r){if(!e){var i;let e={code:"custom",input:n,inst:r,path:[...null!=(i=r._zod.def.path)?i:[]],continue:!r._zod.def.abort};r._zod.def.params&&(e.params=r._zod.def.params),t.issues.push(es(e))}}e.i(36187),e.i(57088),e.i(69751),e.i(68346);var iU=e_,iO=th,iN=t8;e.s(["$ZodRegistry",()=>iP,"$input",()=>i$,"$output",()=>iD,"globalRegistry",()=>iA,"registry",()=>iE],95832);let iD=Symbol("ZodOutput"),i$=Symbol("ZodInput");class iP{add(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let i=n[0];if(this._map.set(e,i),i&&"object"==typeof i&&"id"in i){if(this._idmap.has(i.id))throw Error("ID ".concat(i.id," already exists in the registry"));this._idmap.set(i.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){var n;let r={...null!=(n=this.get(t))?n:{}};delete r.id;let i={...r,...this._map.get(e)};return Object.keys(i).length?i:void 0}return this._map.get(e)}has(e){return this._map.has(e)}constructor(){this._map=new Map,this._idmap=new Map}}function iE(){return new iP}let iA=iE();function iT(e,t){return new e({type:"string",...W(t)})}function iC(e,t){return new e({type:"string",coerce:!0,...W(t)})}function iL(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...W(t)})}function iR(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...W(t)})}function iM(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...W(t)})}function iF(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...W(t)})}function iJ(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...W(t)})}function iB(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...W(t)})}function iW(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...W(t)})}function iV(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...W(t)})}function iG(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...W(t)})}function iK(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...W(t)})}function iX(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...W(t)})}function iq(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...W(t)})}function iY(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...W(t)})}function iH(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...W(t)})}function iQ(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...W(t)})}function i0(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...W(t)})}function i4(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...W(t)})}function i6(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...W(t)})}function i1(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...W(t)})}function i2(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...W(t)})}function i9(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...W(t)})}function i3(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...W(t)})}e.i(95832),e.i(49463),e.s(["TimePrecision",()=>i5,"_any",()=>ay,"_array",()=>aQ,"_base64",()=>i1,"_base64url",()=>i2,"_bigint",()=>ad,"_boolean",()=>al,"_catch",()=>oc,"_check",()=>og,"_cidrv4",()=>i4,"_cidrv6",()=>i6,"_coercedBigint",()=>am,"_coercedBoolean",()=>as,"_coercedDate",()=>aI,"_coercedNumber",()=>ar,"_coercedString",()=>iC,"_cuid",()=>iK,"_cuid2",()=>iX,"_custom",()=>of,"_date",()=>ax,"_default",()=>oi,"_discriminatedUnion",()=>a4,"_e164",()=>i9,"_email",()=>iL,"_emoji",()=>iV,"_endsWith",()=>aW,"_enum",()=>a5,"_file",()=>oe,"_float32",()=>aa,"_float64",()=>ao,"_gt",()=>aj,"_gte",()=>aZ,"_guid",()=>iR,"_includes",()=>aJ,"_int",()=>ai,"_int32",()=>ac,"_int64",()=>af,"_intersection",()=>a6,"_ipv4",()=>iQ,"_ipv6",()=>i0,"_isoDate",()=>i7,"_isoDateTime",()=>i8,"_isoDuration",()=>at,"_isoTime",()=>ae,"_jwt",()=>i3,"_ksuid",()=>iH,"_lazy",()=>od,"_length",()=>aL,"_literal",()=>a7,"_lowercase",()=>aM,"_lt",()=>az,"_lte",()=>aS,"_map",()=>a9,"_max",()=>aS,"_maxLength",()=>aT,"_maxSize",()=>aP,"_mime",()=>aG,"_min",()=>aZ,"_minLength",()=>aC,"_minSize",()=>aE,"_multipleOf",()=>a$,"_nan",()=>aw,"_nanoid",()=>iG,"_nativeEnum",()=>a8,"_negative",()=>aO,"_never",()=>ab,"_nonnegative",()=>aD,"_nonoptional",()=>oa,"_nonpositive",()=>aN,"_normalize",()=>aX,"_null",()=>ah,"_nullable",()=>or,"_number",()=>an,"_optional",()=>on,"_overwrite",()=>aK,"_pipe",()=>ou,"_positive",()=>aU,"_promise",()=>om,"_property",()=>aV,"_readonly",()=>ol,"_record",()=>a2,"_refine",()=>ov,"_regex",()=>aR,"_set",()=>a3,"_size",()=>aA,"_startsWith",()=>aB,"_string",()=>iT,"_stringFormat",()=>oy,"_stringbool",()=>oh,"_success",()=>oo,"_superRefine",()=>op,"_symbol",()=>ap,"_templateLiteral",()=>os,"_toLowerCase",()=>aY,"_toUpperCase",()=>aH,"_transform",()=>ot,"_trim",()=>aq,"_tuple",()=>a1,"_uint32",()=>au,"_uint64",()=>av,"_ulid",()=>iq,"_undefined",()=>ag,"_union",()=>a0,"_unknown",()=>a_,"_uppercase",()=>aF,"_url",()=>iW,"_uuid",()=>iM,"_uuidv4",()=>iF,"_uuidv6",()=>iJ,"_uuidv7",()=>iB,"_void",()=>ak,"_xid",()=>iY],69912);let i5={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function i8(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...W(t)})}function i7(e,t){return new e({type:"string",format:"date",check:"string_format",...W(t)})}function ae(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...W(t)})}function at(e,t){return new e({type:"string",format:"duration",check:"string_format",...W(t)})}function an(e,t){return new e({type:"number",checks:[],...W(t)})}function ar(e,t){return new e({type:"number",coerce:!0,checks:[],...W(t)})}function ai(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...W(t)})}function aa(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float32",...W(t)})}function ao(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float64",...W(t)})}function ac(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"int32",...W(t)})}function au(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"uint32",...W(t)})}function al(e,t){return new e({type:"boolean",...W(t)})}function as(e,t){return new e({type:"boolean",coerce:!0,...W(t)})}function ad(e,t){return new e({type:"bigint",...W(t)})}function am(e,t){return new e({type:"bigint",coerce:!0,...W(t)})}function af(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...W(t)})}function av(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...W(t)})}function ap(e,t){return new e({type:"symbol",...W(t)})}function ag(e,t){return new e({type:"undefined",...W(t)})}function ah(e,t){return new e({type:"null",...W(t)})}function ay(e){return new e({type:"any"})}function a_(e){return new e({type:"unknown"})}function ab(e,t){return new e({type:"never",...W(t)})}function ak(e,t){return new e({type:"void",...W(t)})}function ax(e,t){return new e({type:"date",...W(t)})}function aI(e,t){return new e({type:"date",coerce:!0,...W(t)})}function aw(e,t){return new e({type:"nan",...W(t)})}function az(e,t){return new nM({check:"less_than",...W(t),value:e,inclusive:!1})}function aS(e,t){return new nM({check:"less_than",...W(t),value:e,inclusive:!0})}function aj(e,t){return new nF({check:"greater_than",...W(t),value:e,inclusive:!1})}function aZ(e,t){return new nF({check:"greater_than",...W(t),value:e,inclusive:!0})}function aU(e){return aj(0,e)}function aO(e){return az(0,e)}function aN(e){return aS(0,e)}function aD(e){return aZ(0,e)}function a$(e,t){return new nJ({check:"multiple_of",...W(t),value:e})}function aP(e,t){return new nV({check:"max_size",...W(t),maximum:e})}function aE(e,t){return new nG({check:"min_size",...W(t),minimum:e})}function aA(e,t){return new nK({check:"size_equals",...W(t),size:e})}function aT(e,t){return new nX({check:"max_length",...W(t),maximum:e})}function aC(e,t){return new nq({check:"min_length",...W(t),minimum:e})}function aL(e,t){return new nY({check:"length_equals",...W(t),length:e})}function aR(e,t){return new nQ({check:"string_format",format:"regex",...W(t),pattern:e})}function aM(e){return new n0({check:"string_format",format:"lowercase",...W(e)})}function aF(e){return new n4({check:"string_format",format:"uppercase",...W(e)})}function aJ(e,t){return new n6({check:"string_format",format:"includes",...W(t),includes:e})}function aB(e,t){return new n1({check:"string_format",format:"starts_with",...W(t),prefix:e})}function aW(e,t){return new n2({check:"string_format",format:"ends_with",...W(t),suffix:e})}function aV(e,t,n){return new n3({check:"property",property:e,schema:t,...W(n)})}function aG(e,t){return new n5({check:"mime_type",mime:e,...W(t)})}function aK(e){return new n8({check:"overwrite",tx:e})}function aX(e){return aK(t=>t.normalize(e))}function aq(){return aK(e=>e.trim())}function aY(){return aK(e=>e.toLowerCase())}function aH(){return aK(e=>e.toUpperCase())}function aQ(e,t,n){return new e({type:"array",element:t,...W(n)})}function a0(e,t,n){return new e({type:"union",options:t,...W(n)})}function a4(e,t,n,r){return new e({type:"union",options:n,discriminator:t,...W(r)})}function a6(e,t,n){return new e({type:"intersection",left:t,right:n})}function a1(e,t,n,r){let i=n instanceof rt,a=i?r:n;return new e({type:"tuple",items:t,rest:i?n:null,...W(a)})}function a2(e,t,n,r){return new e({type:"record",keyType:t,valueType:n,...W(r)})}function a9(e,t,n,r){return new e({type:"map",keyType:t,valueType:n,...W(r)})}function a3(e,t,n){return new e({type:"set",valueType:t,...W(n)})}function a5(e,t,n){return new e({type:"enum",entries:Array.isArray(t)?Object.fromEntries(t.map(e=>[e,e])):t,...W(n)})}function a8(e,t,n){return new e({type:"enum",entries:t,...W(n)})}function a7(e,t,n){return new e({type:"literal",values:Array.isArray(t)?t:[t],...W(n)})}function oe(e,t){return new e({type:"file",...W(t)})}function ot(e,t){return new e({type:"transform",transform:t})}function on(e,t){return new e({type:"optional",innerType:t})}function or(e,t){return new e({type:"nullable",innerType:t})}function oi(e,t,n){return new e({type:"default",innerType:t,get defaultValue(){return"function"==typeof n?n():C(n)}})}function oa(e,t,n){return new e({type:"nonoptional",innerType:t,...W(n)})}function oo(e,t){return new e({type:"success",innerType:t})}function oc(e,t,n){return new e({type:"catch",innerType:t,catchValue:"function"==typeof n?n:()=>n})}function ou(e,t,n){return new e({type:"pipe",in:t,out:n})}function ol(e,t){return new e({type:"readonly",innerType:t})}function os(e,t,n){return new e({type:"template_literal",parts:t,...W(n)})}function od(e,t){return new e({type:"lazy",getter:t})}function om(e,t){return new e({type:"promise",innerType:t})}function of(e,t,n){let r=W(n);return null!=r.abort||(r.abort=!0),new e({type:"custom",check:"custom",fn:t,...r})}function ov(e,t,n){return new e({type:"custom",check:"custom",fn:t,...W(n)})}function op(e){let t=og(n=>(n.addIssue=e=>{if("string"==typeof e)n.issues.push(es(e,n.value,t._zod.def));else e.fatal&&(e.continue=!1),null!=e.code||(e.code="custom"),null!=e.input||(e.input=n.value),null!=e.inst||(e.inst=t),null!=e.continue||(e.continue=!t._zod.def.abort),n.issues.push(es(e))},e(n.value,n)));return t}function og(e,t){let n=new nL({check:"custom",...W(t)});return n._zod.check=e,n}function oh(e,t){var n,r,i,a,o;let c=W(t),u=null!=(n=c.truthy)?n:["true","1","yes","on","y","enabled"],l=null!=(r=c.falsy)?r:["false","0","no","off","n","disabled"];"sensitive"!==c.case&&(u=u.map(e=>"string"==typeof e?e.toLowerCase():e),l=l.map(e=>"string"==typeof e?e.toLowerCase():e));let s=new Set(u),d=new Set(l),m=null!=(i=e.Codec)?i:iy,f=null!=(a=e.Boolean)?a:r$,v=new m({type:"pipe",in:new(null!=(o=e.String)?o:rn)({type:"string",error:c.error}),out:new f({type:"boolean",error:c.error}),transform:(e,t)=>{let n=e;return"sensitive"!==c.case&&(n=n.toLowerCase()),!!s.has(n)||!d.has(n)&&(t.issues.push({code:"invalid_value",expected:"stringbool",values:[...s,...d],input:t.value,inst:v,continue:!1}),{})},reverseTransform:(e,t)=>!0===e?u[0]||"true":l[0]||"false",error:c.error});return v}function oy(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=W(r),a={...W(r),check:"string_format",type:"string",format:t,fn:"function"==typeof n?n:e=>n.test(e),...i};return n instanceof RegExp&&(a.pattern=n),new e(a)}e.i(69912),e.s(["JSONSchemaGenerator",()=>o_,"toJSONSchema",()=>ob],43147);class o_{process(e){var t,n,r,i,a;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:[],schemaPath:[]},c=e._zod.def,u=this.seen.get(e);if(u)return u.count++,o.schemaPath.includes(e)&&(u.cycle=o.path),u.schema;let l={schema:{},count:1,cycle:void 0,path:o.path};this.seen.set(e,l);let s=null==(t=(n=e._zod).toJSONSchema)?void 0:t.call(n);if(s)l.schema=s;else{let t={...o,schemaPath:[...o.schemaPath,e],path:o.path},n=e._zod.parent;if(n)l.ref=n,this.process(n,t),this.seen.get(n).isParent=!0;else{let n=l.schema;switch(c.type){case"string":{n.type="string";let{minimum:t,maximum:r,format:a,patterns:o,contentEncoding:c}=e._zod.bag;if("number"==typeof t&&(n.minLength=t),"number"==typeof r&&(n.maxLength=r),a&&(n.format=null!=(i=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[a])?i:a,""===n.format&&delete n.format),c&&(n.contentEncoding=c),o&&o.size>0){let e=[...o];1===e.length?n.pattern=e[0].source:e.length>1&&(l.schema.allOf=[...e.map(e=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:t,maximum:r,format:i,multipleOf:a,exclusiveMaximum:o,exclusiveMinimum:c}=e._zod.bag;"string"==typeof i&&i.includes("int")?n.type="integer":n.type="number","number"==typeof c&&("draft-4"===this.target||"openapi-3.0"===this.target?(n.minimum=c,n.exclusiveMinimum=!0):n.exclusiveMinimum=c),"number"==typeof t&&(n.minimum=t,"number"==typeof c&&"draft-4"!==this.target&&(c>=t?delete n.minimum:delete n.exclusiveMinimum)),"number"==typeof o&&("draft-4"===this.target||"openapi-3.0"===this.target?(n.maximum=o,n.exclusiveMaximum=!0):n.exclusiveMaximum=o),"number"==typeof r&&(n.maximum=r,"number"==typeof o&&"draft-4"!==this.target&&(o<=r?delete n.maximum:delete n.exclusiveMaximum)),"number"==typeof a&&(n.multipleOf=a);break}case"boolean":case"success":n.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(n.type="string",n.nullable=!0,n.enum=[null]):n.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":n.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:r,maximum:i}=e._zod.bag;"number"==typeof r&&(n.minItems=r),"number"==typeof i&&(n.maxItems=i),n.type="array",n.items=this.process(c.element,{...t,path:[...t.path,"items"]});break}case"object":{n.type="object",n.properties={};let e=c.shape;for(let r in e)n.properties[r]=this.process(e[r],{...t,path:[...t.path,"properties",r]});let r=new Set([...new Set(Object.keys(e))].filter(e=>{let t=c.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));r.size>0&&(n.required=Array.from(r)),(null==(a=c.catchall)?void 0:a._zod.def.type)==="never"?n.additionalProperties=!1:c.catchall?c.catchall&&(n.additionalProperties=this.process(c.catchall,{...t,path:[...t.path,"additionalProperties"]})):"output"===this.io&&(n.additionalProperties=!1);break}case"union":n.anyOf=c.options.map((e,n)=>this.process(e,{...t,path:[...t.path,"anyOf",n]}));break;case"intersection":{let e=this.process(c.left,{...t,path:[...t.path,"allOf",0]}),r=this.process(c.right,{...t,path:[...t.path,"allOf",1]}),i=e=>"allOf"in e&&1===Object.keys(e).length;n.allOf=[...i(e)?e.allOf:[e],...i(r)?r.allOf:[r]];break}case"tuple":{n.type="array";let r="draft-2020-12"===this.target?"prefixItems":"items",i="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",a=c.items.map((e,n)=>this.process(e,{...t,path:[...t.path,r,n]})),o=c.rest?this.process(c.rest,{...t,path:[...t.path,i,..."openapi-3.0"===this.target?[c.items.length]:[]]}):null;"draft-2020-12"===this.target?(n.prefixItems=a,o&&(n.items=o)):"openapi-3.0"===this.target?(n.items={anyOf:a},o&&n.items.anyOf.push(o),n.minItems=a.length,o||(n.maxItems=a.length)):(n.items=a,o&&(n.additionalItems=o));let{minimum:u,maximum:l}=e._zod.bag;"number"==typeof u&&(n.minItems=u),"number"==typeof l&&(n.maxItems=l);break}case"record":n.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(n.propertyNames=this.process(c.keyType,{...t,path:[...t.path,"propertyNames"]})),n.additionalProperties=this.process(c.valueType,{...t,path:[...t.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=h(c.entries);e.every(e=>"number"==typeof e)&&(n.type="number"),e.every(e=>"string"==typeof e)&&(n.type="string"),n.enum=e;break}case"literal":{let e=[];for(let t of c.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let t=e[0];n.type=null===t?"null":typeof t,"draft-4"===this.target||"openapi-3.0"===this.target?n.enum=[t]:n.const=t}else e.every(e=>"number"==typeof e)&&(n.type="number"),e.every(e=>"string"==typeof e)&&(n.type="string"),e.every(e=>"boolean"==typeof e)&&(n.type="string"),e.every(e=>null===e)&&(n.type="null"),n.enum=e;break}case"file":{let t={type:"string",format:"binary",contentEncoding:"binary"},{minimum:r,maximum:i,mime:a}=e._zod.bag;void 0!==r&&(t.minLength=r),void 0!==i&&(t.maxLength=i),a?1===a.length?(t.contentMediaType=a[0],Object.assign(n,t)):n.anyOf=a.map(e=>({...t,contentMediaType:e})):Object.assign(n,t);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let e=this.process(c.innerType,t);"openapi-3.0"===this.target?(l.ref=c.innerType,n.nullable=!0):n.anyOf=[e,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(c.innerType,t),l.ref=c.innerType;break;case"default":this.process(c.innerType,t),l.ref=c.innerType,n.default=JSON.parse(JSON.stringify(c.defaultValue));break;case"prefault":this.process(c.innerType,t),l.ref=c.innerType,"input"===this.io&&(n._prefault=JSON.parse(JSON.stringify(c.defaultValue)));break;case"catch":{let e;this.process(c.innerType,t),l.ref=c.innerType;try{e=c.catchValue(void 0)}catch(e){throw Error("Dynamic catch values are not supported in JSON Schema")}n.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let t=e._zod.pattern;if(!t)throw Error("Pattern not found in template literal");n.type="string",n.pattern=t.source;break}case"pipe":{let e="input"===this.io?"transform"===c.in._zod.def.type?c.out:c.in:c.out;this.process(e,t),l.ref=e;break}case"readonly":this.process(c.innerType,t),l.ref=c.innerType,n.readOnly=!0;break;case"lazy":{let n=e._zod.innerType;this.process(n,t),l.ref=n;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let d=this.metadataRegistry.get(e);return d&&Object.assign(l.schema,d),"input"===this.io&&function e(t,n){let r=null!=n?n:{seen:new Set};if(r.seen.has(t))return!1;r.seen.add(t);let i=t._zod.def;switch(i.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return e(i.element,r);case"object":for(let t in i.shape)if(e(i.shape[t],r))return!0;return!1;case"union":for(let t of i.options)if(e(t,r))return!0;return!1;case"intersection":return e(i.left,r)||e(i.right,r);case"tuple":for(let t of i.items)if(e(t,r))return!0;if(i.rest&&e(i.rest,r))return!0;return!1;case"record":case"map":return e(i.keyType,r)||e(i.valueType,r);case"set":return e(i.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(i.innerType,r);case"lazy":return e(i.getter(),r);case"transform":return!0;case"pipe":return e(i.in,r)||e(i.out,r)}throw Error("Unknown schema type: ".concat(i.type))}(e)&&(delete l.schema.examples,delete l.schema.default),"input"===this.io&&l.schema._prefault&&(null!=(r=l.schema).default||(r.default=l.schema._prefault)),delete l.schema._prefault,this.seen.get(e).schema}emit(e,t){var n,r,i,a,o,c,u,l,s,d;let m={cycles:null!=(i=null==t?void 0:t.cycles)?i:"ref",reused:null!=(a=null==t?void 0:t.reused)?a:"inline",external:null!=(o=null==t?void 0:t.external)?o:void 0},f=this.seen.get(e);if(!f)throw Error("Unprocessed schema. This is a bug in Zod.");let v=e=>{var t,n,r,i,a;let o="draft-2020-12"===this.target?"$defs":"definitions";if(m.external){let a=null==(t=m.external.registry.get(e[0]))?void 0:t.id,c=null!=(n=m.external.uri)?n:e=>e;if(a)return{ref:c(a)};let u=null!=(i=null!=(r=e[1].defId)?r:e[1].schema.id)?i:"schema".concat(this.counter++);return e[1].defId=u,{defId:u,ref:"".concat(c("__shared"),"#/").concat(o,"/").concat(u)}}if(e[1]===f)return{ref:"#"};let c="".concat("#","/").concat(o,"/"),u=null!=(a=e[1].schema.id)?a:"__schema".concat(this.counter++);return{defId:u,ref:c+u}},p=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:n,defId:r}=v(e);t.def={...t.schema},r&&(t.defId=r);let i=t.schema;for(let e in i)delete i[e];i.$ref=n};if("throw"===m.cycles)for(let e of this.seen.entries()){let t=e[1];if(t.cycle)throw Error("Cycle detected: "+"#/".concat(null==(c=t.cycle)?void 0:c.join("/"),"/<root>")+'\n\nSet the `cycles` parameter to `"ref"` to resolve cyclical schemas with defs.')}for(let t of this.seen.entries()){let n=t[1];if(e===t[0]){p(t);continue}if(m.external){let n=null==(l=m.external.registry.get(t[0]))?void 0:l.id;if(e!==t[0]&&n){p(t);continue}}if((null==(u=this.metadataRegistry.get(t[0]))?void 0:u.id)||n.cycle||n.count>1&&"ref"===m.reused){p(t);continue}}let g=(e,t)=>{var n,r,i;let a=this.seen.get(e),o=null!=(n=a.def)?n:a.schema,c={...o};if(null===a.ref)return;let u=a.ref;if(a.ref=null,u){g(u,t);let e=this.seen.get(u).schema;e.$ref&&("draft-7"===t.target||"draft-4"===t.target||"openapi-3.0"===t.target)?(o.allOf=null!=(r=o.allOf)?r:[],o.allOf.push(e)):(Object.assign(o,e),Object.assign(o,c))}a.isParent||this.override({zodSchema:e,jsonSchema:o,path:null!=(i=a.path)?i:[]})};for(let e of[...this.seen.entries()].reverse())g(e[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?h.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn("Invalid target: ".concat(this.target)),null==(n=m.external)?void 0:n.uri){let t=null==(s=m.external.registry.get(e))?void 0:s.id;if(!t)throw Error("Schema is missing an `id` property");h.$id=m.external.uri(t)}Object.assign(h,f.def);let y=null!=(d=null==(r=m.external)?void 0:r.defs)?d:{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(y[t.defId]=t.def)}m.external||Object.keys(y).length>0&&("draft-2020-12"===this.target?h.$defs=y:h.definitions=y);try{return JSON.parse(JSON.stringify(h))}catch(e){throw Error("Error converting schema to JSON.")}}constructor(e){var t,n,r,i,a;this.counter=0,this.metadataRegistry=null!=(t=null==e?void 0:e.metadata)?t:iA,this.target=null!=(n=null==e?void 0:e.target)?n:"draft-2020-12",this.unrepresentable=null!=(r=null==e?void 0:e.unrepresentable)?r:"throw",this.override=null!=(i=null==e?void 0:e.override)?i:()=>{},this.io=null!=(a=null==e?void 0:e.io)?a:"output",this.seen=new Map}}function ob(e,t){if(e instanceof iP){let n=new o_(t),r={};for(let t of e._idmap.entries()){let[e,r]=t;n.process(r)}let i={},a={registry:e,uri:null==t?void 0:t.uri,defs:r};for(let r of e._idmap.entries()){let[e,o]=r;i[e]=n.emit(o,{...t,external:a})}return Object.keys(r).length>0&&(i.__shared={["draft-2020-12"===n.target?"$defs":"definitions"]:r}),{schemas:i}}let n=new o_(t);return n.process(e),n.emit(e,t)}e.i(43147);var ok=t7,ox=e.i(36891);e.s(["ZodISODate",()=>li,"ZodISODateTime",()=>ln,"ZodISODuration",()=>lu,"ZodISOTime",()=>lo,"date",()=>la,"datetime",()=>lr,"duration",()=>ll,"time",()=>lc],77504),e.s(["ZodAny",()=>cX,"ZodArray",()=>c9,"ZodBase64",()=>cp,"ZodBase64URL",()=>ch,"ZodBigInt",()=>cC,"ZodBigIntFormat",()=>cR,"ZodBoolean",()=>cA,"ZodCIDRv4",()=>cd,"ZodCIDRv6",()=>cf,"ZodCUID",()=>o5,"ZodCUID2",()=>o7,"ZodCatch",()=>uL,"ZodCodec",()=>uW,"ZodCustom",()=>u1,"ZodCustomStringFormat",()=>cI,"ZodDate",()=>c1,"ZodDefault",()=>uN,"ZodDiscriminatedUnion",()=>ui,"ZodE164",()=>c_,"ZodEmail",()=>oW,"ZodEmoji",()=>o1,"ZodEnum",()=>uh,"ZodFile",()=>ux,"ZodFunction",()=>u4,"ZodGUID",()=>oG,"ZodIPv4",()=>cc,"ZodIPv6",()=>cl,"ZodIntersection",()=>uo,"ZodJWT",()=>ck,"ZodKSUID",()=>ca,"ZodLazy",()=>uY,"ZodLiteral",()=>ub,"ZodMap",()=>uf,"ZodNaN",()=>uM,"ZodNanoID",()=>o9,"ZodNever",()=>cQ,"ZodNonOptional",()=>uE,"ZodNull",()=>cG,"ZodNullable",()=>uZ,"ZodNumber",()=>cZ,"ZodNumberFormat",()=>cO,"ZodObject",()=>c8,"ZodOptional",()=>uS,"ZodPipe",()=>uJ,"ZodPrefault",()=>u$,"ZodPromise",()=>uQ,"ZodReadonly",()=>uG,"ZodRecord",()=>us,"ZodSet",()=>up,"ZodString",()=>oF,"ZodStringFormat",()=>oB,"ZodSuccess",()=>uT,"ZodSymbol",()=>cJ,"ZodTemplateLiteral",()=>uX,"ZodTransform",()=>uw,"ZodTuple",()=>uu,"ZodType",()=>oR,"ZodULID",()=>ct,"ZodURL",()=>o0,"ZodUUID",()=>oX,"ZodUndefined",()=>cW,"ZodUnion",()=>un,"ZodUnknown",()=>cY,"ZodVoid",()=>c4,"ZodXID",()=>cr,"_ZodString",()=>oM,"_default",()=>uD,"_function",()=>u6,"any",()=>cq,"array",()=>c3,"base64",()=>cg,"base64url",()=>cy,"bigint",()=>cL,"boolean",()=>cT,"catch",()=>uR,"check",()=>u2,"cidrv4",()=>cm,"cidrv6",()=>cv,"codec",()=>uV,"cuid",()=>o8,"cuid2",()=>ce,"custom",()=>u9,"date",()=>c2,"discriminatedUnion",()=>ua,"e164",()=>cb,"email",()=>oV,"emoji",()=>o2,"enum",()=>uy,"file",()=>uI,"float32",()=>cD,"float64",()=>c$,"function",()=>u6,"guid",()=>oK,"hash",()=>cj,"hex",()=>cS,"hostname",()=>cz,"httpUrl",()=>o6,"instanceof",()=>u8,"int",()=>cN,"int32",()=>cP,"int64",()=>cM,"intersection",()=>uc,"ipv4",()=>cu,"ipv6",()=>cs,"json",()=>le,"jwt",()=>cx,"keyof",()=>c5,"ksuid",()=>co,"lazy",()=>uH,"literal",()=>uk,"looseObject",()=>ut,"map",()=>uv,"nan",()=>uF,"nanoid",()=>o3,"nativeEnum",()=>u_,"never",()=>c0,"nonoptional",()=>uA,"null",()=>cK,"nullable",()=>uU,"nullish",()=>uO,"number",()=>cU,"object",()=>c7,"optional",()=>uj,"partialRecord",()=>um,"pipe",()=>uB,"prefault",()=>uP,"preprocess",()=>lt,"promise",()=>u0,"readonly",()=>uK,"record",()=>ud,"refine",()=>u3,"set",()=>ug,"strictObject",()=>ue,"string",()=>oJ,"stringFormat",()=>cw,"stringbool",()=>u7,"success",()=>uC,"superRefine",()=>u5,"symbol",()=>cB,"templateLiteral",()=>uq,"transform",()=>uz,"tuple",()=>ul,"uint32",()=>cE,"uint64",()=>cF,"ulid",()=>cn,"undefined",()=>cV,"union",()=>ur,"unknown",()=>cH,"url",()=>o4,"uuid",()=>oq,"uuidv4",()=>oY,"uuidv6",()=>oH,"uuidv7",()=>oQ,"void",()=>c6,"xid",()=>ci],77738);var oI=th,ow=e_;e.s(["decode",()=>o$,"decodeAsync",()=>oE,"encode",()=>oD,"encodeAsync",()=>oP,"parse",()=>oZ,"parseAsync",()=>oU,"safeDecode",()=>oT,"safeDecodeAsync",()=>oL,"safeEncode",()=>oA,"safeEncodeAsync",()=>oC,"safeParse",()=>oO,"safeParseAsync",()=>oN],57261),e.s(["ZodError",()=>oS,"ZodRealError",()=>oj],90592);let oz=(e,t)=>{nu.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>nd(e,t)},flatten:{value:t=>ns(e,t)},addIssue:{value:t=>{e.issues.push(t),e.message=JSON.stringify(e.issues,_,2)}},addIssues:{value:t=>{e.issues.push(...t),e.message=JSON.stringify(e.issues,_,2)}},isEmpty:{get:()=>0===e.issues.length}})},oS=nt("ZodError",oz),oj=nt("ZodError",oz,{Parent:Error}),oZ=np(oj),oU=nh(oj),oO=n_(oj),oN=nk(oj),oD=nI(oj),o$=nz(oj),oP=nj(oj),oE=nU(oj),oA=nN(oj),oT=n$(oj),oC=nE(oj),oL=nT(oj),oR=nt("ZodType",(e,t)=>(rt.init(e,t),e.def=t,e.type=t.type,Object.defineProperty(e,"_def",{value:t}),e.check=function(){for(var n,r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return e.clone({...t,checks:[...null!=(n=t.checks)?n:[],...i.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]})},e.clone=(t,n)=>B(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>oZ(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>oO(e,t,n),e.parseAsync=async(t,n)=>oU(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>oN(e,t,n),e.spa=e.safeParseAsync,e.encode=(t,n)=>oD(e,t,n),e.decode=(t,n)=>o$(e,t,n),e.encodeAsync=async(t,n)=>oP(e,t,n),e.decodeAsync=async(t,n)=>oE(e,t,n),e.safeEncode=(t,n)=>oA(e,t,n),e.safeDecode=(t,n)=>oT(e,t,n),e.safeEncodeAsync=async(t,n)=>oC(e,t,n),e.safeDecodeAsync=async(t,n)=>oL(e,t,n),e.refine=(t,n)=>e.check(u3(t,n)),e.superRefine=t=>e.check(op(t)),e.overwrite=t=>e.check(aK(t)),e.optional=()=>uj(e),e.nullable=()=>uU(e),e.nullish=()=>uj(uU(e)),e.nonoptional=t=>uA(e,t),e.array=()=>c3(e),e.or=t=>ur([e,t]),e.and=t=>uc(e,t),e.transform=t=>uB(e,uz(t)),e.default=t=>uD(e,t),e.prefault=t=>uP(e,t),e.catch=t=>uR(e,t),e.pipe=t=>uB(e,t),e.readonly=()=>uK(e),e.describe=t=>{let n=e.clone();return iA.add(n,{description:t}),n},Object.defineProperty(e,"description",{get(){var t;return null==(t=iA.get(e))?void 0:t.description},configurable:!0}),e.meta=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];if(0===n.length)return iA.get(e);let i=e.clone();return iA.add(i,n[0]),i},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),oM=nt("_ZodString",(e,t)=>{var n,r,i;rn.init(e,t),oR.init(e,t);let a=e._zod.bag;e.format=null!=(n=a.format)?n:null,e.minLength=null!=(r=a.minimum)?r:null,e.maxLength=null!=(i=a.maximum)?i:null,e.regex=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aR(...n))},e.includes=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aJ(...n))},e.startsWith=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aB(...n))},e.endsWith=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aW(...n))},e.min=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aC(...n))},e.max=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aT(...n))},e.length=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aL(...n))},e.nonempty=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aC(1,...n))},e.lowercase=t=>e.check(aM(t)),e.uppercase=t=>e.check(aF(t)),e.trim=()=>e.check(aq()),e.normalize=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aX(...n))},e.toLowerCase=()=>e.check(aY()),e.toUpperCase=()=>e.check(aH())}),oF=nt("ZodString",(e,t)=>{rn.init(e,t),oM.init(e,t),e.email=t=>e.check(iL(oW,t)),e.url=t=>e.check(iW(o0,t)),e.jwt=t=>e.check(i3(ck,t)),e.emoji=t=>e.check(iV(o1,t)),e.guid=t=>e.check(iR(oG,t)),e.uuid=t=>e.check(iM(oX,t)),e.uuidv4=t=>e.check(iF(oX,t)),e.uuidv6=t=>e.check(iJ(oX,t)),e.uuidv7=t=>e.check(iB(oX,t)),e.nanoid=t=>e.check(iG(o9,t)),e.guid=t=>e.check(iR(oG,t)),e.cuid=t=>e.check(iK(o5,t)),e.cuid2=t=>e.check(iX(o7,t)),e.ulid=t=>e.check(iq(ct,t)),e.base64=t=>e.check(i1(cp,t)),e.base64url=t=>e.check(i2(ch,t)),e.xid=t=>e.check(iY(cr,t)),e.ksuid=t=>e.check(iH(ca,t)),e.ipv4=t=>e.check(iQ(cc,t)),e.ipv6=t=>e.check(i0(cl,t)),e.cidrv4=t=>e.check(i4(cd,t)),e.cidrv6=t=>e.check(i6(cf,t)),e.e164=t=>e.check(i9(c_,t)),e.datetime=t=>e.check(lr(t)),e.date=t=>e.check(la(t)),e.time=t=>e.check(lc(t)),e.duration=t=>e.check(ll(t))});function oJ(e){return iT(oF,e)}let oB=nt("ZodStringFormat",(e,t)=>{rr.init(e,t),oM.init(e,t)}),oW=nt("ZodEmail",(e,t)=>{ro.init(e,t),oB.init(e,t)});function oV(e){return iL(oW,e)}let oG=nt("ZodGUID",(e,t)=>{ri.init(e,t),oB.init(e,t)});function oK(e){return iR(oG,e)}let oX=nt("ZodUUID",(e,t)=>{ra.init(e,t),oB.init(e,t)});function oq(e){return iM(oX,e)}function oY(e){return iF(oX,e)}function oH(e){return iJ(oX,e)}function oQ(e){return iB(oX,e)}let o0=nt("ZodURL",(e,t)=>{rc.init(e,t),oB.init(e,t)});function o4(e){return iW(o0,e)}function o6(e){return iW(o0,{protocol:/^https?$/,hostname:oI.domain,...ow.normalizeParams(e)})}let o1=nt("ZodEmoji",(e,t)=>{ru.init(e,t),oB.init(e,t)});function o2(e){return iV(o1,e)}let o9=nt("ZodNanoID",(e,t)=>{rl.init(e,t),oB.init(e,t)});function o3(e){return iG(o9,e)}let o5=nt("ZodCUID",(e,t)=>{rs.init(e,t),oB.init(e,t)});function o8(e){return iK(o5,e)}let o7=nt("ZodCUID2",(e,t)=>{rd.init(e,t),oB.init(e,t)});function ce(e){return iX(o7,e)}let ct=nt("ZodULID",(e,t)=>{rm.init(e,t),oB.init(e,t)});function cn(e){return iq(ct,e)}let cr=nt("ZodXID",(e,t)=>{rf.init(e,t),oB.init(e,t)});function ci(e){return iY(cr,e)}let ca=nt("ZodKSUID",(e,t)=>{rv.init(e,t),oB.init(e,t)});function co(e){return iH(ca,e)}let cc=nt("ZodIPv4",(e,t)=>{r_.init(e,t),oB.init(e,t)});function cu(e){return iQ(cc,e)}let cl=nt("ZodIPv6",(e,t)=>{rb.init(e,t),oB.init(e,t)});function cs(e){return i0(cl,e)}let cd=nt("ZodCIDRv4",(e,t)=>{rk.init(e,t),oB.init(e,t)});function cm(e){return i4(cd,e)}let cf=nt("ZodCIDRv6",(e,t)=>{rx.init(e,t),oB.init(e,t)});function cv(e){return i6(cf,e)}let cp=nt("ZodBase64",(e,t)=>{rw.init(e,t),oB.init(e,t)});function cg(e){return i1(cp,e)}let ch=nt("ZodBase64URL",(e,t)=>{rS.init(e,t),oB.init(e,t)});function cy(e){return i2(ch,e)}let c_=nt("ZodE164",(e,t)=>{rj.init(e,t),oB.init(e,t)});function cb(e){return i9(c_,e)}let ck=nt("ZodJWT",(e,t)=>{rU.init(e,t),oB.init(e,t)});function cx(e){return i3(ck,e)}let cI=nt("ZodCustomStringFormat",(e,t)=>{rO.init(e,t),oB.init(e,t)});function cw(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return oy(cI,e,t,n)}function cz(e){return oy(cI,"hostname",oI.hostname,e)}function cS(e){return oy(cI,"hex",oI.hex,e)}function cj(e,t){var n;let r=null!=(n=null==t?void 0:t.enc)?n:"hex",i="".concat(e,"_").concat(r),a=oI[i];if(!a)throw Error("Unrecognized hash format: ".concat(i));return oy(cI,i,a,t)}let cZ=nt("ZodNumber",(e,t)=>{var n,r,i,a,o,c,u,l,s;rN.init(e,t),oR.init(e,t),e.gt=(t,n)=>e.check(aj(t,n)),e.gte=(t,n)=>e.check(aZ(t,n)),e.min=(t,n)=>e.check(aZ(t,n)),e.lt=(t,n)=>e.check(az(t,n)),e.lte=(t,n)=>e.check(aS(t,n)),e.max=(t,n)=>e.check(aS(t,n)),e.int=t=>e.check(cN(t)),e.safe=t=>e.check(cN(t)),e.positive=t=>e.check(aj(0,t)),e.nonnegative=t=>e.check(aZ(0,t)),e.negative=t=>e.check(az(0,t)),e.nonpositive=t=>e.check(aS(0,t)),e.multipleOf=(t,n)=>e.check(a$(t,n)),e.step=(t,n)=>e.check(a$(t,n)),e.finite=()=>e;let d=e._zod.bag;e.minValue=null!=(i=Math.max(null!=(n=d.minimum)?n:-1/0,null!=(r=d.exclusiveMinimum)?r:-1/0))?i:null,e.maxValue=null!=(c=Math.min(null!=(a=d.maximum)?a:1/0,null!=(o=d.exclusiveMaximum)?o:1/0))?c:null,e.isInt=(null!=(u=d.format)?u:"").includes("int")||Number.isSafeInteger(null!=(l=d.multipleOf)?l:.5),e.isFinite=!0,e.format=null!=(s=d.format)?s:null});function cU(e){return an(cZ,e)}let cO=nt("ZodNumberFormat",(e,t)=>{rD.init(e,t),cZ.init(e,t)});function cN(e){return ai(cO,e)}function cD(e){return aa(cO,e)}function c$(e){return ao(cO,e)}function cP(e){return ac(cO,e)}function cE(e){return au(cO,e)}let cA=nt("ZodBoolean",(e,t)=>{r$.init(e,t),oR.init(e,t)});function cT(e){return al(cA,e)}let cC=nt("ZodBigInt",(e,t)=>{var n,r,i;rP.init(e,t),oR.init(e,t),e.gte=(t,n)=>e.check(aZ(t,n)),e.min=(t,n)=>e.check(aZ(t,n)),e.gt=(t,n)=>e.check(aj(t,n)),e.gte=(t,n)=>e.check(aZ(t,n)),e.min=(t,n)=>e.check(aZ(t,n)),e.lt=(t,n)=>e.check(az(t,n)),e.lte=(t,n)=>e.check(aS(t,n)),e.max=(t,n)=>e.check(aS(t,n)),e.positive=t=>e.check(aj(BigInt(0),t)),e.negative=t=>e.check(az(BigInt(0),t)),e.nonpositive=t=>e.check(aS(BigInt(0),t)),e.nonnegative=t=>e.check(aZ(BigInt(0),t)),e.multipleOf=(t,n)=>e.check(a$(t,n));let a=e._zod.bag;e.minValue=null!=(n=a.minimum)?n:null,e.maxValue=null!=(r=a.maximum)?r:null,e.format=null!=(i=a.format)?i:null});function cL(e){return ad(cC,e)}let cR=nt("ZodBigIntFormat",(e,t)=>{rE.init(e,t),cC.init(e,t)});function cM(e){return af(cR,e)}function cF(e){return av(cR,e)}let cJ=nt("ZodSymbol",(e,t)=>{rA.init(e,t),oR.init(e,t)});function cB(e){return ap(cJ,e)}let cW=nt("ZodUndefined",(e,t)=>{rT.init(e,t),oR.init(e,t)});function cV(e){return ag(cW,e)}let cG=nt("ZodNull",(e,t)=>{rC.init(e,t),oR.init(e,t)});function cK(e){return ah(cG,e)}let cX=nt("ZodAny",(e,t)=>{rL.init(e,t),oR.init(e,t)});function cq(){return ay(cX)}let cY=nt("ZodUnknown",(e,t)=>{rR.init(e,t),oR.init(e,t)});function cH(){return a_(cY)}let cQ=nt("ZodNever",(e,t)=>{rM.init(e,t),oR.init(e,t)});function c0(e){return ab(cQ,e)}let c4=nt("ZodVoid",(e,t)=>{rF.init(e,t),oR.init(e,t)});function c6(e){return ak(c4,e)}let c1=nt("ZodDate",(e,t)=>{rJ.init(e,t),oR.init(e,t),e.min=(t,n)=>e.check(aZ(t,n)),e.max=(t,n)=>e.check(aS(t,n));let n=e._zod.bag;e.minDate=n.minimum?new Date(n.minimum):null,e.maxDate=n.maximum?new Date(n.maximum):null});function c2(e){return ax(c1,e)}let c9=nt("ZodArray",(e,t)=>{rW.init(e,t),oR.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(aC(t,n)),e.nonempty=t=>e.check(aC(1,t)),e.max=(t,n)=>e.check(aT(t,n)),e.length=(t,n)=>e.check(aL(t,n)),e.unwrap=()=>e.element});function c3(e,t){return aQ(c9,e,t)}function c5(e){return uy(Object.keys(e._zod.def.shape))}let c8=nt("ZodObject",(e,t)=>{rq.init(e,t),oR.init(e,t),ow.defineLazy(e,"shape",()=>t.shape),e.keyof=()=>uy(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:cH()}),e.loose=()=>e.clone({...e._zod.def,catchall:cH()}),e.strict=()=>e.clone({...e._zod.def,catchall:c0()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>ow.extend(e,t),e.safeExtend=t=>ow.safeExtend(e,t),e.merge=t=>ow.merge(e,t),e.pick=t=>ow.pick(e,t),e.omit=t=>ow.omit(e,t),e.partial=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return ow.partial(uS,e,n[0])},e.required=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return ow.required(uE,e,n[0])}});function c7(e,t){return new c8({type:"object",get shape(){return ow.assignProp(this,"shape",e?ow.objectClone(e):{}),this.shape},...ow.normalizeParams(t)})}function ue(e,t){return new c8({type:"object",get shape(){return ow.assignProp(this,"shape",ow.objectClone(e)),this.shape},catchall:c0(),...ow.normalizeParams(t)})}function ut(e,t){return new c8({type:"object",get shape(){return ow.assignProp(this,"shape",ow.objectClone(e)),this.shape},catchall:cH(),...ow.normalizeParams(t)})}let un=nt("ZodUnion",(e,t)=>{rH.init(e,t),oR.init(e,t),e.options=t.options});function ur(e,t){return new un({type:"union",options:e,...ow.normalizeParams(t)})}let ui=nt("ZodDiscriminatedUnion",(e,t)=>{un.init(e,t),rQ.init(e,t)});function ua(e,t,n){return new ui({type:"union",options:t,discriminator:e,...ow.normalizeParams(n)})}let uo=nt("ZodIntersection",(e,t)=>{r0.init(e,t),oR.init(e,t)});function uc(e,t){return new uo({type:"intersection",left:e,right:t})}let uu=nt("ZodTuple",(e,t)=>{r6.init(e,t),oR.init(e,t),e.rest=t=>e.clone({...e._zod.def,rest:t})});function ul(e,t,n){let r=t instanceof rt,i=r?n:t;return new uu({type:"tuple",items:e,rest:r?t:null,...ow.normalizeParams(i)})}let us=nt("ZodRecord",(e,t)=>{r2.init(e,t),oR.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function ud(e,t,n){return new us({type:"record",keyType:e,valueType:t,...ow.normalizeParams(n)})}function um(e,t,n){let r=B(e);return r._zod.values=void 0,new us({type:"record",keyType:r,valueType:t,...ow.normalizeParams(n)})}let uf=nt("ZodMap",(e,t)=>{r9.init(e,t),oR.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function uv(e,t,n){return new uf({type:"map",keyType:e,valueType:t,...ow.normalizeParams(n)})}let up=nt("ZodSet",(e,t)=>{r5.init(e,t),oR.init(e,t),e.min=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aE(...n))},e.nonempty=t=>e.check(aE(1,t)),e.max=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aP(...n))},e.size=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(aA(...n))}});function ug(e,t){return new up({type:"set",valueType:e,...ow.normalizeParams(t)})}let uh=nt("ZodEnum",(e,t)=>{r7.init(e,t),oR.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,r)=>{let i={};for(let r of e)if(n.has(r))i[r]=t.entries[r];else throw Error("Key ".concat(r," not found in enum"));return new uh({...t,checks:[],...ow.normalizeParams(r),entries:i})},e.exclude=(e,r)=>{let i={...t.entries};for(let t of e)if(n.has(t))delete i[t];else throw Error("Key ".concat(t," not found in enum"));return new uh({...t,checks:[],...ow.normalizeParams(r),entries:i})}});function uy(e,t){return new uh({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...ow.normalizeParams(t)})}function u_(e,t){return new uh({type:"enum",entries:e,...ow.normalizeParams(t)})}let ub=nt("ZodLiteral",(e,t)=>{ie.init(e,t),oR.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function uk(e,t){return new ub({type:"literal",values:Array.isArray(e)?e:[e],...ow.normalizeParams(t)})}let ux=nt("ZodFile",(e,t)=>{it.init(e,t),oR.init(e,t),e.min=(t,n)=>e.check(aE(t,n)),e.max=(t,n)=>e.check(aP(t,n)),e.mime=(t,n)=>e.check(aG(Array.isArray(t)?t:[t],n))});function uI(e){return oe(ux,e)}let uw=nt("ZodTransform",(e,t)=>{ir.init(e,t),oR.init(e,t),e._zod.parse=(n,r)=>{if("backward"===r.direction)throw new ni(e.constructor.name);n.addIssue=r=>{if("string"==typeof r)n.issues.push(ow.issue(r,n.value,t));else r.fatal&&(r.continue=!1),null!=r.code||(r.code="custom"),null!=r.input||(r.input=n.value),null!=r.inst||(r.inst=e),n.issues.push(ow.issue(r))};let i=t.transform(n.value,n);return i instanceof Promise?i.then(e=>(n.value=e,n)):(n.value=i,n)}});function uz(e){return new uw({type:"transform",transform:e})}let uS=nt("ZodOptional",(e,t)=>{ia.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uj(e){return new uS({type:"optional",innerType:e})}let uZ=nt("ZodNullable",(e,t)=>{io.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uU(e){return new uZ({type:"nullable",innerType:e})}function uO(e){return uj(uU(e))}let uN=nt("ZodDefault",(e,t)=>{ic.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function uD(e,t){return new uN({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():ow.shallowClone(t)}})}let u$=nt("ZodPrefault",(e,t)=>{il.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uP(e,t){return new u$({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():ow.shallowClone(t)}})}let uE=nt("ZodNonOptional",(e,t)=>{is.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uA(e,t){return new uE({type:"nonoptional",innerType:e,...ow.normalizeParams(t)})}let uT=nt("ZodSuccess",(e,t)=>{im.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uC(e){return new uT({type:"success",innerType:e})}let uL=nt("ZodCatch",(e,t)=>{iv.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function uR(e,t){return new uL({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})}let uM=nt("ZodNaN",(e,t)=>{ip.init(e,t),oR.init(e,t)});function uF(e){return aw(uM,e)}let uJ=nt("ZodPipe",(e,t)=>{ig.init(e,t),oR.init(e,t),e.in=t.in,e.out=t.out});function uB(e,t){return new uJ({type:"pipe",in:e,out:t})}let uW=nt("ZodCodec",(e,t)=>{uJ.init(e,t),iy.init(e,t)});function uV(e,t,n){return new uW({type:"pipe",in:e,out:t,transform:n.decode,reverseTransform:n.encode})}let uG=nt("ZodReadonly",(e,t)=>{ik.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function uK(e){return new uG({type:"readonly",innerType:e})}let uX=nt("ZodTemplateLiteral",(e,t)=>{iI.init(e,t),oR.init(e,t)});function uq(e,t){return new uX({type:"template_literal",parts:e,...ow.normalizeParams(t)})}let uY=nt("ZodLazy",(e,t)=>{iS.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.getter()});function uH(e){return new uY({type:"lazy",getter:e})}let uQ=nt("ZodPromise",(e,t)=>{iz.init(e,t),oR.init(e,t),e.unwrap=()=>e._zod.def.innerType});function u0(e){return new uQ({type:"promise",innerType:e})}let u4=nt("ZodFunction",(e,t)=>{iw.init(e,t),oR.init(e,t)});function u6(e){var t,n;return new u4({type:"function",input:Array.isArray(null==e?void 0:e.input)?ul(null==e?void 0:e.input):null!=(t=null==e?void 0:e.input)?t:c3(cH()),output:null!=(n=null==e?void 0:e.output)?n:cH()})}let u1=nt("ZodCustom",(e,t)=>{ij.init(e,t),oR.init(e,t)});function u2(e){let t=new nL({check:"custom"});return t._zod.check=e,t}function u9(e,t){return of(u1,null!=e?e:()=>!0,t)}function u3(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return ov(u1,e,t)}function u5(e){return op(e)}function u8(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{error:"Input not instance of ".concat(e.name)},n=new u1({type:"custom",check:"custom",fn:t=>t instanceof e,abort:!0,...ow.normalizeParams(t)});return n._zod.bag.Class=e,n}let u7=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return oh({Codec:uW,Boolean:cA,String:oF},...t)};function le(e){let t=uH(()=>ur([oJ(e),cU(),cT(),cK(),c3(t),ud(oJ(),t)]));return t}function lt(e,t){return uB(uz(e),t)}let ln=nt("ZodISODateTime",(e,t)=>{rp.init(e,t),oB.init(e,t)});function lr(e){return i8(ln,e)}let li=nt("ZodISODate",(e,t)=>{rg.init(e,t),oB.init(e,t)});function la(e){return i7(li,e)}let lo=nt("ZodISOTime",(e,t)=>{rh.init(e,t),oB.init(e,t)});function lc(e){return ae(lo,e)}let lu=nt("ZodISODuration",(e,t)=>{ry.init(e,t),oB.init(e,t)});function ll(e){return at(lu,e)}var ls=e.i(77504);function ld(e){return iC(oF,e)}function lm(e){return ar(cZ,e)}function lf(e){return as(cA,e)}function lv(e){return am(cC,e)}function lp(e){return aI(c1,e)}e.s(["bigint",()=>lv,"boolean",()=>lf,"date",()=>lp,"number",()=>lm,"string",()=>ld],55094);var lg=e.i(55094);no(tS()),e.i(23513);var lh=ox;e.i(77738),e.s(["endsWith",()=>aW,"gt",()=>aj,"gte",()=>aZ,"includes",()=>aJ,"length",()=>aL,"lowercase",()=>aM,"lt",()=>az,"lte",()=>aS,"maxLength",()=>aT,"maxSize",()=>aP,"mime",()=>aG,"minLength",()=>aC,"minSize",()=>aE,"multipleOf",()=>a$,"negative",()=>aO,"nonnegative",()=>aD,"nonpositive",()=>aN,"normalize",()=>aX,"overwrite",()=>aK,"positive",()=>aU,"property",()=>aV,"regex",()=>aR,"size",()=>aA,"startsWith",()=>aB,"toLowerCase",()=>aY,"toUpperCase",()=>aH,"trim",()=>aq,"uppercase",()=>aF],97890),e.s([],57893),e.i(57893),e.i(97890),e.i(90592),e.i(57261),e.s(["$brand",()=>nn,"ZodFirstPartyTypeKind",()=>t,"ZodIssueCode",()=>ly,"config",()=>no,"getErrorMap",()=>lb,"setErrorMap",()=>l_],34397),e.s(["ZodFirstPartyTypeKind",()=>t,"ZodIssueCode",()=>ly,"getErrorMap",()=>lb,"setErrorMap",()=>l_],3196);let ly={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function l_(e){no({customError:e})}function lb(){return no().customError}t||(t={}),e.i(3196),e.i(34397);var oI=th,ow=e_,lk=t8,lx=ls,lI=lg,lw=e.i(49984),lw=lw;lw.object({studentId:lw.string().min(1,"Student ID is required"),examId:lw.string().min(1,"Exam ID is required"),obtainedMarks:lw.number().min(0,"Marks cannot be negative").max(1e3,"Marks cannot exceed 1000"),remarks:lw.string().optional()}),lw.object({examId:lw.string().min(1,"Exam ID is required"),marks:lw.array(lw.object({studentId:lw.string().min(1,"Student ID is required"),obtainedMarks:lw.number().min(0,"Marks cannot be negative").max(1e3,"Marks cannot exceed 1000"),remarks:lw.string().optional()})).min(1,"At least one mark entry is required")});var lz=e.i(87523),lS=e.i(33),lj=e.i(4534),lZ=e.i(33163);let lU=(0,e.i(4741).default)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function lO(e){let{exam:t,students:i,onSave:a,saving:u=!1}=e,[l,m]=(0,r.useState)(()=>{let e={};return i.forEach(t=>{var n,r,i;e[t.id]={obtainedMarks:(null==(r=t.currentMark)||null==(n=r.obtainedMarks)?void 0:n.toString())||"",remarks:(null==(i=t.currentMark)?void 0:i.remarks)||""}}),e}),[f,v]=(0,r.useState)({}),[p,g]=(0,r.useState)(""),h=(e,t,n)=>{m(r=>({...r,[e]:{...r[e],[t]:n}})),f[e]&&v(t=>{let n={...t};return delete n[e],n}),p&&g("")},y=async()=>{if(g(""),!(()=>{let e={},n=!1;return Object.entries(l).forEach(r=>{let[i,a]=r;if(""!==a.obtainedMarks){let r=parseFloat(a.obtainedMarks);if(isNaN(r))e[i]=[{field:"obtainedMarks",message:"Invalid marks format"}],n=!0;else{let o=((e,t,n,r,i)=>{let a=[];return e&&""!==e.trim()||a.push({field:"studentId",message:"Student ID is required"}),t&&""!==t.trim()||a.push({field:"examId",message:"Exam ID is required"}),n<0&&a.push({field:"obtainedMarks",message:"Marks cannot be negative",studentId:e}),n>r&&a.push({field:"obtainedMarks",message:"Marks cannot exceed maximum marks (".concat(r,")"),studentId:e}),(n.toString().split(".")[1]||"").length>2&&a.push({field:"obtainedMarks",message:"Marks can have at most 2 decimal places",studentId:e}),i&&i.length>500&&a.push({field:"remarks",message:"Remarks cannot exceed 500 characters",studentId:e}),{isValid:0===a.length,errors:a}})(i,t.id,r,t.maxMarks,a.remarks);o.isValid||(e[i]=o.errors,n=!0)}}}),v(e),!n})())return void g("Please fix the validation errors before saving.");let e=Object.entries(l).filter(e=>{let[t,n]=e;return""!==n.obtainedMarks}).map(e=>{let[t,n]=e;return{studentId:t,obtainedMarks:parseFloat(n.obtainedMarks),remarks:n.remarks||void 0}});if(0===e.length)return void g("No marks to save. Please enter marks for at least one student.");try{await a(e)}catch(e){g("Failed to save marks. Please try again."),console.error("Error saving marks:",e)}};return(0,n.jsxs)(o.Card,{children:[(0,n.jsxs)(o.CardHeader,{children:[(0,n.jsxs)(o.CardTitle,{className:"flex items-center space-x-2",children:[(0,n.jsx)(lz.Users,{className:"w-5 h-5"}),(0,n.jsxs)("span",{children:["Students (",i.length,")"]})]}),(0,n.jsxs)(o.CardDescription,{children:["Enter marks for each student. Maximum marks: ",t.maxMarks]}),p&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,n.jsx)(lZ.AlertCircle,{className:"w-4 h-4 text-red-600"}),(0,n.jsx)("span",{className:"text-sm text-red-700",children:p})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:[(0,n.jsx)(lU,{className:"w-4 h-4 text-blue-600"}),(0,n.jsx)("span",{className:"text-sm text-blue-700",children:"Marks can be entered with up to 2 decimal places (e.g., 85.5). Leave blank to skip a student."})]}),(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsxs)(c.Button,{onClick:y,disabled:u,children:[(0,n.jsx)(lS.Save,{className:"w-4 h-4 mr-2"}),u?"Saving...":"Save All Marks"]})})]}),(0,n.jsx)(o.CardContent,{children:0===i.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(lz.Users,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"No students found for this exam"})]}):(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,n.jsx)("thead",{className:"bg-gray-50",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roll No."}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Section"}),(0,n.jsxs)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:["Marks (/",t.maxMarks,")"]}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Remarks"}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,n.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>{var r,i;let a=f[e.id]||[],o=a.length>0;return(0,n.jsxs)("tr",{className:o?"bg-red-50":"",children:[(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,n.jsx)("div",{className:"text-sm text-gray-500",children:e.admissionNo})]})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.rollNumber||"-"}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.sectionName||"-"}),(0,n.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,n.jsx)(s.Input,{type:"number",min:"0",max:t.maxMarks,step:"0.01",value:(null==(r=l[e.id])?void 0:r.obtainedMarks)||"",onChange:t=>h(e.id,"obtainedMarks",t.target.value),className:"w-20 ".concat(o?"border-red-500":""),placeholder:"0"}),o&&(0,n.jsx)("div",{className:"text-xs text-red-500 mt-1",children:a.map((e,t)=>(0,n.jsx)("div",{children:e.message},t))})]}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)(s.Input,{type:"text",value:(null==(i=l[e.id])?void 0:i.remarks)||"",onChange:t=>h(e.id,"remarks",t.target.value),className:"w-32",placeholder:"Optional",maxLength:500})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.hasMarks?(0,n.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,n.jsx)(lj.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:"Pending"})})]},e.id)})})]})}),(0,n.jsx)("div",{className:"lg:hidden space-y-4",children:i.map(e=>{var r,i;let a=f[e.id]||[],c=a.length>0;return(0,n.jsx)(o.Card,{className:"p-4 ".concat(c?"border-red-500 bg-red-50":""),children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:[e.admissionNo," • Roll: ",e.rollNumber||"-"," • Section: ",e.sectionName||"-"]})]}),(0,n.jsx)("div",{className:"ml-4",children:e.hasMarks?(0,n.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,n.jsx)(lj.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:"Pending"})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)(d.Label,{htmlFor:"marks-".concat(e.id),className:"text-sm font-medium text-gray-700",children:["Marks (/",t.maxMarks,")"]}),(0,n.jsx)(s.Input,{id:"marks-".concat(e.id),type:"number",min:"0",max:t.maxMarks,step:"0.01",value:(null==(r=l[e.id])?void 0:r.obtainedMarks)||"",onChange:t=>h(e.id,"obtainedMarks",t.target.value),className:"mt-1 ".concat(c?"border-red-500":""),placeholder:"0"}),c&&(0,n.jsx)("div",{className:"text-xs text-red-500 mt-1",children:a.map((e,t)=>(0,n.jsx)("div",{children:e.message},t))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(d.Label,{htmlFor:"remarks-".concat(e.id),className:"text-sm font-medium text-gray-700",children:"Remarks"}),(0,n.jsx)(s.Input,{id:"remarks-".concat(e.id),type:"text",value:(null==(i=l[e.id])?void 0:i.remarks)||"",onChange:t=>h(e.id,"remarks",t.target.value),className:"mt-1",placeholder:"Optional",maxLength:500})]})]})]})},e.id)})})]})})]})}var lN=e.i(69556),lD=e.i(86080),l$=e.i(90842);function lP(){let e=(0,i.useParams)();(0,i.useRouter)();let{data:t}=(0,a.useSession)(),s=e.examId,[d,m]=(0,r.useState)(null),[f,v]=(0,r.useState)(!0),[p,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{h()},[s]);let h=async()=>{try{v(!0);let e=await fetch("/api/teacher/exams/".concat(s,"/students"));if(e.ok){let t=await e.json();m(t)}else console.error("Failed to fetch exam data")}catch(e){console.error("Error fetching exam data:",e)}finally{v(!1)}},y=async e=>{try{g(!0);let t=await fetch("/api/teacher/marks",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({examId:s,marks:e})});if(t.ok){let e=await t.json(),n=e.results.filter(e=>e.success).length,r=e.results.filter(e=>!e.success).length;0===r?(alert("Successfully saved marks for ".concat(n," students")),h()):(alert("Saved marks for ".concat(n," students. ").concat(r," failed.")),console.error("Some marks failed to save:",e.results.filter(e=>!e.success)))}else{let e=await t.json();alert("Failed to save marks: ".concat(e.error))}}catch(e){console.error("Error saving marks:",e),alert("Failed to save marks. Please try again.")}finally{g(!1)}};return f?(0,n.jsx)(u.default,{title:"Enter Marks",navigation:l.teacherNavigation,children:(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,n.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading exam data..."})]})}):d?(0,n.jsx)(u.default,{title:"Enter Marks",navigation:l.teacherNavigation,children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,n.jsx)(l$.default,{href:"/teacher/marks",children:(0,n.jsxs)(c.Button,{variant:"outline",size:"sm",children:[(0,n.jsx)(lD.ArrowLeft,{className:"w-4 h-4 mr-1"}),"Back"]})})}),(0,n.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Enter Marks"}),(0,n.jsxs)("p",{className:"text-sm sm:text-base text-gray-600",children:[d.exam.name," - ",d.exam.subject.name," (",d.exam.subject.class.name,")"]})]})}),(0,n.jsxs)(o.Card,{children:[(0,n.jsx)(o.CardHeader,{children:(0,n.jsxs)(o.CardTitle,{className:"flex items-center space-x-2",children:[(0,n.jsx)(lN.BookOpen,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Exam Details"})]})}),(0,n.jsx)(o.CardContent,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-700",children:"Subject:"}),(0,n.jsx)("p",{className:"text-gray-600",children:d.exam.subject.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-700",children:"Class:"}),(0,n.jsx)("p",{className:"text-gray-600",children:d.exam.subject.class.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-700",children:"Max Marks:"}),(0,n.jsx)("p",{className:"text-gray-600",children:d.exam.maxMarks})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium text-gray-700",children:"Date:"}),(0,n.jsx)("p",{className:"text-gray-600",children:new Date(d.exam.date).toLocaleDateString()})]})]})})]}),(0,n.jsx)(lO,{exam:d.exam,students:d.students,onSave:y,saving:p})]})}):(0,n.jsx)(u.default,{title:"Enter Marks",navigation:l.teacherNavigation,children:(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(lZ.AlertCircle,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Exam not found"}),(0,n.jsx)(l$.default,{href:"/teacher/marks",children:(0,n.jsxs)(c.Button,{className:"mt-4",children:[(0,n.jsx)(lD.ArrowLeft,{className:"w-4 h-4 mr-2"}),"Back to Marks"]})})]})})}}]);