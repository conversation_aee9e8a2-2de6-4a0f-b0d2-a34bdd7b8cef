(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,53309,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>p],30151);var t=e.i(53379),r=e.i(46686);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,o=t.map(t=>{let o=n(t,e);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let e=0;e<o.length;e++){let r=o[e];"function"==typeof r?r():n(t[e],null)}}}}function u(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.useCallback(o(...t),t)}function i(e){let n=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...u}=e;if(r.isValidElement(n)){var i,a,l;let e,s,c=(s=(e=null==(a=Object.getOwnPropertyDescriptor((i=n).props,"ref"))?void 0:a.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(s=(e=null==(l=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,f=function(e,t){let r={...t};for(let n in t){let o=e[n],u=t[n];/^on[A-Z]/.test(n)?o&&u?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=u(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...u}:"className"===n&&(r[n]=[o,u].filter(Boolean).join(" "))}return{...e,...r}}(u,n.props);return n.type!==r.Fragment&&(f.ref=t?o(t,c):c),r.cloneElement(n,f)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),u=r.forwardRef((e,o)=>{let{children:u,...i}=e,a=r.Children.toArray(u),l=a.find(s);if(l){let e=l.props.children,u=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(n,{...i,ref:o,children:r.isValidElement(e)?r.cloneElement(e,void 0,u):null})}return(0,t.jsx)(n,{...i,ref:o,children:u})});return u.displayName="".concat(e,".Slot"),u}e.s(["Slot",()=>a,"createSlot",()=>i],88338),e.s(["composeRefs",()=>o,"useComposedRefs",()=>u],35952);var a=i("Slot"),l=Symbol("radix.slottable");function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var c=e.i(94323),f=e.i(36946);let d=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=r.forwardRef((e,r)=>{let{className:n,variant:o,size:u,asChild:i=!1,...l}=e;return(0,t.jsx)(i?a:"button",{className:(0,f.cn)(d({variant:o,size:u,className:n})),ref:r,...l})});p.displayName="Button"},18125,(e,t,r)=>{t.exports=e.r(9885)},62521,e=>{"use strict";e.s(["Primitive",()=>u,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),n=e.i(88338),o=e.i(53379),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let u=(0,n.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?u:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,t.forwardRef)((e,r)=>{let{color:u="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:s="",children:c,iconNode:f,...d}=e;return(0,t.createElement)("svg",{ref:r,...o,width:i,height:i,stroke:u,strokeWidth:l?24*Number(a)/Number(i):a,className:n("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(d)&&{"aria-hidden":"true"},...d},[...f.map(e=>{let[r,n]=e;return(0,t.createElement)(r,n)}),...Array.isArray(c)?c:[c]])}),i=(e,o)=>{let i=(0,t.forwardRef)((i,a)=>{let{className:l,...s}=i;return(0,t.createElement)(u,{ref:a,iconNode:o,className:n("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...s})});return i.displayName=r(e),i}},46945,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function u(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return i},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return u}})},56229,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return u},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let n=e.r(95039)._(e.r(46945)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:r}=e,u=e.protocol||"",i=e.pathname||"",a=e.hash||"",l=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==s?(s="//"+(s||""),i&&"/"!==i[0]&&(i="/"+i)):s||(s=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+u+s+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return u(e)}},35251,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(46686);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=u(e,n)),t&&(o.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},79713,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return y},PageNotFoundError:function(){return m},SP:function(){return p},ST:function(){return g},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return s},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return P}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let u=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>u.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,g=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function P(e){return JSON.stringify({message:e.message,stack:e.stack})}},46716,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return u}});let n=e.r(79713),o=e.r(36984);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},9260,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},90842,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return y},useLinkStatus:function(){return b}});let n=e.r(95039),o=e.r(53379),u=n._(e.r(46686)),i=e.r(56229),a=e.r(33762),l=e.r(35251),s=e.r(79713),c=e.r(98497);e.r(53309);let f=e.r(1883),d=e.r(46716),p=e.r(74179);e.r(9260);let g=e.r(37802);function h(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function y(e){var t;let r,n,i,[y,b]=(0,u.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,u.useRef)(null),{href:P,as:x,children:w,prefetch:j=null,passHref:E,replace:O,shallow:_,scroll:k,onClick:C,onMouseEnter:S,onTouchStart:A,legacyBehavior:R=!1,onNavigate:N,ref:T,unstable_dynamicOnHover:M,...L}=e;r=w,R&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let U=u.default.useContext(a.AppRouterContext),I=!1!==j,F=!1!==j?null===(t=j)||"auto"===t?g.FetchStrategy.PPR:g.FetchStrategy.Full:g.FetchStrategy.PPR,{href:B,as:D}=u.default.useMemo(()=>{let e=h(P);return{href:e,as:x?h(x):e}},[P,x]);R&&(n=u.default.Children.only(r));let W=R?n&&"object"==typeof n&&n.ref:T,K=u.default.useCallback(e=>(null!==U&&(v.current=(0,f.mountLinkInstance)(e,B,U,F,I,b)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[I,B,U,F,b]),z={ref:(0,l.useMergedRef)(K,W),onClick(e){R||"function"!=typeof C||C(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,n,o,i,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}u.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==i||i,n.current)})}}(e,B,D,v,O,k,N))},onMouseEnter(e){R||"function"!=typeof S||S(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),U&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){R||"function"!=typeof A||A(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),U&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,s.isAbsoluteUrl)(D)?z.href=D:R&&!E&&("a"!==n.type||"href"in n.props)||(z.href=(0,c.addBasePath)(D)),i=R?u.default.cloneElement(n,z):(0,o.jsx)("a",{...L,...z,children:r}),(0,o.jsx)(m.Provider,{value:y,children:i})}let m=(0,u.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,u.useContext)(m);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)}]);