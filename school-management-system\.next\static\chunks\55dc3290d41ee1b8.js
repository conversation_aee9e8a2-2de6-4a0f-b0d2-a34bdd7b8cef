(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,r,t)=>{r.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>o,"CardDescription",()=>l,"CardHeader",()=>i,"CardTitle",()=>n]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let s=t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...i})});s.displayName="Card";let i=t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...i})});i.displayName="CardHeader";let n=t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...i})});n.displayName="CardTitle";let l=t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...i})});l.displayName="CardDescription";let o=t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...i})});o.displayName="CardContent",t.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...i})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>f],30151);var r=e.i(53379),t=e.i(46686);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,s=r.map(r=>{let s=a(r,e);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let e=0;e<s.length;e++){let t=s[e];"function"==typeof t?t():a(r[e],null)}}}}function i(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t.useCallback(s(...r),r)}function n(e){let a=function(e){let r=t.forwardRef((e,r)=>{let{children:a,...i}=e;if(t.isValidElement(a)){var n,l,o;let e,d,c=(d=(e=null==(l=Object.getOwnPropertyDescriptor((n=a).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(d=(e=null==(o=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:o.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref,u=function(e,r){let t={...r};for(let a in r){let s=e[a],i=r[a];/^on[A-Z]/.test(a)?s&&i?t[a]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let a=i(...r);return s(...r),a}:s&&(t[a]=s):"style"===a?t[a]={...s,...i}:"className"===a&&(t[a]=[s,i].filter(Boolean).join(" "))}return{...e,...t}}(i,a.props);return a.type!==t.Fragment&&(u.ref=r?s(r,c):c),t.cloneElement(a,u)}return t.Children.count(a)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),i=t.forwardRef((e,s)=>{let{children:i,...n}=e,l=t.Children.toArray(i),o=l.find(d);if(o){let e=o.props.children,i=l.map(r=>r!==o?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(a,{...n,ref:s,children:t.isValidElement(e)?t.cloneElement(e,void 0,i):null})}return(0,r.jsx)(a,{...n,ref:s,children:i})});return i.displayName="".concat(e,".Slot"),i}e.s(["Slot",()=>l,"createSlot",()=>n],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>i],35952);var l=n("Slot"),o=Symbol("radix.slottable");function d(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var c=e.i(94323),u=e.i(36946);let m=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),f=t.forwardRef((e,t)=>{let{className:a,variant:s,size:i,asChild:n=!1,...o}=e;return(0,r.jsx)(n?l:"button",{className:(0,u.cn)(m({variant:s,size:i,className:a})),ref:t,...o})});f.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>i,"dispatchDiscreteCustomEvent",()=>n]);var r=e.i(46686),t=e.i(50321),a=e.i(88338),s=e.i(53379),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let i=(0,a.createSlot)("Primitive.".concat(t)),n=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?i:t,{...n,ref:r})});return n.displayName="Primitive.".concat(t),{...e,[t]:n}},{});function n(e,r){e&&t.flushSync(()=>e.dispatchEvent(r))}},4741,e=>{"use strict";e.s(["default",()=>n],4741);var r=e.i(46686);let t=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)((e,t)=>{let{color:i="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:i,strokeWidth:o?24*Number(l)/Number(n):l,className:a("lucide",d),...!c&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),n=(e,s)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:o,...d}=n;return(0,r.createElement)(i,{ref:l,iconNode:s,className:a("lucide-".concat(t(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...d})});return n.displayName=t(e),n}},80873,e=>{"use strict";e.s(["User",()=>r],80873);let r=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>r,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>a,"teacherNavigation",()=>t]);let r=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],t=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],a=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},92521,e=>{"use strict";e.s(["Label",()=>o],92521);var r=e.i(53379),t=e.i(46686),a=e.i(62521),s=t.forwardRef((e,t)=>(0,r.jsx)(a.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));s.displayName="Label";var i=e.i(94323),n=e.i(36946);let l=(0,i.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(s,{ref:t,className:(0,n.cn)(l(),a),...i})});o.displayName=s.displayName},96487,e=>{"use strict";e.s(["Input",()=>s]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let s=t.forwardRef((e,t)=>{let{className:s,type:i,...n}=e;return(0,r.jsx)("input",{type:i,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});s.displayName="Input"},70307,e=>{"use strict";e.s(["Alert",()=>n,"AlertDescription",()=>l]);var r=e.i(53379),t=e.i(46686),a=e.i(94323),s=e.i(36946);let i=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),n=t.forwardRef((e,t)=>{let{className:a,variant:n,...l}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(i({variant:n}),a),...l})});n.displayName="Alert",t.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",a),...i})}).displayName="AlertTitle";let l=t.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",a),...i})});l.displayName="AlertDescription"},44670,e=>{"use strict";e.s(["Loader2",()=>r],44670);let r=(0,e.i(4741).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},98325,e=>{"use strict";e.s(["Eye",()=>r],98325);let r=(0,e.i(4741).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},50060,e=>{"use strict";e.s(["default",()=>v],50060);var r=e.i(53379),t=e.i(46686),a=e.i(30151),s=e.i(96487),i=e.i(92521),n=e.i(32668),l=e.i(70307),o=e.i(44670),d=e.i(98325),c=e.i(4741);let u=(0,c.default)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var m=e.i(80873);let f=(0,c.default)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var h=e.i(69758),p=e.i(84633);async function g(e){try{var r;if(!e.email||!e.password)return{success:!1,error:"Email and password are required"};if(!e.email.includes("@"))return{success:!1,error:"Please enter a valid email address"};if(e.password.length<6)return{success:!1,error:"Password must be at least 6 characters long"};let t=await (0,h.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});if(null==t?void 0:t.error)return{success:!1,error:"Invalid email or password. Please try again."};let a=await (0,h.getSession)(),s=(null==a||null==(r=a.user)?void 0:r.role)?(0,p.getRoleDashboardUrl)(a.user.role):"/";return{success:!0,redirectUrl:s}}catch(e){return console.error("Login error:",e),{success:!1,error:"An unexpected error occurred. Please try again."}}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/login";try{await (0,h.signOut)({redirect:!0,callbackUrl:e})}catch(r){console.error("Logout error:",r),window.location.href=e}}let y={admin:{email:"<EMAIL>",password:"Admin@12345",role:"ADMIN"},teacher:{email:"<EMAIL>",password:"Teacher@12345",role:"TEACHER"},student:{email:"<EMAIL>",password:"Student@12345",role:"STUDENT"}};var b=e.i(18125);function v(){let[e,c]=(0,t.useState)(!1),{formData:h,validationErrors:p,isLoading:v,error:w,updateField:k,handleSubmit:N,fillDemoCredentials:j}=function(){let{login:e,...r}=function(){let e=(0,b.useRouter)(),[r,a]=(0,t.useState)({isLoading:!1,error:null,success:!1}),s=(0,t.useCallback)(async r=>{a(e=>({...e,isLoading:!0,error:null,success:!1}));try{let t=await g(r);return t.success?(a(e=>({...e,isLoading:!1,success:!0})),t.redirectUrl&&e.push(t.redirectUrl)):a(e=>({...e,isLoading:!1,error:t.error||"Login failed"})),t}catch(r){let e=r instanceof Error?r.message:"An unexpected error occurred";return a(r=>({...r,isLoading:!1,error:e})),{success:!1,error:e}}},[e]),i=(0,t.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/login";a(e=>({...e,isLoading:!0,error:null}));try{await x(e),a(e=>({...e,isLoading:!1,success:!0}))}catch(r){let e=r instanceof Error?r.message:"Logout failed";a(r=>({...r,isLoading:!1,error:e}))}},[]),n=(0,t.useCallback)(()=>{a(e=>({...e,error:null}))},[]),l=(0,t.useCallback)(()=>{a(e=>({...e,success:!1}))},[]),o=(0,t.useCallback)(()=>{a({isLoading:!1,error:null,success:!1})},[]);return{...r,login:s,logout:i,clearError:n,clearSuccess:l,reset:o}}(),[a,s]=(0,t.useState)({email:"",password:""}),[i,n]=(0,t.useState)({}),l=(0,t.useCallback)((e,t)=>{s(r=>({...r,[e]:t})),i[e]&&n(r=>({...r,[e]:void 0})),r.error&&r.clearError()},[i,r]),o=(0,t.useCallback)(()=>{let e={};return a.email.trim()?a.email.includes("@")||(e.email="Please enter a valid email address"):e.email="Email is required",a.password.trim()?a.password.length<6&&(e.password="Password must be at least 6 characters long"):e.password="Password is required",n(e),0===Object.keys(e).length},[a]),d=(0,t.useCallback)(async r=>(r&&r.preventDefault(),o())?await e(a):{success:!1,error:"Please fix the validation errors"},[a,o,e]),c=(0,t.useCallback)(()=>{s({email:"",password:""}),n({}),r.reset()},[r]),u=(0,t.useCallback)(e=>{s({admin:{email:"<EMAIL>",password:"Admin@12345"},teacher:{email:"<EMAIL>",password:"Teacher@12345"},student:{email:"<EMAIL>",password:"Student@12345"}}[e]),n({})},[]);return{...r,formData:a,validationErrors:i,updateField:l,validateForm:o,handleSubmit:d,resetForm:c,fillDemoCredentials:u}}();return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4",children:(0,r.jsxs)(n.Card,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(n.CardHeader,{className:"text-center px-4 sm:px-6",children:[(0,r.jsx)(n.CardTitle,{className:"text-xl sm:text-2xl font-bold leading-tight",children:"School Management System"}),(0,r.jsx)(n.CardDescription,{className:"text-sm sm:text-base mt-2",children:"Sign in to your account"})]}),(0,r.jsxs)(n.CardContent,{className:"px-4 sm:px-6",children:[(0,r.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[w&&(0,r.jsx)(l.Alert,{variant:"destructive",children:(0,r.jsx)(l.AlertDescription,{children:w})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.Label,{htmlFor:"email",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.User,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(s.Input,{id:"email",type:"email",value:h.email,onChange:e=>k("email",e.target.value),placeholder:"Enter your email address",className:"pl-10 min-h-[44px] text-base ".concat(p.email?"border-red-500":""),required:!0,disabled:v})]}),p.email&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:p.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.Label,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(s.Input,{id:"password",type:e?"text":"password",value:h.password,onChange:e=>k("password",e.target.value),placeholder:"Enter your password",className:"pl-10 pr-10 min-h-[44px] text-base ".concat(p.password?"border-red-500":""),required:!0,disabled:v}),(0,r.jsx)("button",{type:"button",onClick:()=>c(!e),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",disabled:v,children:e?(0,r.jsx)(u,{className:"h-4 w-4"}):(0,r.jsx)(d.Eye,{className:"h-4 w-4"})})]}),p.password&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:p.password})]}),(0,r.jsx)(a.Button,{type:"submit",className:"w-full min-h-[44px] text-base",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold mb-3 text-center text-gray-900 dark:text-gray-100 text-sm sm:text-base",children:"Demo Credentials"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:[(0,r.jsx)(a.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>j("admin"),disabled:v,className:"text-xs",children:"Fill Admin"}),(0,r.jsx)(a.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>j("teacher"),disabled:v,className:"text-xs",children:"Fill Teacher"}),(0,r.jsx)(a.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>j("student"),disabled:v,className:"text-xs",children:"Fill Student"})]}),(0,r.jsx)("div",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Admin:"})," ",y.admin.email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Teacher:"})," ",y.teacher.email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Student:"})," ",y.student.email]})]})})]})]})]})]})})}}]);