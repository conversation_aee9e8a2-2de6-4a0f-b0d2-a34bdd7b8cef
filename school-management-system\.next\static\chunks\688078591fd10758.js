(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57606,e=>{"use strict";e.s(["X",()=>t],57606);let t=(0,e.i(4741).default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},22848,e=>{"use strict";e.s(["ChevronRight",()=>t],22848);let t=(0,e.i(4741).default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},66792,e=>{"use strict";e.s(["Upload",()=>t],66792);let t=(0,e.i(4741).default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},23178,e=>{"use strict";e.s(["Download",()=>t],23178);let t=(0,e.i(4741).default)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},96274,e=>{"use strict";e.s(["FileText",()=>t],96274);let t=(0,e.i(4741).default)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},96832,35255,7418,e=>{"use strict";e.s(["Search",()=>n],96832);var t=e.i(4741);let n=(0,t.default)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);e.s(["Plus",()=>r],35255);let r=(0,t.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);e.s(["Edit",()=>o],7418);let o=(0,t.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},89559,46404,93020,87523,69556,35505,21138,66505,20067,10535,e=>{"use strict";e.s(["default",()=>om],89559);var t,n,r,o,i,a=e.i(53379),l=e.i(46686),s=e.i(69758),u=e.i(18125),c=e.i(30151),d=e.i(4741);let f=(0,d.default)("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]),p=(0,d.default)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),h=(0,d.default)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var m=e.i(55973);function v(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}"undefined"!=typeof window&&window.document&&window.document.createElement;var g=e.i(35952);function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=()=>{let t=n.map(e=>l.createContext(e));return function(n){let r=(null==n?void 0:n[e])||t;return l.useMemo(()=>({["__scope".concat(e)]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=l.createContext(r),i=n.length;n=[...n,r];let s=t=>{var n;let{scope:r,children:s,...u}=t,c=(null==r||null==(n=r[e])?void 0:n[i])||o,d=l.useMemo(()=>u,Object.values(u));return(0,a.jsx)(c.Provider,{value:d,children:s})};return s.displayName=t+"Provider",[s,function(n,a){var s;let u=(null==a||null==(s=a[e])?void 0:s[i])||o,c=l.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error("`".concat(n,"` must be used within `").concat(t,"`"))}]},function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=t[0];if(1===t.length)return r;let o=()=>{let e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){let n=e.reduce((e,n)=>{let{useScope:r,scopeName:o}=n,i=r(t)["__scope".concat(o)];return{...e,...i}},{});return l.useMemo(()=>({["__scope".concat(r.scopeName)]:n}),[n])}};return o.scopeName=r.scopeName,o}(r,...t)]}var x=(null==(n=globalThis)?void 0:n.document)?l.useLayoutEffect:()=>{};l[" useEffectEvent ".trim().toString()],l[" useInsertionEffect ".trim().toString()];var w=l[" useInsertionEffect ".trim().toString()]||x;function b(e){let{prop:t,defaultProp:n,onChange:r=()=>{},caller:o}=e,[i,a,s]=function(e){let{defaultProp:t,onChange:n}=e,[r,o]=l.useState(t),i=l.useRef(r),a=l.useRef(n);return w(()=>{a.current=n},[n]),l.useEffect(()=>{if(i.current!==r){var e;null==(e=a.current)||e.call(a,r),i.current=r}},[r,i]),[r,o,a]}({defaultProp:n,onChange:r}),u=void 0!==t,c=u?t:i;{let e=l.useRef(void 0!==t);l.useEffect(()=>{let t=e.current;if(t!==u){let e=u?"controlled":"uncontrolled";console.warn("".concat(o," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=u},[u,o])}return[c,l.useCallback(e=>{if(u){let r="function"==typeof e?e(t):e;if(r!==t){var n;null==(n=s.current)||n.call(s,r)}}else a(e)},[u,t,a,s])]}Symbol("RADIX:SYNC_STATE");var k=e.i(62521);function E(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function R(e,t){var n=E(e,t,"get");return n.get?n.get.call(e):n.value}function C(e,t,n){var r=E(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var j=e.i(88338);function M(e){let t=e+"CollectionProvider",[n,r]=y(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=l.default.useRef(null),i=l.default.useRef(new Map).current;return(0,a.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};s.displayName=t;let u=e+"CollectionSlot",c=(0,j.createSlot)(u),d=l.default.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,g.useComposedRefs)(t,o.collectionRef);return(0,a.jsx)(c,{ref:l,children:r})});d.displayName=u;let f=e+"CollectionItemSlot",p="data-radix-collection-item",h=(0,j.createSlot)(f),m=l.default.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,s=l.default.useRef(null),u=(0,g.useComposedRefs)(t,s),c=i(f,n);return l.default.useEffect(()=>(c.itemMap.set(s,{ref:s,...o}),()=>void c.itemMap.delete(s))),(0,a.jsx)(h,{...{[p]:""},ref:u,children:r})});return m.displayName=f,[{Provider:s,Slot:d,ItemSlot:m},function(t){let n=i(e+"CollectionConsumer",t);return l.default.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var N=new WeakMap;function S(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=A(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function A(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap,class e extends Map{set(e,t){return N.get(this)&&(this.has(e)?R(this,r)[R(this,r).indexOf(e)]=e:R(this,r).push(e)),super.set(e,t),this}insert(e,t,n){let o,i=this.has(t),a=R(this,r).length,l=A(e),s=l>=0?l:a+l,u=s<0||s>=a?-1:s;if(u===this.size||i&&u===this.size-1||-1===u)return this.set(t,n),this;let c=this.size+ +!i;l<0&&s++;let d=[...R(this,r)],f=!1;for(let e=s;e<c;e++)if(s===e){let r=d[e];d[e]===t&&(r=d[e+1]),i&&this.delete(t),o=this.get(r),this.set(t,n)}else{f||d[e-1]!==t||(f=!0);let n=d[f?e:e-1],r=o;o=this.get(n),this.delete(n),this.set(n,r)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=R(this,r).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let o=R(this,r).indexOf(e);return -1===o?this:this.insert(o,t,n)}after(e){let t=R(this,r).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let o=R(this,r).indexOf(e);return -1===o?this:this.insert(o+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return C(this,r,[]),super.clear()}delete(e){let t=super.delete(e);return t&&R(this,r).splice(R(this,r).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=S(R(this,r),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=S(R(this,r),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return R(this,r).indexOf(e)}keyAt(e){return S(R(this,r),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,a=null!=o?o:this.at(0);for(let e of this)a=0===i&&1===t.length?e:Reflect.apply(r,this,[a,e,i,this]),i++;return a}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,r,{writable:!0,value:void 0}),C(this,r,[...super.keys()]),N.set(this,!0)}};var T=l.createContext(void 0);function P(e){let t=l.useContext(T);return e||t||"ltr"}function O(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>function(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return null==(e=t.current)?void 0:e.call(t,...r)},[])}var D="dismissableLayer.update",L=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),I=l.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:s,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:d,onDismiss:f,...p}=e,h=l.useContext(L),[m,y]=l.useState(null),x=null!=(r=null==m?void 0:m.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,w]=l.useState({}),b=(0,g.useComposedRefs)(t,e=>y(e)),E=Array.from(h.layers),[R]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),C=E.indexOf(R),j=m?E.indexOf(m):-1,M=h.layersWithOutsidePointerEventsDisabled.size>0,N=j>=C,S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=O(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){F("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));N&&!n&&(null==u||u(e),null==d||d(e),e.defaultPrevented||null==f||f())},x),A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=O(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&F("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==c||c(e),null==d||d(e),e.defaultPrevented||null==f||f())},x);return!function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=O(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})},[r,n])}(e=>{j===h.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},x),l.useEffect(()=>{if(m)return i&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),_(),()=>{i&&1===h.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=o)}},[m,x,i,h]),l.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),_())},[m,h]),l.useEffect(()=>{let e=()=>w({});return document.addEventListener(D,e),()=>document.removeEventListener(D,e)},[]),(0,a.jsx)(k.Primitive.div,{...p,ref:b,style:{pointerEvents:M?N?"auto":"none":void 0,...e.style},onFocusCapture:v(e.onFocusCapture,A.onFocusCapture),onBlurCapture:v(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:v(e.onPointerDownCapture,S.onPointerDownCapture)})});function _(){let e=new CustomEvent(D);document.dispatchEvent(e)}function F(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,k.dispatchDiscreteCustomEvent)(i,a):i.dispatchEvent(a)}I.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(L),r=l.useRef(null),o=(0,g.useComposedRefs)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(k.Primitive.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var z=0;function B(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var W="focusScope.autoFocusOnMount",H="focusScope.autoFocusOnUnmount",K={bubbles:!1,cancelable:!0},U=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[u,c]=l.useState(null),d=O(o),f=O(i),p=l.useRef(null),h=(0,g.useComposedRefs)(t,e=>c(e)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(m.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:G(p.current,{select:!0})},t=function(e){if(m.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||G(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&G(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,m.paused]),l.useEffect(()=>{if(u){q.add(m);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(W,K);u.addEventListener(W,d),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(G(r,{select:t}),document.activeElement!==n)return}(V(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&G(u))}return()=>{u.removeEventListener(W,d),setTimeout(()=>{let t=new CustomEvent(H,K);u.addEventListener(H,f),u.dispatchEvent(t),t.defaultPrevented||G(null!=e?e:document.body,{select:!0}),u.removeEventListener(H,f),q.remove(m)},0)}}},[u,d,f,m]);let v=l.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=V(e);return[X(t,e),X(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&G(i,{select:!0})):(e.preventDefault(),n&&G(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,a.jsx)(k.Primitive.div,{tabIndex:-1,...s,ref:h,onKeyDown:v})});function V(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function X(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function G(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}U.displayName="FocusScope";var q=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=Y(e,t)).unshift(t)},remove(t){var n;null==(n=(e=Y(e,t))[0])||n.resume()}}}();function Y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var Z=l[" useId ".trim().toString()]||(()=>void 0),$=0;function J(e){let[t,n]=l.useState(Z());return x(()=>{e||n(e=>null!=e?e:String($++))},[e]),e||(t?"radix-".concat(t):"")}let Q=["top","right","bottom","left"],ee=Math.min,et=Math.max,en=Math.round,er=Math.floor,eo=e=>({x:e,y:e}),ei={left:"right",right:"left",bottom:"top",top:"bottom"},ea={start:"end",end:"start"};function el(e,t){return"function"==typeof e?e(t):e}function es(e){return e.split("-")[0]}function eu(e){return e.split("-")[1]}function ec(e){return"x"===e?"y":"x"}function ed(e){return"y"===e?"height":"width"}let ef=new Set(["top","bottom"]);function ep(e){return ef.has(es(e))?"y":"x"}function eh(e){return e.replace(/start|end/g,e=>ea[e])}let em=["left","right"],ev=["right","left"],eg=["top","bottom"],ey=["bottom","top"];function ex(e){return e.replace(/left|right|bottom|top/g,e=>ei[e])}function ew(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function eb(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ek(e,t,n){let r,{reference:o,floating:i}=e,a=ep(t),l=ec(ep(t)),s=ed(l),u=es(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(eu(t)){case"start":r[l]-=p*(n&&c?-1:1);break;case"end":r[l]+=p*(n&&c?-1:1)}return r}let eE=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=ek(u,r,s),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:x}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(f=x.placement),x.rects&&(u=!0===x.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:c,y:d}=ek(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function eR(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=el(t,e),h=ew(p),m=l[f?"floating"===d?"reference":"floating":d],v=eb(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),x=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},w=eb(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-w.top+h.top)/x.y,bottom:(w.bottom-v.bottom+h.bottom)/x.y,left:(v.left-w.left+h.left)/x.x,right:(w.right-v.right+h.right)/x.x}}function eC(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ej(e){return Q.some(t=>e[t]>=0)}let eM=new Set(["left","top"]);async function eN(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=es(n),l=eu(n),s="y"===ep(n),u=eM.has(a)?-1:1,c=i&&s?-1:1,d=el(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function eS(){return"undefined"!=typeof window}function eA(e){return eO(e)?(e.nodeName||"").toLowerCase():"#document"}function eT(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eP(e){var t;return null==(t=(eO(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eO(e){return!!eS()&&(e instanceof Node||e instanceof eT(e).Node)}function eD(e){return!!eS()&&(e instanceof Element||e instanceof eT(e).Element)}function eL(e){return!!eS()&&(e instanceof HTMLElement||e instanceof eT(e).HTMLElement)}function eI(e){return!!eS()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eT(e).ShadowRoot)}let e_=new Set(["inline","contents"]);function eF(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eY(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!e_.has(o)}let ez=new Set(["table","td","th"]),eB=[":popover-open",":modal"];function eW(e){return eB.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eH=["transform","translate","scale","rotate","perspective"],eK=["transform","translate","scale","rotate","perspective","filter"],eU=["paint","layout","strict","content"];function eV(e){let t=eX(),n=eD(e)?eY(e):e;return eH.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eK.some(e=>(n.willChange||"").includes(e))||eU.some(e=>(n.contain||"").includes(e))}function eX(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eG=new Set(["html","body","#document"]);function eq(e){return eG.has(eA(e))}function eY(e){return eT(e).getComputedStyle(e)}function eZ(e){return eD(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function e$(e){if("html"===eA(e))return e;let t=e.assignedSlot||e.parentNode||eI(e)&&e.host||eP(e);return eI(t)?t.host:t}function eJ(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=e$(t);return eq(n)?t.ownerDocument?t.ownerDocument.body:t.body:eL(n)&&eF(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=eT(o);if(i){let e=eQ(a);return t.concat(a,a.visualViewport||[],eF(o)?o:[],e&&n?eJ(e):[])}return t.concat(o,eJ(o,[],n))}function eQ(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function e0(e){let t=eY(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eL(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=en(n)!==i||en(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function e1(e){return eD(e)?e:e.contextElement}function e2(e){let t=e1(e);if(!eL(t))return eo(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=e0(t),a=(i?en(n.width):n.width)/r,l=(i?en(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let e4=eo(0);function e5(e){let t=eT(e);return eX()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:e4}function e3(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=e1(e),l=eo(1);t&&(r?eD(r)&&(l=e2(r)):l=e2(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===eT(a))&&o)?e5(a):eo(0),u=(i.left+s.x)/l.x,c=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=eT(a),t=r&&eD(r)?eT(r):r,n=e,o=eQ(n);for(;o&&r&&t!==n;){let e=e2(o),t=o.getBoundingClientRect(),r=eY(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=i,c+=a,o=eQ(n=eT(o))}}return eb({width:d,height:f,x:u,y:c})}function e6(e,t){let n=eZ(e).scrollLeft;return t?t.left+n:e3(eP(e)).left+n}function e8(e,t){let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-e6(e,n),y:n.top+t.scrollTop}}let e9=new Set(["absolute","fixed"]);function e7(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eT(e),r=eP(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=eX();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}let u=e6(r);if(u<=0){let e=r.ownerDocument,t=e.body,n=getComputedStyle(t),o="CSS1Compat"===e.compatMode&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,a=Math.abs(r.clientWidth-t.clientWidth-o);a<=25&&(i-=a)}else u<=25&&(i+=u);return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=eP(e),n=eZ(e),r=e.ownerDocument.body,o=et(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=et(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+e6(e),l=-n.scrollTop;return"rtl"===eY(r).direction&&(a+=et(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(eP(e));else if(eD(t))r=function(e,t){let n=e3(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eL(e)?e2(e):eo(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=e5(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return eb(r)}function te(e){return"static"===eY(e).position}function tt(e,t){if(!eL(e)||"fixed"===eY(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eP(e)===n&&(n=n.ownerDocument.body),n}function tn(e,t){var n;let r=eT(e);if(eW(e))return r;if(!eL(e)){let t=e$(e);for(;t&&!eq(t);){if(eD(t)&&!te(t))return t;t=e$(t)}return r}let o=tt(e,t);for(;o&&(n=o,ez.has(eA(n)))&&te(o);)o=tt(o,t);return o&&eq(o)&&te(o)&&!eV(o)?r:o||function(e){let t=e$(e);for(;eL(t)&&!eq(t);){if(eV(t))return t;if(eW(t))break;t=e$(t)}return null}(e)||r}let tr=async function(e){let t=this.getOffsetParent||tn,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eL(t),o=eP(t),i="fixed"===n,a=e3(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=eo(0);if(r||!r&&!i)if(("body"!==eA(t)||eF(o))&&(l=eZ(t)),r){let e=e3(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=e6(o));i&&!r&&o&&(s.x=e6(o));let u=!o||r||i?eo(0):e8(o,l);return{x:a.left+l.scrollLeft-s.x-u.x,y:a.top+l.scrollTop-s.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},to={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=eP(r),l=!!t&&eW(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},u=eo(1),c=eo(0),d=eL(r);if((d||!d&&!i)&&(("body"!==eA(r)||eF(a))&&(s=eZ(r)),eL(r))){let e=e3(r);u=e2(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!a||d||i?eo(0):e8(a,s);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:eP,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eW(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eJ(e,[],!1).filter(e=>eD(e)&&"body"!==eA(e)),o=null,i="fixed"===eY(e).position,a=i?e$(e):e;for(;eD(a)&&!eq(a);){let t=eY(a),n=eV(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e9.has(o.position)||eF(a)&&!n&&function e(t,n){let r=e$(t);return!(r===n||!eD(r)||eq(r))&&("fixed"===eY(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=e$(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=e7(t,n,o);return e.top=et(r.top,e.top),e.right=ee(r.right,e.right),e.bottom=ee(r.bottom,e.bottom),e.left=et(r.left,e.left),e},e7(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:tn,getElementRects:tr,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=e0(e);return{width:t,height:n}},getScale:e2,isElement:eD,isRTL:function(e){return"rtl"===eY(e).direction}};function ti(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ta=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:u,padding:c=0}=el(e,t)||{};if(null==u)return{};let d=ew(c),f={x:n,y:r},p=ec(ep(o)),h=ed(p),m=await a.getDimensions(u),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),b=w?w[g]:0;b&&await (null==a.isElement?void 0:a.isElement(w))||(b=l.floating[g]||i.floating[h]);let k=b/2-m[h]/2-1,E=ee(d[v?"top":"left"],k),R=ee(d[v?"bottom":"right"],k),C=b-m[h]-R,j=b/2-m[h]/2+(y/2-x/2),M=et(E,ee(j,C)),N=!s.arrow&&null!=eu(o)&&j!==M&&i.reference[h]/2-(j<E?E:R)-m[h]/2<0,S=N?j<E?j-E:j-C:0;return{[p]:f[p]+S,data:{[p]:M,centerOffset:j-M-S,...N&&{alignmentOffset:S}},reset:N}}});var tl=e.i(50321),ts="undefined"!=typeof document?l.useLayoutEffect:function(){};function tu(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!tu(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!tu(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function tc(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function td(e,t){let n=tc(e);return Math.round(t*n)/n}function tf(e){let t=l.useRef(e);return ts(()=>{t.current=e}),t}var tp=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,a.jsx)(k.Primitive.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tp.displayName="Arrow";var th="Popper",[tm,tv]=y(th),[tg,ty]=tm(th),tx=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,a.jsx)(tg,{scope:t,anchor:r,onAnchorChange:o,children:n})};tx.displayName=th;var tw="PopperAnchor",tb=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=ty(tw,n),s=l.useRef(null),u=(0,g.useComposedRefs)(t,s),c=l.useRef(null);return l.useEffect(()=>{let e=c.current;c.current=(null==r?void 0:r.current)||s.current,e!==c.current&&i.onAnchorChange(c.current)}),r?null:(0,a.jsx)(k.Primitive.div,{...o,ref:u})});tb.displayName=tw;var tk="PopperContent",[tE,tR]=tm(tk),tC=l.forwardRef((e,t)=>{var n,r,o,i,s,u,c,d;let{__scopePopper:f,side:p="bottom",sideOffset:h=0,align:m="center",alignOffset:v=0,arrowPadding:y=0,avoidCollisions:w=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:j="optimized",onPlaced:M,...N}=e,S=ty(tk,f),[A,T]=l.useState(null),P=(0,g.useComposedRefs)(t,e=>T(e)),[D,L]=l.useState(null),I=function(e){let[t,n]=l.useState(void 0);return x(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(D),_=null!=(c=null==I?void 0:I.width)?c:0,F=null!=(d=null==I?void 0:I.height)?d:0,z="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(b)?b:[b],W=B.length>0,H={padding:z,boundary:B.filter(tS),altBoundary:W},{refs:K,floatingStyles:U,placement:V,isPositioned:X,middlewareData:G}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=l.useState(r);tu(p,r)||h(r);let[m,v]=l.useState(null),[g,y]=l.useState(null),x=l.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),w=l.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=i||m,k=a||g,E=l.useRef(null),R=l.useRef(null),C=l.useRef(d),j=null!=u,M=tf(u),N=tf(o),S=tf(c),A=l.useCallback(()=>{if(!E.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};N.current&&(e.platform=N.current),((e,t,n)=>{let r=new Map,o={platform:to,...n},i={...o.platform,_c:r};return eE(e,t,{...o,platform:i})})(E.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};T.current&&!tu(C.current,t)&&(C.current=t,tl.flushSync(()=>{f(t)}))})},[p,t,n,N,S]);ts(()=>{!1===c&&C.current.isPositioned&&(C.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let T=l.useRef(!1);ts(()=>(T.current=!0,()=>{T.current=!1}),[]),ts(()=>{if(b&&(E.current=b),k&&(R.current=k),b&&k){if(M.current)return M.current(b,k,A);A()}},[b,k,A,M,j]);let P=l.useMemo(()=>({reference:E,floating:R,setReference:x,setFloating:w}),[x,w]),O=l.useMemo(()=>({reference:b,floating:k}),[b,k]),D=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=td(O.floating,d.x),r=td(O.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...tc(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,O.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:A,refs:P,elements:O,floatingStyles:D}),[d,A,P,O,D])}({strategy:"fixed",placement:p+("center"!==m?"-"+m:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=e1(e),d=i||a?[...c?eJ(c):[],...eJ(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=c&&s?function(e,t){let n,r=null,o=eP(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(l||t(),!f||!p)return;let h=er(d),m=er(o.clientWidth-(c+f)),v={rootMargin:-h+"px "+-m+"px "+-er(o.clientHeight-(d+p))+"px "+-er(c)+"px",threshold:et(0,ee(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||ti(u,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?e3(e):null;return u&&function t(){let r=e3(e);m&&!ti(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===j})},elements:{reference:S.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await eN(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}))({mainAxis:h+F,alignmentAxis:v}),w&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=el(e,t),u={x:n,y:r},c=await eR(t,s),d=ep(es(o)),f=ec(d),p=u[f],h=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=et(n,ee(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=et(n,ee(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=el(e,t),c={x:n,y:r},d=ep(o),f=ec(d),p=c[f],h=c[d],m=el(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===f?"width":"height",t=eM.has(es(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}))():void 0,...H}),w&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...x}=el(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let w=es(l),b=ep(c),k=es(c)===c,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),R=m||(k||!y?[ex(c)]:function(e){let t=ex(e);return[eh(e),t,eh(t)]}(c)),C="none"!==g;!m&&C&&R.push(...function(e,t,n,r){let o=eu(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?ev:em;return t?em:ev;case"left":case"right":return t?eg:ey;default:return[]}}(es(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(eh)))),i}(c,y,g,E));let j=[c,...R],M=await eR(t,x),N=[],S=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&N.push(M[w]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=eu(e),o=ec(ep(e)),i=ed(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=ex(a)),[a,ex(a)]}(l,u,E);N.push(M[e[0]],M[e[1]])}if(S=[...S,{placement:l,overflows:N}],!N.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==h||b===ep(t)||S.every(e=>ep(e.placement)!==b||e.overflows[0]>0)))return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(i=S.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=S.filter(e=>{if(C){let t=ep(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...H}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:s,elements:u}=t,{apply:c=()=>{},...d}=el(e,t),f=await eR(t,d),p=es(a),h=eu(a),m="y"===ep(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,x=v-f.left-f.right,w=ee(g-f[o],y),b=ee(v-f[i],x),k=!t.middlewareData.shift,E=w,R=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(R=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=y),k&&!h){let e=et(f.left,0),t=et(f.right,0),n=et(f.top,0),r=et(f.bottom,0);m?R=v-2*(0!==e||0!==t?e+t:et(f.left,f.right)):E=g-2*(0!==n||0!==r?n+r:et(f.top,f.bottom))}await c({...t,availableWidth:R,availableHeight:E});let C=await s.getDimensions(u.floating);return v!==C.width||g!==C.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),D&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ta({element:n.current,padding:r}).fn(t):{}:n?ta({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:D,padding:y}),tA({arrowWidth:_,arrowHeight:F}),C&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=el(e,t);switch(r){case"referenceHidden":{let e=eC(await eR(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ej(e)}}}case"escaped":{let e=eC(await eR(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ej(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...H})]}),[q,Y]=tT(V),Z=O(M);x(()=>{X&&(null==Z||Z())},[X,Z]);let $=null==(n=G.arrow)?void 0:n.x,J=null==(r=G.arrow)?void 0:r.y,Q=(null==(o=G.arrow)?void 0:o.centerOffset)!==0,[en,eo]=l.useState();return x(()=>{A&&eo(window.getComputedStyle(A).zIndex)},[A]),(0,a.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:X?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(i=G.transformOrigin)?void 0:i.x,null==(s=G.transformOrigin)?void 0:s.y].join(" "),...(null==(u=G.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(tE,{scope:f,placedSide:q,onArrowChange:L,arrowX:$,arrowY:J,shouldHideArrow:Q,children:(0,a.jsx)(k.Primitive.div,{"data-side":q,"data-align":Y,...N,ref:P,style:{...N.style,animation:X?void 0:"none"}})})})});tC.displayName=tk;var tj="PopperArrow",tM={top:"bottom",right:"left",bottom:"top",left:"right"},tN=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tR(tj,n),i=tM[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(tp,{...r,ref:t,style:{...r.style,display:"block"}})})});function tS(e){return null!==e}tN.displayName=tj;var tA=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=tT(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=u.arrow)?void 0:o.y)?a:0)+f/2,y="",x="";return"bottom"===p?(y=c?m:"".concat(v,"px"),x="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),x="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),x=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),x=c?m:"".concat(g,"px")),{data:{x:y,y:x}}}});function tT(e){let[t,n="center"]=e.split("-");return[t,n]}var tP=l.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[s,u]=l.useState(!1);x(()=>u(!0),[]);let c=o||s&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?tl.default.createPortal((0,a.jsx)(k.Primitive.div,{...i,ref:t}),c):null});tP.displayName="Portal";var tO=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=l.useState(),i=l.useRef(null),a=l.useRef(e),s=l.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return l.useEffect(()=>{let e=tD(i.current);s.current="mounted"===u?e:"none"},[u]),x(()=>{let t=i.current,n=a.current;if(n!==e){let r=s.current,o=tD(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),x(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=tD(i.current).includes(CSS.escape(e.animationName));if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(s.current=tD(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):l.Children.only(n),i=(0,g.useComposedRefs)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?l.cloneElement(o,{ref:i}):null};function tD(e){return(null==e?void 0:e.animationName)||"none"}tO.displayName="Presence";var tL="rovingFocusGroup.onEntryFocus",tI={bubbles:!1,cancelable:!0},t_="RovingFocusGroup",[tF,tz,tB]=M(t_),[tW,tH]=y(t_,[tB]),[tK,tU]=tW(t_),tV=l.forwardRef((e,t)=>(0,a.jsx)(tF.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(tF.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(tX,{...e,ref:t})})}));tV.displayName=t_;var tX=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:c,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...p}=e,h=l.useRef(null),m=(0,g.useComposedRefs)(t,h),y=P(i),[x,w]=b({prop:s,defaultProp:null!=u?u:null,onChange:c,caller:t_}),[E,R]=l.useState(!1),C=O(d),j=tz(n),M=l.useRef(!1),[N,S]=l.useState(0);return l.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(tL,C),()=>e.removeEventListener(tL,C)},[C]),(0,a.jsx)(tK,{scope:n,orientation:r,dir:y,loop:o,currentTabStopId:x,onItemFocus:l.useCallback(e=>w(e),[w]),onItemShiftTab:l.useCallback(()=>R(!0),[]),onFocusableItemAdd:l.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:l.useCallback(()=>S(e=>e-1),[]),children:(0,a.jsx)(k.Primitive.div,{tabIndex:E||0===N?-1:0,"data-orientation":r,...p,ref:m,style:{outline:"none",...e.style},onMouseDown:v(e.onMouseDown,()=>{M.current=!0}),onFocus:v(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(tL,tI);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);tZ([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),f)}}M.current=!1}),onBlur:v(e.onBlur,()=>R(!1))})})}),tG="RovingFocusGroupItem",tq=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:s,...u}=e,c=J(),d=i||c,f=tU(tG,n),p=f.currentTabStopId===d,h=tz(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:y}=f;return l.useEffect(()=>{if(r)return m(),()=>g()},[r,m,g]),(0,a.jsx)(tF.ItemSlot,{scope:n,id:d,focusable:r,active:o,children:(0,a.jsx)(k.Primitive.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...u,ref:t,onMouseDown:v(e.onMouseDown,e=>{r?f.onItemFocus(d):e.preventDefault()}),onFocus:v(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:v(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tY[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tZ(n))}}),children:"function"==typeof s?s({isCurrentTabStop:p,hasTabStop:null!=y}):s})})});tq.displayName=tG;var tY={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tZ(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var t$=new WeakMap,tJ=new WeakMap,tQ={},t0=0,t1=function(e){return e&&(e.host||t1(e.parentNode))},t2=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=t1(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tQ[n]||(tQ[n]=new WeakMap);var i=tQ[n],a=[],l=new Set,s=new Set(o),u=function(e){!e||l.has(e)||(l.add(e),u(e.parentNode))};o.forEach(u);var c=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(t$.get(e)||0)+1,u=(i.get(e)||0)+1;t$.set(e,s),i.set(e,u),a.push(e),1===s&&o&&tJ.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),l.clear(),t0++,function(){a.forEach(function(e){var t=t$.get(e)-1,o=i.get(e)-1;t$.set(e,t),i.set(e,o),t||(tJ.has(e)||e.removeAttribute(r),tJ.delete(e)),o||e.removeAttribute(n)}),--t0||(t$=new WeakMap,t$=new WeakMap,tJ=new WeakMap,tQ={})}},t4=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),t2(r,o,n,"aria-hidden")):function(){return null}},t5=function(){return(t5=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t3(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var t6=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),t8="width-before-scroll-bar";function t9(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var t7="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,ne=new WeakMap;function nt(e){return e}var nn=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=nt),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return o.options=t5({async:!0,ssr:!1},e),o}(),nr=function(){},no=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),s=l.useState({onScrollCapture:nr,onWheelCapture:nr,onTouchMoveCapture:nr}),u=s[0],c=s[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,x=e.noIsolation,w=e.inert,b=e.allowPinchZoom,k=e.as,E=e.gapMode,R=t3(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[a,t],r=function(e){return n.forEach(function(t){return t9(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,t7(function(){var e=ne.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||t9(e,null)}),r.forEach(function(e){t.has(e)||t9(e,o)})}ne.set(i,n)},[n]),i),j=t5(t5({},R),u);return l.createElement(l.Fragment,null,m&&l.createElement(g,{sideCar:nn,removeScrollBar:h,shards:v,noRelative:y,noIsolation:x,inert:w,setCallbacks:c,allowPinchZoom:!!b,lockRef:a,gapMode:E}),d?l.cloneElement(l.Children.only(f),t5(t5({},j),{ref:C})):l.createElement(void 0===k?"div":k,t5({},j,{className:p,ref:C}),f))});no.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},no.classNames={fullWidth:t8,zeroRight:t6};var ni=function(e){var t=e.sideCar,n=t3(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,t5({},n))};ni.isSideCarExport=!0;var na=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return t&&e.setAttribute("nonce",t),e}())){var r,o;(r=t).styleSheet?r.styleSheet.cssText=n:r.appendChild(document.createTextNode(n)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},nl=function(){var e=na();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ns=function(){var e=nl();return function(t){return e(t.styles,t.dynamic),null}},nu={left:0,top:0,right:0,gap:0},nc=function(e){return parseInt(e||"",10)||0},nd=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[nc(n),nc(r),nc(o)]},nf=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return nu;var t=nd(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},np=ns(),nh="data-scroll-locked",nm=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(nh,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(t6," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(t8," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(t6," .").concat(t6," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(t8," .").concat(t8," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(nh,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},nv=function(){var e=parseInt(document.body.getAttribute(nh)||"0",10);return isFinite(e)?e:0},ng=function(){l.useEffect(function(){return document.body.setAttribute(nh,(nv()+1).toString()),function(){var e=nv()-1;e<=0?document.body.removeAttribute(nh):document.body.setAttribute(nh,e.toString())}},[])},ny=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;ng();var i=l.useMemo(function(){return nf(o)},[o]);return l.createElement(np,{styles:nm(i,!t,o,n?"":"!important")})},nx=!1;if("undefined"!=typeof window)try{var nw=Object.defineProperty({},"passive",{get:function(){return nx=!0,!0}});window.addEventListener("test",nw,nw),window.removeEventListener("test",nw,nw)}catch(e){nx=!1}var nb=!!nx&&{passive:!1},nk=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nE=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nR(e,r)){var o=nC(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body)return!1},nR=function(e,t){return"v"===e?nk(t,"overflowY"):nk(t,"overflowX")},nC=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nj=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,u=t.contains(s),c=!1,d=l>0,f=0,p=0;do{if(!s)break;var h=nC(e,s),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&nR(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s))return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},nM=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nN=function(e){return[e.deltaX,e.deltaY]},nS=function(e){return e&&"current"in e?e.current:e},nA=0,nT=[];let nP=(t=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(nA++)[0],i=l.useState(ns)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nS),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=nM(e),l=n.current,s="deltaX"in e?e.deltaX:l[0]-i[0],u="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=nE(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=nE(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=o),!o)return!0;var p=r.current||o;return nj(p,t,e,"h"===p?s:u,!0)},[]),u=l.useCallback(function(e){if(nT.length&&nT[nT.length-1]===i){var n="deltaY"in e?nN(e):nM(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nS).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=nM(e),r.current=void 0},[]),f=l.useCallback(function(t){c(t.type,nN(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){c(t.type,nM(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return nT.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,nb),document.addEventListener("touchmove",u,nb),document.addEventListener("touchstart",d,nb),function(){nT=nT.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,nb),document.removeEventListener("touchmove",u,nb),document.removeEventListener("touchstart",d,nb)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(ny,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},nn.useMedium(t),ni);var nO=l.forwardRef(function(e,t){return l.createElement(no,t5({},e,{ref:t,sideCar:nP}))});nO.classNames=no.classNames;var nD=["Enter"," "],nL=["ArrowUp","PageDown","End"],nI=["ArrowDown","PageUp","Home",...nL],n_={ltr:[...nD,"ArrowRight"],rtl:[...nD,"ArrowLeft"]},nF={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nz="Menu",[nB,nW,nH]=M(nz),[nK,nU]=y(nz,[nH,tv,tH]),nV=tv(),nX=tH(),[nG,nq]=nK(nz),[nY,nZ]=nK(nz),n$=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:s=!0}=e,u=nV(t),[c,d]=l.useState(null),f=l.useRef(!1),p=O(i),h=P(o);return l.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,a.jsx)(tx,{...u,children:(0,a.jsx)(nG,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,a.jsx)(nY,{scope:t,onClose:l.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:s,children:r})})})};n$.displayName=nz;var nJ=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nV(n);return(0,a.jsx)(tb,{...o,...r,ref:t})});nJ.displayName="MenuAnchor";var nQ="MenuPortal",[n0,n1]=nK(nQ,{forceMount:void 0}),n2=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nq(nQ,t);return(0,a.jsx)(n0,{scope:t,forceMount:n,children:(0,a.jsx)(tO,{present:n||i.open,children:(0,a.jsx)(tP,{asChild:!0,container:o,children:r})})})};n2.displayName=nQ;var n4="MenuContent",[n5,n3]=nK(n4),n6=l.forwardRef((e,t)=>{let n=n1(n4,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nq(n4,e.__scopeMenu),l=nZ(n4,e.__scopeMenu);return(0,a.jsx)(nB.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(tO,{present:r||i.open,children:(0,a.jsx)(nB.Slot,{scope:e.__scopeMenu,children:l.modal?(0,a.jsx)(n8,{...o,ref:t}):(0,a.jsx)(n9,{...o,ref:t})})})})}),n8=l.forwardRef((e,t)=>{let n=nq(n4,e.__scopeMenu),r=l.useRef(null),o=(0,g.useComposedRefs)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return t4(e)},[]),(0,a.jsx)(re,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:v(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),n9=l.forwardRef((e,t)=>{let n=nq(n4,e.__scopeMenu);return(0,a.jsx)(re,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),n7=(0,j.createSlot)("MenuContent.ScrollLock"),re=l.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:u,onEntryFocus:c,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,onDismiss:m,disableOutsideScroll:y,...x}=e,w=nq(n4,n),b=nZ(n4,n),k=nV(n),E=nX(n),R=nW(n),[C,j]=l.useState(null),M=l.useRef(null),N=(0,g.useComposedRefs)(t,M,w.onContentChange),S=l.useRef(0),A=l.useRef(""),T=l.useRef(0),P=l.useRef(null),O=l.useRef("right"),D=l.useRef(0),L=y?nO:l.Fragment;l.useEffect(()=>()=>window.clearTimeout(S.current),[]),l.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:B()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:B()),z++,()=>{1===z&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),z--}},[]);let _=l.useCallback(e=>{var t,n;return O.current===(null==(t=P.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,u=a.y,c=l.x,d=l.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=P.current)?void 0:n.area)},[]);return(0,a.jsx)(n5,{scope:n,searchRef:A,onItemEnter:l.useCallback(e=>{_(e)&&e.preventDefault()},[_]),onItemLeave:l.useCallback(e=>{var t;_(e)||(null==(t=M.current)||t.focus(),j(null))},[_]),onTriggerLeave:l.useCallback(e=>{_(e)&&e.preventDefault()},[_]),pointerGraceTimerRef:T,onPointerGraceIntentChange:l.useCallback(e=>{P.current=e},[]),children:(0,a.jsx)(L,{...y?{as:n7,allowPinchZoom:!0}:void 0,children:(0,a.jsx)(U,{asChild:!0,trapped:o,onMountAutoFocus:v(i,e=>{var t;e.preventDefault(),null==(t=M.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,a.jsx)(I,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,onDismiss:m,children:(0,a.jsx)(tV,{asChild:!0,...E,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:j,onEntryFocus:v(c,e=>{b.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,a.jsx)(tC,{role:"menu","aria-orientation":"vertical","data-state":rj(w.open),"data-radix-menu-content":"",dir:b.dir,...k,...x,ref:N,style:{outline:"none",...x.style},onKeyDown:v(x.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=A.current+e,o=R().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),s=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){A.current=t,window.clearTimeout(S.current),""!==t&&(S.current=window.setTimeout(()=>e(""),1e3))}(r),s&&setTimeout(()=>s.focus())})(e.key));let o=M.current;if(e.target!==o||!nI.includes(e.key))return;e.preventDefault();let i=R().filter(e=>!e.disabled).map(e=>e.ref.current);nL.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:v(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(S.current),A.current="")}),onPointerMove:v(e.onPointerMove,rS(e=>{let t=e.target,n=D.current!==e.clientX;e.currentTarget.contains(t)&&n&&(O.current=e.clientX>D.current?"right":"left",D.current=e.clientX)}))})})})})})})});n6.displayName=n4;var rt=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(k.Primitive.div,{role:"group",...r,ref:t})});rt.displayName="MenuGroup";var rn=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(k.Primitive.div,{...r,ref:t})});rn.displayName="MenuLabel";var rr="MenuItem",ro="menu.itemSelect",ri=l.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=l.useRef(null),s=nZ(rr,e.__scopeMenu),u=n3(rr,e.__scopeMenu),c=(0,g.useComposedRefs)(t,i),d=l.useRef(!1);return(0,a.jsx)(ra,{...o,ref:c,disabled:n,onClick:v(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(ro,{bubbles:!0,cancelable:!0});e.addEventListener(ro,e=>null==r?void 0:r(e),{once:!0}),(0,k.dispatchDiscreteCustomEvent)(e,t),t.defaultPrevented?d.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),d.current=!0},onPointerUp:v(e.onPointerUp,e=>{var t;d.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:v(e.onKeyDown,e=>{let t=""!==u.searchRef.current;n||t&&" "===e.key||nD.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ri.displayName=rr;var ra=l.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,s=n3(rr,n),u=nX(n),c=l.useRef(null),d=(0,g.useComposedRefs)(t,c),[f,p]=l.useState(!1),[h,m]=l.useState("");return l.useEffect(()=>{let e=c.current;if(e){var t;m((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,a.jsx)(nB.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:h,children:(0,a.jsx)(tq,{asChild:!0,...u,focusable:!r,children:(0,a.jsx)(k.Primitive.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:d,onPointerMove:v(e.onPointerMove,rS(e=>{r?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:v(e.onPointerLeave,rS(e=>s.onItemLeave(e))),onFocus:v(e.onFocus,()=>p(!0)),onBlur:v(e.onBlur,()=>p(!1))})})})}),rl=l.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,a.jsx)(rm,{scope:e.__scopeMenu,checked:n,children:(0,a.jsx)(ri,{role:"menuitemcheckbox","aria-checked":rM(n)?"mixed":n,...o,ref:t,"data-state":rN(n),onSelect:v(o.onSelect,()=>null==r?void 0:r(!!rM(n)||!n),{checkForDefaultPrevented:!1})})})});rl.displayName="MenuCheckboxItem";var rs="MenuRadioGroup",[ru,rc]=nK(rs,{value:void 0,onValueChange:()=>{}}),rd=l.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=O(r);return(0,a.jsx)(ru,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,a.jsx)(rt,{...o,ref:t})})});rd.displayName=rs;var rf="MenuRadioItem",rp=l.forwardRef((e,t)=>{let{value:n,...r}=e,o=rc(rf,e.__scopeMenu),i=n===o.value;return(0,a.jsx)(rm,{scope:e.__scopeMenu,checked:i,children:(0,a.jsx)(ri,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":rN(i),onSelect:v(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});rp.displayName=rf;var rh="MenuItemIndicator",[rm,rv]=nK(rh,{checked:!1}),rg=l.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=rv(rh,n);return(0,a.jsx)(tO,{present:r||rM(i.checked)||!0===i.checked,children:(0,a.jsx)(k.Primitive.span,{...o,ref:t,"data-state":rN(i.checked)})})});rg.displayName=rh;var ry=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,a.jsx)(k.Primitive.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ry.displayName="MenuSeparator";var rx=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nV(n);return(0,a.jsx)(tN,{...o,...r,ref:t})});rx.displayName="MenuArrow";var[rw,rb]=nK("MenuSub"),rk="MenuSubTrigger",rE=l.forwardRef((e,t)=>{let n=nq(rk,e.__scopeMenu),r=nZ(rk,e.__scopeMenu),o=rb(rk,e.__scopeMenu),i=n3(rk,e.__scopeMenu),s=l.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:c}=i,d={__scopeMenu:e.__scopeMenu},f=l.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return l.useEffect(()=>f,[f]),l.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),c(null)}},[u,c]),(0,a.jsx)(nJ,{asChild:!0,...d,children:(0,a.jsx)(ra,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":rj(n.open),...e,ref:(0,g.composeRefs)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:v(e.onPointerMove,rS(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:v(e.onPointerLeave,rS(e=>{var t,r;f();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,l=o[a?"left":"right"],s=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:s,y:o.top},{x:s,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:v(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&n_[r.dir].includes(t.key)){var a;n.onOpenChange(!0),null==(a=n.content)||a.focus(),t.preventDefault()}})})})});rE.displayName=rk;var rR="MenuSubContent",rC=l.forwardRef((e,t)=>{let n=n1(n4,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nq(n4,e.__scopeMenu),s=nZ(n4,e.__scopeMenu),u=rb(rR,e.__scopeMenu),c=l.useRef(null),d=(0,g.useComposedRefs)(t,c);return(0,a.jsx)(nB.Provider,{scope:e.__scopeMenu,children:(0,a.jsx)(tO,{present:r||i.open,children:(0,a.jsx)(nB.Slot,{scope:e.__scopeMenu,children:(0,a.jsx)(re,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:d,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:v(e.onFocusOutside,e=>{e.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:v(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:v(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nF[s.dir].includes(e.key);if(t&&n){var r;i.onOpenChange(!1),null==(r=u.trigger)||r.focus(),e.preventDefault()}})})})})})});function rj(e){return e?"open":"closed"}function rM(e){return"indeterminate"===e}function rN(e){return rM(e)?"indeterminate":e?"checked":"unchecked"}function rS(e){return t=>"mouse"===t.pointerType?e(t):void 0}rC.displayName=rR;var rA="DropdownMenu",[rT,rP]=y(rA,[nU]),rO=nU(),[rD,rL]=rT(rA),rI=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:s,modal:u=!0}=e,c=rO(t),d=l.useRef(null),[f,p]=b({prop:o,defaultProp:null!=i&&i,onChange:s,caller:rA});return(0,a.jsx)(rD,{scope:t,triggerId:J(),triggerRef:d,contentId:J(),open:f,onOpenChange:p,onOpenToggle:l.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,a.jsx)(n$,{...c,open:f,onOpenChange:p,dir:r,modal:u,children:n})})};rI.displayName=rA;var r_="DropdownMenuTrigger",rF=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=rL(r_,n),l=rO(n);return(0,a.jsx)(nJ,{asChild:!0,...l,children:(0,a.jsx)(k.Primitive.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,g.composeRefs)(t,i.triggerRef),onPointerDown:v(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:v(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rF.displayName=r_;var rz=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rO(t);return(0,a.jsx)(n2,{...r,...n})};rz.displayName="DropdownMenuPortal";var rB="DropdownMenuContent",rW=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rL(rB,n),i=rO(n),s=l.useRef(!1);return(0,a.jsx)(n6,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:v(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=o.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:v(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rW.displayName=rB,l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rt,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var rH=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rn,{...o,...r,ref:t})});rH.displayName="DropdownMenuLabel";var rK=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(ri,{...o,...r,ref:t})});rK.displayName="DropdownMenuItem";var rU=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rl,{...o,...r,ref:t})});rU.displayName="DropdownMenuCheckboxItem",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rd,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup";var rV=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rp,{...o,...r,ref:t})});rV.displayName="DropdownMenuRadioItem";var rX=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rg,{...o,...r,ref:t})});rX.displayName="DropdownMenuItemIndicator";var rG=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(ry,{...o,...r,ref:t})});rG.displayName="DropdownMenuSeparator",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rx,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var rq=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rE,{...o,...r,ref:t})});rq.displayName="DropdownMenuSubTrigger";var rY=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(n);return(0,a.jsx)(rC,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rY.displayName="DropdownMenuSubContent";let rZ=(0,d.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var r$=e.i(22848);let rJ=(0,d.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var rQ=e.i(36946);l.forwardRef((e,t)=>{let{className:n,inset:r,children:o,...i}=e;return(0,a.jsxs)(rq,{ref:t,className:(0,rQ.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",n),...i,children:[o,(0,a.jsx)(r$.ChevronRight,{className:"ml-auto h-4 w-4"})]})}).displayName=rq.displayName,l.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(rY,{ref:t,className:(0,rQ.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...r})}).displayName=rY.displayName;let r0=l.forwardRef((e,t)=>{let{className:n,sideOffset:r=4,...o}=e;return(0,a.jsx)(rz,{children:(0,a.jsx)(rW,{ref:t,sideOffset:r,className:(0,rQ.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...o})})});r0.displayName=rW.displayName;let r1=l.forwardRef((e,t)=>{let{className:n,inset:r,...o}=e;return(0,a.jsx)(rK,{ref:t,className:(0,rQ.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",n),...o})});function r2(){let{theme:e,actualTheme:t,mounted:n,setTheme:r}=(0,m.useTheme)();return(0,a.jsxs)(rI,{children:[(0,a.jsx)(rF,{asChild:!0,children:(0,a.jsxs)(c.Button,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{n&&r("light"===t?"dark":"light")},title:"Switch to ".concat("light"===t?"dark":"light"," mode"),children:[(0,a.jsx)(p,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(f,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(r0,{align:"end",children:[(0,a.jsxs)(r1,{onClick:()=>r("light"),children:[(0,a.jsx)(p,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Light"})]}),(0,a.jsxs)(r1,{onClick:()=>r("dark"),children:[(0,a.jsx)(f,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dark"})]}),(0,a.jsxs)(r1,{onClick:()=>r("system"),children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"System"})]})]})]})}r1.displayName=rK.displayName,l.forwardRef((e,t)=>{let{className:n,children:r,checked:o,...i}=e;return(0,a.jsxs)(rU,{ref:t,className:(0,rQ.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),checked:o,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(rX,{children:(0,a.jsx)(rZ,{className:"h-4 w-4"})})}),r]})}).displayName=rU.displayName,l.forwardRef((e,t)=>{let{className:n,children:r,...o}=e;return(0,a.jsxs)(rV,{ref:t,className:(0,rQ.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(rX,{children:(0,a.jsx)(rJ,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=rV.displayName,l.forwardRef((e,t)=>{let{className:n,inset:r,...o}=e;return(0,a.jsx)(rH,{ref:t,className:(0,rQ.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",n),...o})}).displayName=rH.displayName,l.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,a.jsx)(rG,{ref:t,className:(0,rQ.cn)("-mx-1 my-1 h-px bg-muted",n),...r})}).displayName=rG.displayName;let r4=(0,d.default)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var r5=e.i(57606),r3=e.i(80873);let r6=(0,d.default)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),r8=(0,d.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);e.s(["Bell",()=>r9],46404);let r9=(0,d.default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var r7=e.i(96832);e.s(["School",()=>oe],93020);let oe=(0,d.default)("school",[["path",{d:"M14 21v-3a2 2 0 0 0-4 0v3",key:"1rgiei"}],["path",{d:"M18 5v16",key:"1ethyx"}],["path",{d:"m4 6 7.106-3.79a2 2 0 0 1 1.788 0L20 6",key:"zywc2d"}],["path",{d:"m6 11-3.52 2.147a1 1 0 0 0-.48.854V19a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-5a1 1 0 0 0-.48-.853L18 11",key:"1d4ql0"}],["path",{d:"M6 5v16",key:"1sn0nx"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]]);var ot=e.i(35255),on=e.i(66792),or=e.i(23178);e.s(["Users",()=>oo],87523);let oo=(0,d.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);e.s(["BookOpen",()=>oi],69556);let oi=(0,d.default)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);e.s(["GraduationCap",()=>oa],35505);let oa=(0,d.default)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var ol=e.i(96274);e.s(["BarChart3",()=>os],21138);let os=(0,d.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);e.s(["Calendar",()=>ou],66505);let ou=(0,d.default)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var oc=e.i(18498),od=e.i(7418);e.s(["ClipboardList",()=>of],20067);let of=(0,d.default)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);e.s(["Award",()=>op],10535);let op=(0,d.default)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),oh={Plus:ot.Plus,Upload:on.Upload,Download:or.Download,Users:oo,BookOpen:oi,GraduationCap:oa,FileText:ol.FileText,BarChart3:os,Calendar:ou,Home:oc.Home,Settings:r8,Bell:r9,User:r3.User,Edit:od.Edit,ClipboardList:of,Award:op};function om(e){var t,n,r,o;let{children:i,title:d,navigation:f}=e,{data:p}=(0,s.useSession)(),h=(0,u.useRouter)(),[m,v]=(0,l.useState)(!1),g=async()=>{await (0,s.signOut)({callbackUrl:"/"})},y=e=>oh[e]||oc.Home;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-950",children:[(0,a.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden ".concat(m?"block":"hidden"),children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>v(!1)}),(0,a.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(oe,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-lg font-semibold",children:"SMS"})]}),(0,a.jsx)(c.Button,{variant:"ghost",size:"sm",onClick:()=>v(!1),children:(0,a.jsx)(r5.X,{className:"h-5 w-5"})})]}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:f.map(e=>{let t=y(e.icon);return(0,a.jsxs)(c.Button,{variant:"ghost",className:"w-full justify-start",onClick:()=>{h.push(e.href),v(!1)},children:[(0,a.jsx)(t,{className:"mr-3 h-5 w-5"}),e.name]},"mobile-".concat(e.name))})})]})]}),(0,a.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,a.jsxs)("div",{className:"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center px-4",children:[(0,a.jsx)(oe,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-lg font-semibold hidden xl:inline",children:"School Management System"}),(0,a.jsx)("span",{className:"ml-2 text-lg font-semibold xl:hidden",children:"SMS"})]}),(0,a.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:f.map(e=>{let t=y(e.icon);return(0,a.jsxs)(c.Button,{variant:"ghost",className:"w-full justify-start",onClick:()=>h.push(e.href),children:[(0,a.jsx)(t,{className:"mr-3 h-5 w-5"}),e.name]},"desktop-".concat(e.name))})})]})}),(0,a.jsxs)("div",{className:"lg:pl-64",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8",children:[(0,a.jsx)(c.Button,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>v(!0),children:(0,a.jsx)(r4,{className:"h-5 w-5"})}),(0,a.jsx)("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:(0,a.jsxs)("div",{className:"relative flex flex-1",children:[(0,a.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,a.jsx)(r7.Search,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Search...",className:"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent"})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-x-4 lg:gap-x-6",children:[(0,a.jsx)(r2,{}),(0,a.jsx)(c.Button,{variant:"ghost",size:"sm",children:(0,a.jsx)(r9,{className:"h-5 w-5"})}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("div",{className:"flex items-center gap-x-3",children:[(0,a.jsxs)("div",{className:"text-sm hidden sm:block",children:[(0,a.jsxs)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:[null==p||null==(t=p.user)?void 0:t.firstName," ",null==p||null==(n=p.user)?void 0:n.lastName]}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 capitalize",children:null==p||null==(o=p.user)||null==(r=o.role)?void 0:r.toLowerCase()})]}),(0,a.jsx)("div",{className:"flex items-center gap-x-2",children:(0,a.jsxs)(c.Button,{variant:"ghost",size:"sm",onClick:g,className:"flex items-center gap-2",children:[(0,a.jsx)(r6,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Sign Out"})]})})]})})]})]}),(0,a.jsx)("main",{className:"py-6",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:d})}),i]})})]})]})}}]);