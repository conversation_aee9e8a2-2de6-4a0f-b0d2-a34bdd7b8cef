(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,92521,e=>{"use strict";e.s(["Label",()=>d],92521);var r=e.i(53379),t=e.i(46686),a=e.i(62521),i=t.forwardRef((e,t)=>(0,r.jsx)(a.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var s=e.i(94323),l=e.i(36946);let n=(0,s.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(i,{ref:t,className:(0,l.cn)(n(),a),...s})});d.displayName=i.displayName},70307,e=>{"use strict";e.s(["Alert",()=>l,"AlertDescription",()=>n]);var r=e.i(53379),t=e.i(46686),a=e.i(94323),i=e.i(36946);let s=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),l=t.forwardRef((e,t)=>{let{className:a,variant:l,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(s({variant:l}),a),...n})});l.displayName="Alert",t.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let n=t.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",a),...s})});n.displayName="AlertDescription"},18125,(e,r,t)=>{r.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>i,"CardContent",()=>d,"CardDescription",()=>n,"CardHeader",()=>s,"CardTitle",()=>l]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let i=t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",i),...s})});i.displayName="Card";let s=t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",i),...s})});s.displayName="CardHeader";let l=t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",i),...s})});l.displayName="CardTitle";let n=t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",i),...s})});n.displayName="CardDescription";let d=t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",i),...s})});d.displayName="CardContent",t.forwardRef((e,t)=>{let{className:i,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",i),...s})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>f],30151);var r=e.i(53379),t=e.i(46686);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return e=>{let t=!1,i=r.map(r=>{let i=a(r,e);return t||"function"!=typeof i||(t=!0),i});if(t)return()=>{for(let e=0;e<i.length;e++){let t=i[e];"function"==typeof t?t():a(r[e],null)}}}}function s(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t.useCallback(i(...r),r)}function l(e){let a=function(e){let r=t.forwardRef((e,r)=>{let{children:a,...s}=e;if(t.isValidElement(a)){var l,n,d;let e,o,c=(o=(e=null==(n=Object.getOwnPropertyDescriptor((l=a).props,"ref"))?void 0:n.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(o=(e=null==(d=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,u=function(e,r){let t={...r};for(let a in r){let i=e[a],s=r[a];/^on[A-Z]/.test(a)?i&&s?t[a]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let a=s(...r);return i(...r),a}:i&&(t[a]=i):"style"===a?t[a]={...i,...s}:"className"===a&&(t[a]=[i,s].filter(Boolean).join(" "))}return{...e,...t}}(s,a.props);return a.type!==t.Fragment&&(u.ref=r?i(r,c):c),t.cloneElement(a,u)}return t.Children.count(a)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),s=t.forwardRef((e,i)=>{let{children:s,...l}=e,n=t.Children.toArray(s),d=n.find(o);if(d){let e=d.props.children,s=n.map(r=>r!==d?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,r.jsx)(a,{...l,ref:i,children:t.isValidElement(e)?t.cloneElement(e,void 0,s):null})}return(0,r.jsx)(a,{...l,ref:i,children:s})});return s.displayName="".concat(e,".Slot"),s}e.s(["Slot",()=>n,"createSlot",()=>l],88338),e.s(["composeRefs",()=>i,"useComposedRefs",()=>s],35952);var n=l("Slot"),d=Symbol("radix.slottable");function o(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var c=e.i(94323),u=e.i(36946);let h=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),f=t.forwardRef((e,t)=>{let{className:a,variant:i,size:s,asChild:l=!1,...d}=e;return(0,r.jsx)(l?n:"button",{className:(0,u.cn)(h({variant:i,size:s,className:a})),ref:t,...d})});f.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>s,"dispatchDiscreteCustomEvent",()=>l]);var r=e.i(46686),t=e.i(50321),a=e.i(88338),i=e.i(53379),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,a.createSlot)("Primitive.".concat(t)),l=r.forwardRef((e,r)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?s:t,{...l,ref:r})});return l.displayName="Primitive.".concat(t),{...e,[t]:l}},{});function l(e,r){e&&t.flushSync(()=>e.dispatchEvent(r))}},96487,e=>{"use strict";e.s(["Input",()=>i]);var r=e.i(53379),t=e.i(46686),a=e.i(36946);let i=t.forwardRef((e,t)=>{let{className:i,type:s,...l}=e;return(0,r.jsx)("input",{type:s,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:t,...l})});i.displayName="Input"},42226,e=>{"use strict";e.s(["default",()=>o]);var r=e.i(53379),t=e.i(46686),a=e.i(18125),i=e.i(30151),s=e.i(96487),l=e.i(92521),n=e.i(32668),d=e.i(70307);function o(e){let{teacher:o,mode:c}=e,u=(0,a.useRouter)(),[h,f]=(0,t.useState)(!1),[g,p]=(0,t.useState)(""),[x,m]=(0,t.useState)(""),[v,y]=(0,t.useState)(null),[b,j]=(0,t.useState)({firstName:"",lastName:"",email:"",phone:"",dateOfBirth:"",gender:"MALE",address:"",qualification:"",experience:"",joiningDate:"",salary:"",isActive:!0});(0,t.useEffect)(()=>{if(o){var e,r,t;j({firstName:o.firstName||"",lastName:o.lastName||"",email:o.email||"",phone:o.phone||"",dateOfBirth:o.dateOfBirth?new Date(o.dateOfBirth).toISOString().split("T")[0]:"",gender:o.gender||"MALE",address:o.address||"",qualification:o.qualification||"",experience:(null==(e=o.experience)?void 0:e.toString())||"",joiningDate:o.joiningDate?new Date(o.joiningDate).toISOString().split("T")[0]:"",salary:(null==(r=o.salary)?void 0:r.toString())||"",isActive:null==(t=o.isActive)||t})}},[o]);let N=async e=>{e.preventDefault(),f(!0),p(""),m(""),y(null);try{let e={...b,experience:b.experience?parseInt(b.experience):void 0,salary:b.salary?parseFloat(b.salary):void 0},r="create"===c?"/api/admin/teachers":"/api/admin/teachers/".concat(o.id),t=await fetch(r,{method:"create"===c?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to save teacher");m(a.message),"create"===c&&a.credentials&&y(a.credentials),"create"===c&&setTimeout(()=>{u.push("/admin/teachers")},2e3)}catch(e){p(e.message)}finally{f(!1)}},w=(e,r)=>{j(t=>({...t,[e]:r}))};return(0,r.jsxs)(n.Card,{className:"w-full max-w-2xl mx-auto",children:[(0,r.jsx)(n.CardHeader,{children:(0,r.jsx)(n.CardTitle,{children:"create"===c?"Add New Teacher":"Edit Teacher"})}),(0,r.jsx)(n.CardContent,{children:(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[g&&(0,r.jsx)(d.Alert,{variant:"destructive",children:(0,r.jsx)(d.AlertDescription,{children:g})}),x&&(0,r.jsx)(d.Alert,{children:(0,r.jsx)(d.AlertDescription,{children:x})}),v&&(0,r.jsx)(d.Alert,{children:(0,r.jsx)(d.AlertDescription,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"font-semibold",children:"Teacher created successfully!"}),(0,r.jsx)("p",{children:"Login credentials:"}),(0,r.jsxs)("div",{className:"bg-gray-100 p-3 rounded",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",v.email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Password:"})," ",v.password]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Please share these credentials with the teacher."})]})})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"firstName",children:"First Name *"}),(0,r.jsx)(s.Input,{id:"firstName",value:b.firstName,onChange:e=>w("firstName",e.target.value),required:!0,disabled:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"lastName",children:"Last Name *"}),(0,r.jsx)(s.Input,{id:"lastName",value:b.lastName,onChange:e=>w("lastName",e.target.value),required:!0,disabled:h})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"email",children:"Email *"}),(0,r.jsx)(s.Input,{id:"email",type:"email",value:b.email,onChange:e=>w("email",e.target.value),required:!0,disabled:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"phone",children:"Phone"}),(0,r.jsx)(s.Input,{id:"phone",value:b.phone,onChange:e=>w("phone",e.target.value),disabled:h})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,r.jsx)(s.Input,{id:"dateOfBirth",type:"date",value:b.dateOfBirth,onChange:e=>w("dateOfBirth",e.target.value),disabled:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"gender",children:"Gender"}),(0,r.jsxs)("select",{id:"gender",value:b.gender,onChange:e=>w("gender",e.target.value),disabled:h,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,r.jsx)("option",{value:"MALE",children:"Male"}),(0,r.jsx)("option",{value:"FEMALE",children:"Female"}),(0,r.jsx)("option",{value:"OTHER",children:"Other"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"address",children:"Address"}),(0,r.jsx)(s.Input,{id:"address",value:b.address,onChange:e=>w("address",e.target.value),disabled:h})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"qualification",children:"Qualification"}),(0,r.jsx)(s.Input,{id:"qualification",value:b.qualification,onChange:e=>w("qualification",e.target.value),disabled:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"experience",children:"Years of Experience"}),(0,r.jsx)(s.Input,{id:"experience",type:"number",min:"0",value:b.experience,onChange:e=>w("experience",e.target.value),disabled:h})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"joiningDate",children:"Joining Date"}),(0,r.jsx)(s.Input,{id:"joiningDate",type:"date",value:b.joiningDate,onChange:e=>w("joiningDate",e.target.value),disabled:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.Label,{htmlFor:"salary",children:"Salary"}),(0,r.jsx)(s.Input,{id:"salary",type:"number",min:"0",step:"0.01",value:b.salary,onChange:e=>w("salary",e.target.value),disabled:h})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{id:"isActive",type:"checkbox",checked:b.isActive,onChange:e=>w("isActive",e.target.checked),disabled:h,className:"rounded"}),(0,r.jsx)(l.Label,{htmlFor:"isActive",children:"Active"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(i.Button,{type:"button",variant:"outline",onClick:()=>u.back(),disabled:h,children:"Cancel"}),(0,r.jsx)(i.Button,{type:"submit",disabled:h,children:h?"Saving...":"create"===c?"Create Teacher":"Update Teacher"})]})]})})]})}},23200,e=>{"use strict";e.s(["default",()=>n]);var r=e.i(53379),t=e.i(46686),a=e.i(18125),i=e.i(30151),s=e.i(70307),l=e.i(42226);function n(e){let{params:n}=e,d=(0,a.useRouter)(),[o,c]=(0,t.useState)(null),[u,h]=(0,t.useState)(!0),[f,g]=(0,t.useState)(""),[p,x]=(0,t.useState)(null);return((0,t.useEffect)(()=>{(async()=>{try{let e=await n;x(e.id)}catch(e){g("Failed to resolve route parameters"),h(!1)}})()},[n]),(0,t.useEffect)(()=>{p&&(async()=>{try{let e=await fetch("/api/admin/teachers/".concat(p));if(!e.ok)throw Error("Failed to fetch teacher");let r=await e.json();c(r.teacher)}catch(e){g(e.message)}finally{h(!1)}})()},[p]),u)?(0,r.jsx)("div",{className:"flex justify-center p-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):f?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(s.Alert,{variant:"destructive",children:(0,r.jsx)(s.AlertDescription,{children:f})}),(0,r.jsx)(i.Button,{onClick:()=>d.push("/admin/teachers"),children:"Back to Teachers"})]}):o?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Teacher"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Update information for ",o.firstName," ",o.lastName]})]}),(0,r.jsx)(l.default,{teacher:o,mode:"edit"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(s.Alert,{children:(0,r.jsx)(s.AlertDescription,{children:"Teacher not found"})}),(0,r.jsx)(i.Button,{onClick:()=>d.push("/admin/teachers"),children:"Back to Teachers"})]})}}]);