(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,a)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>i,"CardHeader",()=>n,"CardTitle",()=>l]);var t=e.i(53379),a=e.i(46686),r=e.i(36946);let s=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let l=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});l.displayName="CardTitle";let i=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});i.displayName="CardDescription";let d=a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",a.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>u],30151);var t=e.i(53379),a=e.i(46686);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return e=>{let a=!1,s=t.map(t=>{let s=r(t,e);return a||"function"!=typeof s||(a=!0),s});if(a)return()=>{for(let e=0;e<s.length;e++){let a=s[e];"function"==typeof a?a():r(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.useCallback(s(...t),t)}function l(e){let r=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var l,i,d;let e,c,o=(c=(e=null==(i=Object.getOwnPropertyDescriptor((l=r).props,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,x=function(e,t){let a={...t};for(let r in t){let s=e[r],n=t[r];/^on[A-Z]/.test(r)?s&&n?a[r]=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];let r=n(...t);return s(...t),r}:s&&(a[r]=s):"style"===r?a[r]={...s,...n}:"className"===r&&(a[r]=[s,n].filter(Boolean).join(" "))}return{...e,...a}}(n,r.props);return r.type!==a.Fragment&&(x.ref=t?s(t,o):o),a.cloneElement(r,x)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=a.forwardRef((e,s)=>{let{children:n,...l}=e,i=a.Children.toArray(n),d=i.find(c);if(d){let e=d.props.children,n=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...l,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,t.jsx)(r,{...l,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>i,"createSlot",()=>l],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var i=l("Slot"),d=Symbol("radix.slottable");function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),x=e.i(36946);let m=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,a)=>{let{className:r,variant:s,size:n,asChild:l=!1,...d}=e;return(0,t.jsx)(l?i:"button",{className:(0,x.cn)(m({variant:s,size:n,className:r})),ref:a,...d})});u.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>l]);var t=e.i(46686),a=e.i(50321),r=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let n=(0,r.createSlot)("Primitive.".concat(a)),l=t.forwardRef((e,t)=>{let{asChild:r,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(r?n:a,{...l,ref:t})});return l.displayName="Primitive.".concat(a),{...e,[a]:l}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>l],4741);var t=e.i(46686);let a=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},r=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,a)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:x,...m}=e;return(0,t.createElement)("svg",{ref:a,...s,width:l,height:l,stroke:n,strokeWidth:d?24*Number(i)/Number(l):i,className:r("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(o)?o:[o]])}),l=(e,s)=>{let l=(0,t.forwardRef)((l,i)=>{let{className:d,...c}=l;return(0,t.createElement)(n,{ref:i,iconNode:s,className:r("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return l.displayName=a(e),l}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>r,"teacherNavigation",()=>a]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],a=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],r=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},92521,e=>{"use strict";e.s(["Label",()=>d],92521);var t=e.i(53379),a=e.i(46686),r=e.i(62521),s=a.forwardRef((e,a)=>(0,t.jsx)(r.Primitive.label,{...e,ref:a,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var n=e.i(94323),l=e.i(36946);let i=(0,n.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(s,{ref:a,className:(0,l.cn)(i(),r),...n})});d.displayName=s.displayName},21244,e=>{"use strict";e.s(["TrendingUp",()=>t],21244);let t=(0,e.i(4741).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},98325,e=>{"use strict";e.s(["Eye",()=>t],98325);let t=(0,e.i(4741).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94435,e=>{"use strict";e.s(["Printer",()=>t],94435);let t=(0,e.i(4741).default)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},32934,e=>{"use strict";e.s(["default",()=>f]);var t=e.i(53379),a=e.i(46686),r=e.i(32668),s=e.i(30151),n=e.i(92521),l=e.i(89559),i=e.i(96274),d=e.i(23178),c=e.i(21138),o=e.i(87523),x=e.i(10535),m=e.i(21244),u=e.i(94435),h=e.i(98325),p=e.i(84633);function f(){let[e,f]=(0,a.useState)([]),[g,j]=(0,a.useState)("all"),[y,N]=(0,a.useState)("all"),[v,w]=(0,a.useState)("all");(0,a.useEffect)(()=>{f([{id:"1",studentName:"John Doe",admissionNo:"STU001",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:425,percentage:85,grade:"A",rank:3,generatedAt:"2024-12-15",status:"GENERATED"},{id:"2",studentName:"Jane Smith",admissionNo:"STU002",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:380,percentage:76,grade:"B+",rank:8,generatedAt:"2024-12-15",status:"PUBLISHED"},{id:"3",studentName:"Mike Johnson",admissionNo:"STU003",className:"Grade 8",sectionName:"A",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:450,percentage:90,grade:"A+",rank:1,generatedAt:"2024-12-15",status:"GENERATED"}])},[]);let b=e=>{switch(e){case"GENERATED":return"bg-blue-100 text-blue-800";case"PUBLISHED":return"bg-green-100 text-green-800";case"PENDING":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},k={total:e.length,generated:e.filter(e=>"GENERATED"===e.status).length,published:e.filter(e=>"PUBLISHED"===e.status).length,pending:e.filter(e=>"PENDING"===e.status).length,averagePercentage:e.length>0?Math.round(e.reduce((e,t)=>e+t.percentage,0)/e.length):0};return(0,t.jsx)(l.default,{title:"Reports Management",navigation:p.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Reports Management"}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Generate and manage student report cards and academic reports"})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2",children:[(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full sm:w-auto",children:[(0,t.jsx)(c.BarChart3,{className:"w-4 h-4 mr-2"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Analytics"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Analytics Report"})]}),(0,t.jsxs)(s.Button,{className:"w-full sm:w-auto",children:[(0,t.jsx)(i.FileText,{className:"w-4 h-4 mr-2"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Generate"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Generate Reports"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Total Reports"}),(0,t.jsx)(i.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:k.total})})]},"total-reports"),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Generated"}),(0,t.jsx)(i.FileText,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:k.generated})})]},"generated-reports"),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Published"}),(0,t.jsx)(i.FileText,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k.published})})]},"published-reports"),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Pending"}),(0,t.jsx)(i.FileText,{className:"h-4 w-4 text-yellow-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:k.pending})})]},"pending-reports"),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(r.CardTitle,{className:"text-sm font-medium",children:"Avg Score"}),(0,t.jsx)(m.TrendingUp,{className:"h-4 w-4 text-purple-600"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[k.averagePercentage,"%"]})})]},"average-score")]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsxs)(r.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(i.FileText,{className:"w-5 h-5 mr-2"}),"Generate Report Cards"]}),(0,t.jsx)(r.CardDescription,{children:"Create report cards for all students in a class"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"term-select",children:"Select Term"}),(0,t.jsxs)("select",{id:"term-select",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Choose a term"}),(0,t.jsx)("option",{value:"term1",children:"Term 1"}),(0,t.jsx)("option",{value:"term2",children:"Term 2"}),(0,t.jsx)("option",{value:"term3",children:"Term 3"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"class-select",children:"Select Class"}),(0,t.jsxs)("select",{id:"class-select",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Choose a class"}),(0,t.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,t.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade10",children:"Grade 10"})]})]}),(0,t.jsxs)(s.Button,{className:"w-full",children:[(0,t.jsx)(i.FileText,{className:"w-4 h-4 mr-2"}),"Generate Reports"]})]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsxs)(r.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(c.BarChart3,{className:"w-5 h-5 mr-2"}),"Performance Analytics"]}),(0,t.jsx)(r.CardDescription,{children:"View detailed performance analytics"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(m.TrendingUp,{className:"w-4 h-4 mr-2"}),"Class Performance"]}),(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(x.Award,{className:"w-4 h-4 mr-2"}),"Subject Analysis"]}),(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(o.Users,{className:"w-4 h-4 mr-2"}),"Student Rankings"]})]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsxs)(r.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(d.Download,{className:"w-5 h-5 mr-2"}),"Export Reports"]}),(0,t.jsx)(r.CardDescription,{children:"Export reports in various formats"})]}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(d.Download,{className:"w-4 h-4 mr-2"}),"Export to Excel"]}),(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(i.FileText,{className:"w-4 h-4 mr-2"}),"Export to PDF"]}),(0,t.jsxs)(s.Button,{variant:"outline",className:"w-full",children:[(0,t.jsx)(u.Printer,{className:"w-4 h-4 mr-2"}),"Print Reports"]})]})})]})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsx)(r.CardHeader,{children:(0,t.jsx)(r.CardTitle,{children:"Filters"})}),(0,t.jsx)(r.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"term",children:"Term"}),(0,t.jsxs)("select",{id:"term",value:g,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Terms"}),(0,t.jsx)("option",{value:"term1",children:"Term 1"}),(0,t.jsx)("option",{value:"term2",children:"Term 2"}),(0,t.jsx)("option",{value:"term3",children:"Term 3"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"class",children:"Class"}),(0,t.jsxs)("select",{id:"class",value:y,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Classes"}),(0,t.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,t.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,t.jsx)("option",{value:"grade10",children:"Grade 10"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"status",children:"Status"}),(0,t.jsxs)("select",{id:"status",value:v,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Status"}),(0,t.jsx)("option",{value:"generated",children:"Generated"}),(0,t.jsx)("option",{value:"published",children:"Published"}),(0,t.jsx)("option",{value:"pending",children:"Pending"})]})]})]})})]}),(0,t.jsxs)(r.Card,{children:[(0,t.jsxs)(r.CardHeader,{children:[(0,t.jsx)(r.CardTitle,{children:"Report Cards"}),(0,t.jsx)(r.CardDescription,{children:"Generated report cards for students"})]}),(0,t.jsxs)(r.CardContent,{children:[(0,t.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Term"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rank"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.studentName.split(" ").map(e=>e[0]).join("")})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.studentName}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.admissionNo})]})]})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.className," - ",e.sectionName]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.termName}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.obtainedMarks,"/",e.totalMarks]}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.percentage,"%"]}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(C(e.grade)),children:e.grade})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.rank}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(b(e.status)),children:e.status})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(h.Eye,{className:"w-4 h-4"})}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(d.Download,{className:"w-4 h-4"})}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(u.Printer,{className:"w-4 h-4"})})]})})]},e.id))})]})}),(0,t.jsx)("div",{className:"lg:hidden space-y-4",children:e.map(e=>(0,t.jsx)(r.Card,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0",children:(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.studentName.split(" ").map(e=>e[0]).join("")})}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 truncate",children:e.studentName}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.admissionNo," • ",e.className," - ",e.sectionName]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(b(e.status)),children:e.status})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Term:"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.termName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Marks:"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[e.obtainedMarks,"/",e.totalMarks]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Percentage:"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[e.percentage,"%"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Grade:"}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(C(e.grade)),children:e.grade})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Rank:"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["#",e.rank]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"Generated:"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.generatedAt})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)(s.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(h.Eye,{className:"w-4 h-4 mr-1"}),"View"]}),(0,t.jsxs)(s.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(d.Download,{className:"w-4 h-4 mr-1"}),"Download"]}),(0,t.jsxs)(s.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(u.Printer,{className:"w-4 h-4 mr-1"}),"Print"]})]})]})},e.id))})]})]})]})})}}]);