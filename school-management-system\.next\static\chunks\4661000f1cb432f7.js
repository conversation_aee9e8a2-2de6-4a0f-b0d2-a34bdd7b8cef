(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>h],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,s=t.map(t=>{let s=a(t,e);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let e=0;e<s.length;e++){let r=s[e];"function"==typeof r?r():a(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(s(...t),t)}function i(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...n}=e;if(r.isValidElement(a)){var i,l,d;let e,c,o=(c=(e=null==(l=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,x=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=n(...t);return s(...t),a}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,a.props);return a.type!==r.Fragment&&(x.ref=t?s(t,o):o),r.cloneElement(a,x)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,s)=>{let{children:n,...i}=e,l=r.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...i,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,t.jsx)(a,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),x=e.i(36946);let m=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=r.forwardRef((e,r)=>{let{className:a,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,x.cn)(m({variant:s,size:n,className:a})),ref:r,...d})});h.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),a=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let n=(0,a.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?n:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,r)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:x,...m}=e;return(0,t.createElement)("svg",{ref:r,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:a("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(o)?o:[o]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...c}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:a("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return i.displayName=r(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>a,"teacherNavigation",()=>r]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],r=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],a=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},21244,e=>{"use strict";e.s(["TrendingUp",()=>t],21244);let t=(0,e.i(4741).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},98325,e=>{"use strict";e.s(["Eye",()=>t],98325);let t=(0,e.i(4741).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94435,e=>{"use strict";e.s(["Printer",()=>t],94435);let t=(0,e.i(4741).default)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},627,e=>{"use strict";e.s(["default",()=>u]);var t=e.i(53379),r=e.i(46686),a=e.i(69758),s=e.i(32668),n=e.i(30151),i=e.i(89559),l=e.i(84633),d=e.i(96274),c=e.i(23178),o=e.i(10535),x=e.i(21244),m=e.i(98325),h=e.i(94435);function u(){let{data:e}=(0,a.useSession)(),[u,f]=(0,r.useState)([]);(0,r.useEffect)(()=>{f([{id:"1",termName:"Term 1",academicYear:"2024-2025",totalMarks:500,obtainedMarks:425,percentage:85,grade:"A",rank:3,generatedAt:"2024-12-15",status:"PUBLISHED"},{id:"2",termName:"Term 2",academicYear:"2024-2025",totalMarks:500,obtainedMarks:380,percentage:76,grade:"B+",rank:8,generatedAt:"2024-12-15",status:"GENERATED"}])},[]);let g={total:u.length,average:u.length>0?Math.round(u.reduce((e,t)=>e+t.percentage,0)/u.length):0,highest:Math.max(...u.map(e=>e.percentage)),lowest:Math.min(...u.map(e=>e.percentage)),published:u.filter(e=>"PUBLISHED"===e.status).length,generated:u.filter(e=>"GENERATED"===e.status).length};return(0,t.jsx)(i.default,{title:"My Report Cards",navigation:l.studentNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"My Report Cards"}),(0,t.jsx)("p",{className:"text-gray-600",children:"View your academic performance reports"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(n.Button,{variant:"outline",children:[(0,t.jsx)(c.Download,{className:"w-4 h-4 mr-2"}),"Download All"]}),(0,t.jsxs)(n.Button,{children:[(0,t.jsx)(d.FileText,{className:"w-4 h-4 mr-2"}),"Request Report"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6",children:[(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Total Reports"}),(0,t.jsx)(d.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:g.total})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Average Score"}),(0,t.jsx)(x.TrendingUp,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[g.average,"%"]})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Highest Score"}),(0,t.jsx)(o.Award,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[g.highest,"%"]})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Lowest Score"}),(0,t.jsx)(x.TrendingUp,{className:"h-4 w-4 text-red-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[g.lowest,"%"]})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Published"}),(0,t.jsx)(d.FileText,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.published})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Generated"}),(0,t.jsx)(d.FileText,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:g.generated})})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(e=>(0,t.jsxs)(s.Card,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsx)(s.CardHeader,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center",children:[(0,t.jsx)(d.FileText,{className:"w-5 h-5 mr-2 text-blue-600"}),e.termName]}),(0,t.jsx)(s.CardDescription,{children:e.academicYear})]}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(e){case"GENERATED":return"bg-blue-100 text-blue-800";case"PUBLISHED":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(e.status)),children:e.status})]})}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Total Marks"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:e.totalMarks})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Obtained"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:e.obtainedMarks})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentage"}),(0,t.jsxs)("p",{className:"text-lg font-semibold text-blue-600",children:[e.percentage,"%"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Grade"}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 text-sm font-semibold rounded-full ".concat((e=>{switch(e){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.grade)),children:e.grade})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Class Rank"}),(0,t.jsxs)("p",{className:"text-lg font-semibold text-purple-600",children:["#",e.rank]})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Generated: ",new Date(e.generatedAt).toLocaleDateString()]})}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,t.jsxs)(n.Button,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(m.Eye,{className:"w-4 h-4 mr-1"}),"View"]}),(0,t.jsx)(n.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(c.Download,{className:"w-4 h-4"})}),(0,t.jsx)(n.Button,{variant:"outline",size:"sm",children:(0,t.jsx)(h.Printer,{className:"w-4 h-4"})})]})]})})]},e.id))}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsx)(s.CardTitle,{children:"Performance Summary"}),(0,t.jsx)(s.CardDescription,{children:"Overview of your academic performance across all terms"})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-3xl font-bold text-blue-600",children:[g.average,"%"]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Overall Average"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-3xl font-bold text-green-600",children:[g.highest,"%"]}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Best Performance"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-purple-600",children:g.total}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Terms Completed"})]})]})})]})]})})}}]);