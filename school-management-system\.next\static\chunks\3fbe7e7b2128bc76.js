(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>l,"CardHeader",()=>n,"CardTitle",()=>i]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",s),...n})});s.displayName="Card";let n=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});n.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});i.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",s),...n})});l.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...n})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>m],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,s=t.map(t=>{let s=a(t,e);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let e=0;e<s.length;e++){let r=s[e];"function"==typeof r?r():a(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(s(...t),t)}function i(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...n}=e;if(r.isValidElement(a)){var i,l,d;let e,o,c=(o=(e=null==(l=Object.getOwnPropertyDescriptor((i=a).props,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=null==(d=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=n(...t);return s(...t),a}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,a.props);return a.type!==r.Fragment&&(u.ref=t?s(t,c):c),r.cloneElement(a,u)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,s)=>{let{children:n,...i}=e,l=r.Children.toArray(n),d=l.find(o);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...i,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,t.jsx)(a,{...i,ref:s,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>l,"createSlot",()=>i],88338),e.s(["composeRefs",()=>s,"useComposedRefs",()=>n],35952);var l=i("Slot"),d=Symbol("radix.slottable");function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var c=e.i(94323),u=e.i(36946);let f=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=r.forwardRef((e,r)=>{let{className:a,variant:s,size:n,asChild:i=!1,...d}=e;return(0,t.jsx)(i?l:"button",{className:(0,u.cn)(f({variant:s,size:n,className:a})),ref:r,...d})});m.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(46686),r=e.i(50321),a=e.i(88338),s=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let n=(0,a.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?n:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>i],4741);var t=e.i(46686);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,r)=>{let{color:n="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...f}=e;return(0,t.createElement)("svg",{ref:r,...s,width:i,height:i,stroke:n,strokeWidth:d?24*Number(l)/Number(i):l,className:a("lucide",o),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(c)?c:[c]])}),i=(e,s)=>{let i=(0,t.forwardRef)((i,l)=>{let{className:d,...o}=i;return(0,t.createElement)(n,{ref:l,iconNode:s,className:a("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...o})});return i.displayName=r(e),i}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>s,"studentNavigation",()=>a,"teacherNavigation",()=>r]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],r=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],a=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function s(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},96487,e=>{"use strict";e.s(["Input",()=>s]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let s=r.forwardRef((e,r)=>{let{className:s,type:n,...i}=e;return(0,t.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});s.displayName="Input"},70307,e=>{"use strict";e.s(["Alert",()=>i,"AlertDescription",()=>l]);var t=e.i(53379),r=e.i(46686),a=e.i(94323),s=e.i(36946);let n=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),i=r.forwardRef((e,r)=>{let{className:a,variant:i,...l}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,s.cn)(n({variant:i}),a),...l})});i.displayName="Alert",r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("h5",{ref:r,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",a),...n})}).displayName="AlertTitle";let l=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",a),...n})});l.displayName="AlertDescription"},90285,e=>{"use strict";e.s(["Badge",()=>n]);var t=e.i(53379),r=e.i(94323),a=e.i(36946);let s=(0,r.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function n(e){let{className:r,variant:n,...i}=e;return(0,t.jsx)("div",{className:(0,a.cn)(s({variant:n}),r),...i})}},86900,e=>{"use strict";e.s(["default",()=>u]);var t=e.i(53379),r=e.i(46686),a=e.i(18125),s=e.i(30151),n=e.i(96487),i=e.i(90285),l=e.i(32668),d=e.i(70307),o=e.i(89559),c=e.i(84633);function u(){let e=(0,a.useRouter)(),[u,f]=(0,r.useState)([]),[m,h]=(0,r.useState)(!0),[g,x]=(0,r.useState)(""),[p,b]=(0,r.useState)({page:1,limit:10,total:0,totalPages:0}),[v,y]=(0,r.useState)(""),j=async()=>{try{h(!0);let e=new URLSearchParams({page:p.page.toString(),limit:p.limit.toString(),...v&&{search:v}}),t=await fetch("/api/admin/subjects?".concat(e));if(!t.ok)throw Error("Failed to fetch subjects");let r=await t.json();f(r.subjects),b(r.pagination)}catch(e){x(e.message)}finally{h(!1)}};(0,r.useEffect)(()=>{j()},[p.page,v]);let N=e=>{b(t=>({...t,page:e}))};return m?(0,t.jsx)(o.default,{title:"Subject Management",navigation:c.adminNavigation,children:(0,t.jsx)("div",{className:"flex justify-center p-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})})}):(0,t.jsx)(o.default,{title:"Subject Management",navigation:c.adminNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Subject Management"}),(0,t.jsx)(s.Button,{onClick:()=>e.push("/admin/subjects/new"),children:"Add New Subject"})]}),g&&(0,t.jsx)(d.Alert,{variant:"destructive",children:(0,t.jsx)(d.AlertDescription,{children:g})}),(0,t.jsxs)(l.Card,{children:[(0,t.jsx)(l.CardHeader,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)(l.CardTitle,{children:["Subjects (",p.total,")"]}),(0,t.jsx)("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:(0,t.jsx)(n.Input,{placeholder:"Search subjects...",value:v,onChange:e=>{y(e.target.value),b(e=>({...e,page:1}))},className:"w-full sm:w-64"})})]})}),(0,t.jsxs)(l.CardContent,{children:[0===u.length?(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No subjects found."})}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Subject Name"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Code"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Class"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Exams Count"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:u.map(r=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:r.name}),r.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:r.description})]})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(i.Badge,{variant:"outline",children:r.code})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)(i.Badge,{variant:"secondary",children:r.class.name})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsx)("span",{className:"font-medium",children:r._count.exams})}),(0,t.jsx)("td",{className:"p-2",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(s.Button,{size:"sm",variant:"outline",onClick:()=>e.push("/admin/subjects/".concat(r.id)),children:"View"}),(0,t.jsx)(s.Button,{size:"sm",variant:"outline",onClick:()=>e.push("/admin/subjects/".concat(r.id,"/edit")),children:"Edit"})]})})]},r.id))})]})}),p.totalPages>1&&(0,t.jsxs)("div",{className:"flex justify-between items-center mt-6",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(p.page-1)*p.limit+1," to"," ",Math.min(p.page*p.limit,p.total)," of"," ",p.total," subjects"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>N(p.page-1),disabled:p.page<=1,children:"Previous"}),(0,t.jsxs)("span",{className:"px-3 py-2 text-sm",children:["Page ",p.page," of ",p.totalPages]}),(0,t.jsx)(s.Button,{variant:"outline",size:"sm",onClick:()=>N(p.page+1),disabled:p.page>=p.totalPages,children:"Next"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(l.Card,{children:[(0,t.jsx)(l.CardHeader,{className:"pb-2",children:(0,t.jsx)(l.CardTitle,{className:"text-sm font-medium",children:"Total Subjects"})}),(0,t.jsx)(l.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:p.total})})]},"total-subjects"),(0,t.jsxs)(l.Card,{children:[(0,t.jsx)(l.CardHeader,{className:"pb-2",children:(0,t.jsx)(l.CardTitle,{className:"text-sm font-medium",children:"Total Classes"})}),(0,t.jsx)(l.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:new Set(u.map(e=>e.class.id)).size})})]},"total-classes-with-subjects"),(0,t.jsxs)(l.Card,{children:[(0,t.jsx)(l.CardHeader,{className:"pb-2",children:(0,t.jsx)(l.CardTitle,{className:"text-sm font-medium",children:"Total Exams"})}),(0,t.jsx)(l.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:u.reduce((e,t)=>e+t._count.exams,0)})})]},"total-exams")]})]})})}}]);