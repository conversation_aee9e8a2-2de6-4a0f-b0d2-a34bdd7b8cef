(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,18125,(e,t,s)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>a,"CardContent",()=>d,"CardDescription",()=>i,"CardHeader",()=>n,"CardTitle",()=>l]);var t=e.i(53379),s=e.i(46686),r=e.i(36946);let a=s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...n})});a.displayName="Card";let n=s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...n})});n.displayName="CardHeader";let l=s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",a),...n})});l.displayName="CardTitle";let i=s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-gray-600 dark:text-gray-400",a),...n})});i.displayName="CardDescription";let d=s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",a),...n})});d.displayName="CardContent",s.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>u],30151);var t=e.i(53379),s=e.i(46686);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return e=>{let s=!1,a=t.map(t=>{let a=r(t,e);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let e=0;e<a.length;e++){let s=a[e];"function"==typeof s?s():r(t[e],null)}}}}function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return s.useCallback(a(...t),t)}function l(e){let r=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var l,i,d;let e,c,o=(c=(e=null==(i=Object.getOwnPropertyDescriptor((l=r).props,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(c=(e=null==(d=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,m=function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let r=n(...t);return a(...t),r}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(n,r.props);return r.type!==s.Fragment&&(m.ref=t?a(t,o):o),s.cloneElement(r,m)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=s.forwardRef((e,a)=>{let{children:n,...l}=e,i=s.Children.toArray(n),d=i.find(c);if(d){let e=d.props.children,n=i.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,t.jsx)(r,{...l,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,t.jsx)(r,{...l,ref:a,children:n})});return n.displayName="".concat(e,".Slot"),n}e.s(["Slot",()=>i,"createSlot",()=>l],88338),e.s(["composeRefs",()=>a,"useComposedRefs",()=>n],35952);var i=l("Slot"),d=Symbol("radix.slottable");function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var o=e.i(94323),m=e.i(36946);let x=(0,o.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,s)=>{let{className:r,variant:a,size:n,asChild:l=!1,...d}=e;return(0,t.jsx)(l?i:"button",{className:(0,m.cn)(x({variant:a,size:n,className:r})),ref:s,...d})});u.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>l]);var t=e.i(46686),s=e.i(50321),r=e.i(88338),a=e.i(53379),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let n=(0,r.createSlot)("Primitive.".concat(s)),l=t.forwardRef((e,t)=>{let{asChild:r,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(r?n:s,{...l,ref:t})});return l.displayName="Primitive.".concat(s),{...e,[s]:l}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},18498,e=>{"use strict";e.s(["Home",()=>t],18498);let t=(0,e.i(4741).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},4741,e=>{"use strict";e.s(["default",()=>l],4741);var t=e.i(46686);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},r=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,t.forwardRef)((e,s)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:m,...x}=e;return(0,t.createElement)("svg",{ref:s,...a,width:l,height:l,stroke:n,strokeWidth:d?24*Number(i)/Number(l):i,className:r("lucide",c),...!o&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[s,r]=e;return(0,t.createElement)(s,r)}),...Array.isArray(o)?o:[o]])}),l=(e,a)=>{let l=(0,t.forwardRef)((l,i)=>{let{className:d,...c}=l;return(0,t.createElement)(n,{ref:i,iconNode:a,className:r("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),d),...c})});return l.displayName=s(e),l}},80873,e=>{"use strict";e.s(["User",()=>t],80873);let t=(0,e.i(4741).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},84633,e=>{"use strict";e.s(["adminNavigation",()=>t,"getRoleDashboardUrl",()=>a,"studentNavigation",()=>r,"teacherNavigation",()=>s]);let t=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],s=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],r=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function a(e){switch(e){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},44670,e=>{"use strict";e.s(["Loader2",()=>t],44670);let t=(0,e.i(4741).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},21244,e=>{"use strict";e.s(["TrendingUp",()=>t],21244);let t=(0,e.i(4741).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},9422,e=>{"use strict";e.s(["Clock",()=>t],9422);let t=(0,e.i(4741).default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},63145,e=>{"use strict";e.s(["default",()=>h]);var t=e.i(53379),s=e.i(69758),r=e.i(46686),a=e.i(32668),n=e.i(89559),l=e.i(84633),i=e.i(21244),d=e.i(10535),c=e.i(66505),o=e.i(96274),m=e.i(9422),x=e.i(44670),u=e.i(69556);function h(){var e;let{data:h}=(0,s.useSession)(),[f,p]=(0,r.useState)(null),[g,j]=(0,r.useState)(!0),[y,b]=(0,r.useState)(null);(0,r.useEffect)(()=>{var e;let t=async()=>{try{let e=await fetch("/api/student/dashboard/stats");if(!e.ok)throw Error("Failed to fetch dashboard statistics");let t=await e.json();p(t)}catch(e){b(e instanceof Error?e.message:"An error occurred"),console.error("Error fetching student dashboard stats:",e)}finally{j(!1)}};(null==h||null==(e=h.user)?void 0:e.role)==="STUDENT"&&t()},[h]);let N=[{title:"View Marks",description:"Check your latest grades",icon:d.Award,href:"/student/marks",color:"bg-blue-500"},{title:"Attendance History",description:"View your attendance record",icon:c.Calendar,href:"/student/attendance",color:"bg-green-500"},{title:"Download Report",description:"Get your report card",icon:o.FileText,href:"/student/reports",color:"bg-purple-500"},{title:"View Schedule",description:"Check your timetable",icon:m.Clock,href:"/student/schedule",color:"bg-orange-500"}];return(0,t.jsx)(n.default,{title:"Student Dashboard",navigation:l.studentNavigation,children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:["Welcome back, ",(null==h||null==(e=h.user)?void 0:e.firstName)||"Student","!"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Here's your academic overview and progress summary."})]}),g&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(x.Loader2,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading dashboard statistics..."})]}),y&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("p",{className:"text-red-800",children:["Error loading dashboard: ",y]})}),f&&(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Personal Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Class:"}),(0,t.jsx)("span",{className:"font-medium",children:f.currentClass})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Section:"}),(0,t.jsx)("span",{className:"font-medium",children:f.currentSection})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Roll Number:"}),(0,t.jsx)("span",{className:"font-medium",children:f.rollNumber})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Subjects:"}),(0,t.jsx)("span",{className:"font-medium",children:f.totalSubjects})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Attendance Rate:"}),(0,t.jsxs)("span",{className:"font-medium text-green-600",children:[f.attendanceRate,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Average Marks:"}),(0,t.jsxs)("span",{className:"font-medium text-blue-600",children:[f.averageMarks,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Upcoming Exams:"}),(0,t.jsx)("span",{className:"font-medium",children:f.upcomingExams})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Records:"}),(0,t.jsxs)("span",{className:"font-medium",children:[f.totalMarksRecords," marks, ",f.totalAttendanceRecords," attendance"]})]})]})]})]}),f&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Attendance"}),(0,t.jsx)(i.TrendingUp,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[f.attendanceRate,"%"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last 30 days"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Average Marks"}),(0,t.jsx)(d.Award,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[f.averageMarks,"%"]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[f.totalMarksRecords," exams taken"]})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Total Subjects"}),(0,t.jsx)(u.BookOpen,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:f.totalSubjects}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Current semester"})]})]}),(0,t.jsxs)(a.Card,{children:[(0,t.jsxs)(a.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.CardTitle,{className:"text-sm font-medium",children:"Upcoming Exams"}),(0,t.jsx)(c.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.CardContent,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:f.upcomingExams}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scheduled exams"})]})]})]}),f&&f.recentMarks.length>0&&(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Marks"}),(0,t.jsx)("div",{className:"space-y-3",children:f.recentMarks.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.subject}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.examName})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Score"}),(0,t.jsxs)("p",{className:"font-medium text-blue-600",children:[e.obtainedMarks,"/",e.maxMarks]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Percentage"}),(0,t.jsxs)("p",{className:"font-medium text-green-600",children:[e.percentage,"%"]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Date"}),(0,t.jsx)("p",{className:"font-medium text-purple-600",children:new Date(e.date).toLocaleDateString()})]})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:N.map(e=>(0,t.jsx)(a.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsx)(a.CardContent,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg ".concat(e.color),children:(0,t.jsx)(e.icon,{className:"h-5 w-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.title}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]})]})})},e.title))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Activities"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Marks uploaded"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Mathematics - Unit Test 1: 92%"})]}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"2 days ago"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Attendance marked"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Present in all classes today"})]}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"1 day ago"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Report card generated"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Term 1 report available for download"})]}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"1 week ago"})]})]})]})]})})}}]);