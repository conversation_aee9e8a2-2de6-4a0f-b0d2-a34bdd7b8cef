(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,92521,e=>{"use strict";e.s(["Label",()=>d],92521);var t=e.i(53379),r=e.i(46686),a=e.i(62521),l=r.forwardRef((e,r)=>(0,t.jsx)(a.Primitive.label,{...e,ref:r,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=e.i(94323),n=e.i(36946);let i=(0,s.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l,{ref:r,className:(0,n.cn)(i(),a),...s})});d.displayName=l.displayName},70307,e=>{"use strict";e.s(["Alert",()=>n,"AlertDescription",()=>i]);var t=e.i(53379),r=e.i(46686),a=e.i(94323),l=e.i(36946);let s=(0,a.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),n=r.forwardRef((e,r)=>{let{className:a,variant:n,...i}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(s({variant:n}),a),...i})});n.displayName="Alert",r.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let i=r.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",a),...s})});i.displayName="AlertDescription"},18125,(e,t,r)=>{t.exports=e.r(9885)},32668,e=>{"use strict";e.s(["Card",()=>l,"CardContent",()=>d,"CardDescription",()=>i,"CardHeader",()=>s,"CardTitle",()=>n]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let l=r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",l),...s})});l.displayName="Card";let s=r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",l),...s})});s.displayName="CardHeader";let n=r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",l),...s})});n.displayName="CardTitle";let i=r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600 dark:text-gray-400",l),...s})});i.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",l),...s})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:l,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",l),...s})}).displayName="CardFooter"},30151,35952,88338,e=>{"use strict";e.s(["Button",()=>g],30151);var t=e.i(53379),r=e.i(46686);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return e=>{let r=!1,l=t.map(t=>{let l=a(t,e);return r||"function"!=typeof l||(r=!0),l});if(r)return()=>{for(let e=0;e<l.length;e++){let r=l[e];"function"==typeof r?r():a(t[e],null)}}}}function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return r.useCallback(l(...t),t)}function n(e){let a=function(e){let t=r.forwardRef((e,t)=>{let{children:a,...s}=e;if(r.isValidElement(a)){var n,i,d;let e,o,c=(o=(e=null==(i=Object.getOwnPropertyDescriptor((n=a).props,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=null==(d=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:d.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref,u=function(e,t){let r={...t};for(let a in t){let l=e[a],s=t[a];/^on[A-Z]/.test(a)?l&&s?r[a]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=s(...t);return l(...t),a}:l&&(r[a]=l):"style"===a?r[a]={...l,...s}:"className"===a&&(r[a]=[l,s].filter(Boolean).join(" "))}return{...e,...r}}(s,a.props);return a.type!==r.Fragment&&(u.ref=t?l(t,c):c),r.cloneElement(a,u)}return r.Children.count(a)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),s=r.forwardRef((e,l)=>{let{children:s,...n}=e,i=r.Children.toArray(s),d=i.find(o);if(d){let e=d.props.children,s=i.map(t=>t!==d?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,t.jsx)(a,{...n,ref:l,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,t.jsx)(a,{...n,ref:l,children:s})});return s.displayName="".concat(e,".Slot"),s}e.s(["Slot",()=>i,"createSlot",()=>n],88338),e.s(["composeRefs",()=>l,"useComposedRefs",()=>s],35952);var i=n("Slot"),d=Symbol("radix.slottable");function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var c=e.i(94323),u=e.i(36946);let f=(0,c.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),g=r.forwardRef((e,r)=>{let{className:a,variant:l,size:s,asChild:n=!1,...d}=e;return(0,t.jsx)(n?i:"button",{className:(0,u.cn)(f({variant:l,size:s,className:a})),ref:r,...d})});g.displayName="Button"},62521,e=>{"use strict";e.s(["Primitive",()=>s,"dispatchDiscreteCustomEvent",()=>n]);var t=e.i(46686),r=e.i(50321),a=e.i(88338),l=e.i(53379),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let s=(0,a.createSlot)("Primitive.".concat(r)),n=t.forwardRef((e,t)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?s:r,{...n,ref:t})});return n.displayName="Primitive.".concat(r),{...e,[r]:n}},{});function n(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},96487,e=>{"use strict";e.s(["Input",()=>l]);var t=e.i(53379),r=e.i(46686),a=e.i(36946);let l=r.forwardRef((e,r)=>{let{className:l,type:s,...n}=e;return(0,t.jsx)("input",{type:s,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l),ref:r,...n})});l.displayName="Input"},37848,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(53379),r=e.i(46686),a=e.i(18125),l=e.i(30151),s=e.i(96487),n=e.i(92521),i=e.i(32668),d=e.i(70307);function o(e){var o;let{classData:c,date:u=new Date().toISOString().split("T")[0]}=e,f=(0,a.useRouter)(),[g,m]=(0,r.useState)(!1),[p,x]=(0,r.useState)(""),[v,b]=(0,r.useState)(""),[y,h]=(0,r.useState)(u),[j,N]=(0,r.useState)(c||null),[w,k]=(0,r.useState)([]),[C,S]=(0,r.useState)({});(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/classes");if(e.ok){let t=await e.json();k(t.classes)}}catch(e){console.error("Error fetching classes:",e)}})()},[]),(0,r.useEffect)(()=>{if(j&&j.students){let e={};j.students.forEach(t=>{e[t.id]="PRESENT"}),S(e)}},[j]);let R=async e=>{if(e.preventDefault(),!j)return void x("Please select a class");m(!0),x(""),b("");try{let e=Object.entries(C).map(e=>{let[t,r]=e;return{studentId:parseInt(t),status:r}}),t={classId:j.id,date:y,attendanceRecords:e},r=await fetch("/api/teacher/attendance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await r.json();if(!r.ok)throw Error(a.error||"Failed to mark attendance");b(a.message),setTimeout(()=>{f.push("/teacher/attendance")},2e3)}catch(e){x(e.message)}finally{m(!1)}};return(0,t.jsxs)(i.Card,{className:"w-full max-w-4xl mx-auto",children:[(0,t.jsx)(i.CardHeader,{children:(0,t.jsx)(i.CardTitle,{children:"Mark Attendance"})}),(0,t.jsx)(i.CardContent,{children:(0,t.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[p&&(0,t.jsx)(d.Alert,{variant:"destructive",children:(0,t.jsx)(d.AlertDescription,{children:p})}),v&&(0,t.jsx)(d.Alert,{children:(0,t.jsx)(d.AlertDescription,{children:v})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"class",children:"Class *"}),(0,t.jsxs)("select",{id:"class",value:(null==j?void 0:j.id)||"",onChange:e=>{let t=parseInt(e.target.value);N(w.find(e=>e.id===t)||null)},required:!0,disabled:g,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Select Class"}),w.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.name," ",e.section.name]},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.Label,{htmlFor:"date",children:"Date *"}),(0,t.jsx)(s.Input,{id:"date",type:"date",value:y,onChange:e=>h(e.target.value),required:!0,disabled:g})]})]}),j&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Students in ",j.name," ",j.section.name]}),(0,t.jsx)("div",{className:"space-y-3",children:j.students.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[e.firstName," ",e.lastName]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Roll No: ",e.rollNumber]})]}),(0,t.jsx)("div",{className:"flex gap-2",children:["PRESENT","ABSENT","LATE","HALF_DAY"].map(r=>(0,t.jsx)("button",{type:"button",onClick:()=>{var t;return t=e.id,void S(e=>({...e,[t]:r}))},className:"px-3 py-1 rounded text-sm font-medium transition-colors ".concat(C[e.id]===r?(e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"HALF_DAY":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}})(r):"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:r},r))})]},e.id))}),(0,t.jsxs)("div",{className:"mt-6 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Total Students: ",(null==(o=j.students)?void 0:o.length)||0]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.Button,{type:"button",variant:"outline",onClick:()=>{var e;let t={};null==(e=j.students)||e.forEach(e=>{t[e.id]="PRESENT"}),S(t)},disabled:g,children:"Mark All Present"}),(0,t.jsx)(l.Button,{type:"button",variant:"outline",onClick:()=>{var e;let t={};null==(e=j.students)||e.forEach(e=>{t[e.id]="ABSENT"}),S(t)},disabled:g,children:"Mark All Absent"})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,t.jsx)(l.Button,{type:"button",variant:"outline",onClick:()=>f.back(),disabled:g,children:"Cancel"}),(0,t.jsx)(l.Button,{type:"submit",disabled:g||!j,children:g?"Saving...":"Mark Attendance"})]})]})})]})}}]);