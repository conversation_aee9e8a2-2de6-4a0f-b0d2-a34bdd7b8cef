{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,sLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,4TAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,8UAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,4TAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,8JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline: \"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,8UAAC;QAAI,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/classes/class-table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\n\r\ninterface Class {\r\n  id: number;\r\n  name: string;\r\n  capacity?: number;\r\n  academicYear?: string;\r\n  isActive: boolean;\r\n  section: {\r\n    id: number;\r\n    name: string;\r\n  };\r\n  teacher?: {\r\n    id: number;\r\n    firstName: string;\r\n    lastName: string;\r\n    email: string;\r\n  };\r\n  _count: {\r\n    students: number;\r\n  };\r\n}\r\n\r\ninterface ClassTableProps {\r\n  classes: Class[];\r\n  pagination: {\r\n    page: number;\r\n    limit: number;\r\n    total: number;\r\n    totalPages: number;\r\n  };\r\n  onPageChange: (page: number) => void;\r\n  onSearch: (search: string) => void;\r\n  onFilter: (filter: string) => void;\r\n  loading?: boolean;\r\n}\r\n\r\nexport default function ClassTable({\r\n  classes,\r\n  pagination,\r\n  onPageChange,\r\n  onSearch,\r\n  onFilter,\r\n  loading = false,\r\n}: ClassTableProps) {\r\n  const router = useRouter();\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filterActive, setFilterActive] = useState<string>('all');\r\n  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearchTerm(value);\r\n    onSearch(value);\r\n  };\r\n\r\n  const handleFilter = (value: string) => {\r\n    setFilterActive(value);\r\n    onFilter(value);\r\n  };\r\n\r\n  const handleDelete = async (classId: number) => {\r\n    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {\r\n      return;\r\n    }\r\n\r\n    setDeleteLoading(classId);\r\n    try {\r\n      const response = await fetch(`/api/admin/classes/${classId}`, {\r\n        method: 'DELETE',\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const data = await response.json();\r\n        throw new Error(data.error || 'Failed to delete class');\r\n      }\r\n\r\n      // Refresh the page to show updated data\r\n      window.location.reload();\r\n    } catch (error: any) {\r\n      alert(error.message);\r\n    } finally {\r\n      setDeleteLoading(null);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex justify-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n          <CardTitle>Classes ({pagination.total})</CardTitle>\r\n          <div className=\"flex flex-col sm:flex-row gap-2 w-full sm:w-auto\">\r\n            <Input\r\n              placeholder=\"Search classes...\"\r\n              value={searchTerm}\r\n              onChange={(e) => handleSearch(e.target.value)}\r\n              className=\"w-full sm:w-64\"\r\n            />\r\n            <select\r\n              value={filterActive}\r\n              onChange={(e) => handleFilter(e.target.value)}\r\n              className=\"w-full sm:w-auto p-2 border border-gray-300 rounded-md\"\r\n            >\r\n              <option value=\"all\">All Classes</option>\r\n              <option value=\"true\">Active Only</option>\r\n              <option value=\"false\">Inactive Only</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent>\r\n        {classes.length === 0 ? (\r\n          <div className=\"text-center py-8\">\r\n            <p className=\"text-gray-500\">No classes found.</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"w-full\">\r\n              <thead>\r\n                <tr className=\"border-b\">\r\n                  <th className=\"text-left p-2\">Class Name</th>\r\n                  <th className=\"text-left p-2\">Section</th>\r\n                  <th className=\"text-left p-2\">Teacher</th>\r\n                  <th className=\"text-left p-2\">Students</th>\r\n                  <th className=\"text-left p-2\">Capacity</th>\r\n                  <th className=\"text-left p-2\">Academic Year</th>\r\n                  <th className=\"text-left p-2\">Status</th>\r\n                  <th className=\"text-left p-2\">Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {classes.map((classItem) => (\r\n                  <tr key={classItem.id} className=\"border-b hover:bg-gray-50\">\r\n                    <td className=\"p-2\">\r\n                      <div className=\"font-medium\">{classItem.name}</div>\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      <Badge variant=\"secondary\">{classItem.section.name}</Badge>\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      {classItem.teacher ? (\r\n                        <div>\r\n                          <div className=\"font-medium\">\r\n                            {classItem.teacher.firstName} {classItem.teacher.lastName}\r\n                          </div>\r\n                          <div className=\"text-sm text-gray-500\">{classItem.teacher.email}</div>\r\n                        </div>\r\n                      ) : (\r\n                        <span className=\"text-gray-500 text-sm\">Not assigned</span>\r\n                      )}\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <span className=\"font-medium\">{classItem._count.students}</span>\r\n                        {classItem.capacity && (\r\n                          <span className=\"text-gray-500\">/ {classItem.capacity}</span>\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      {classItem.capacity || '-'}\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      {classItem.academicYear || '-'}\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      <Badge variant={classItem.isActive ? 'default' : 'secondary'}>\r\n                        {classItem.isActive ? 'Active' : 'Inactive'}\r\n                      </Badge>\r\n                    </td>\r\n                    <td className=\"p-2\">\r\n                      <div className=\"flex gap-2\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/admin/classes/${classItem.id}`)}\r\n                        >\r\n                          View\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={() => router.push(`/admin/classes/${classItem.id}/edit`)}\r\n                        >\r\n                          Edit\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"destructive\"\r\n                          onClick={() => handleDelete(classItem.id)}\r\n                          disabled={deleteLoading === classItem.id}\r\n                        >\r\n                          {deleteLoading === classItem.id ? 'Deleting...' : 'Delete'}\r\n                        </Button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )}\r\n\r\n        {/* Pagination */}\r\n        {pagination.totalPages > 1 && (\r\n          <div className=\"flex justify-between items-center mt-6\">\r\n            <div className=\"text-sm text-gray-600\">\r\n              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}\r\n              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}\r\n              {pagination.total} classes\r\n            </div>\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => onPageChange(pagination.page - 1)}\r\n                disabled={pagination.page <= 1}\r\n              >\r\n                Previous\r\n              </Button>\r\n              <span className=\"px-3 py-2 text-sm\">\r\n                Page {pagination.page} of {pagination.totalPages}\r\n              </span>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => onPageChange(pagination.page + 1)}\r\n                disabled={pagination.page >= pagination.totalPages}\r\n              >\r\n                Next\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4Ce,SAAS,WAAW,KAOjB;QAPiB,EACjC,OAAO,EACP,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACC,GAPiB;;IAQjC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0TAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0TAAQ,EAAS;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,0TAAQ,EAAgB;IAElE,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,8EAA8E;YACzF;QACF;QAEA,iBAAiB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAA6B,OAAR,UAAW;gBAC5D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,wCAAwC;YACxC,OAAO,QAAQ,CAAC,MAAM;QACxB,EAAE,OAAO,OAAY;YACnB,MAAM,MAAM,OAAO;QACrB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8UAAC,6KAAI;sBACH,cAAA,8UAAC,oLAAW;gBAAC,WAAU;0BACrB,cAAA,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,8UAAC,6KAAI;;0BACH,8UAAC,mLAAU;0BACT,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,kLAAS;;gCAAC;gCAAU,WAAW,KAAK;gCAAC;;;;;;;sCACtC,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,+KAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;;;;;8CAEZ,8UAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,8UAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8UAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8UAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK9B,8UAAC,oLAAW;;oBACT,QAAQ,MAAM,KAAK,kBAClB,8UAAC;wBAAI,WAAU;kCACb,cAAA,8UAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;6CAG/B,8UAAC;wBAAI,WAAU;kCACb,cAAA,8UAAC;4BAAM,WAAU;;8CACf,8UAAC;8CACC,cAAA,8UAAC;wCAAG,WAAU;;0DACZ,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGlC,8UAAC;8CACE,QAAQ,GAAG,CAAC,CAAC,0BACZ,8UAAC;4CAAsB,WAAU;;8DAC/B,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC;wDAAI,WAAU;kEAAe,UAAU,IAAI;;;;;;;;;;;8DAE9C,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC,+KAAK;wDAAC,SAAQ;kEAAa,UAAU,OAAO,CAAC,IAAI;;;;;;;;;;;8DAEpD,8UAAC;oDAAG,WAAU;8DACX,UAAU,OAAO,iBAChB,8UAAC;;0EACC,8UAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS;oEAAC;oEAAE,UAAU,OAAO,CAAC,QAAQ;;;;;;;0EAE3D,8UAAC;gEAAI,WAAU;0EAAyB,UAAU,OAAO,CAAC,KAAK;;;;;;;;;;;6EAGjE,8UAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;8DAG5C,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAK,WAAU;0EAAe,UAAU,MAAM,CAAC,QAAQ;;;;;;4DACvD,UAAU,QAAQ,kBACjB,8UAAC;gEAAK,WAAU;;oEAAgB;oEAAG,UAAU,QAAQ;;;;;;;;;;;;;;;;;;8DAI3D,8UAAC;oDAAG,WAAU;8DACX,UAAU,QAAQ,IAAI;;;;;;8DAEzB,8UAAC;oDAAG,WAAU;8DACX,UAAU,YAAY,IAAI;;;;;;8DAE7B,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC,+KAAK;wDAAC,SAAS,UAAU,QAAQ,GAAG,YAAY;kEAC9C,UAAU,QAAQ,GAAG,WAAW;;;;;;;;;;;8DAGrC,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,iLAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,kBAA8B,OAAb,UAAU,EAAE;0EAC1D;;;;;;0EAGD,8UAAC,iLAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,kBAA8B,OAAb,UAAU,EAAE,EAAC;0EAC3D;;;;;;0EAGD,8UAAC,iLAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,aAAa,UAAU,EAAE;gEACxC,UAAU,kBAAkB,UAAU,EAAE;0EAEvC,kBAAkB,UAAU,EAAE,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;2CA5DjD,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;oBAwE9B,WAAW,UAAU,GAAG,mBACvB,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;oCAAwB;oCAC3B,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;oCAAE;oCAAI;oCAC3D,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;oCAAE;oCAAI;oCACnE,WAAW,KAAK;oCAAC;;;;;;;0CAEpB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,WAAW,IAAI,GAAG;wCAC9C,UAAU,WAAW,IAAI,IAAI;kDAC9B;;;;;;kDAGD,8UAAC;wCAAK,WAAU;;4CAAoB;4CAC5B,WAAW,IAAI;4CAAC;4CAAK,WAAW,UAAU;;;;;;;kDAElD,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,WAAW,IAAI,GAAG;wCAC9C,UAAU,WAAW,IAAI,IAAI,WAAW,UAAU;kDACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnNwB;;QAQP,mSAAS;;;KARF", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,gUAA0B;AAE/C,MAAM,sBAAsB,mUAA6B;AAEzD,MAAM,oBAAoB,iUAA2B;AAErD,MAAM,qBAAqB,kUAA4B;AAEvD,MAAM,kBAAkB,+TAAyB;AAEjD,MAAM,yBAAyB,sUAAgC;AAE/D,MAAM,uCAAyB,4TAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8UAAC,+VAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,4TAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,4TAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,8UAAC,kUAA4B;kBAC3B,cAAA,8UAAC,mUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,8JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,4TAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,gUAA0B;QACzB,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gUAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,4TAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,8UAAC,wUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,sUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,wUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,4TAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,yUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,4TAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,iUAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,iUAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,4TAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,qMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,8UAAC,iMAAY;;0BACX,8UAAC,wMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8UAAC,iLAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;sCAE/D,8UAAC,gUAAG;4BAAC,WAAU;;;;;;sCACf,8UAAC,mUAAI;4BAAC,WAAU;;;;;;sCAChB,8UAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8UAAC,wMAAmB;gBAAC,OAAM;;kCACzB,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,gUAAG;gCAAC,WAAU;;;;;;0CACf,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,4UAAO;gCAAC,WAAU;;;;;;0CACnB,8UAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAxCgB;;QACoC,qMAAQ;;;KAD5C", "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,mUAAI;IACJ,QAAA,yUAAM;IACN,UAAA,+UAAQ;IACR,OAAA,sUAAK;IACL,UAAA,mVAAQ;IACR,eAAA,kWAAa;IACb,UAAA,mVAAQ;IACR,WAAA,wVAAS;IACT,UAAA,+UAAQ;IACR,MAAA,oUAAI;IACJ,UAAA,+UAAQ;IACR,MAAA,mUAAI;IACJ,MAAA,mUAAI;IACJ,MAAA,4UAAI;IACJ,eAAA,kWAAa;IACb,OAAA,sUAAK;AACP;AAEe,SAAS,gBAAgB,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB,GAArD;QAuHnB,eAA2B,gBAG3B,oBAAA;;IAzHnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,0SAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,oUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,8UAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,8UAAC,0TAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8UAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,8UAAC,iLAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,8UAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,AAAC,UAAmB,OAAV,KAAK,IAAI;;;;;gCAY9B;;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;0BACb,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,yUAAM;oCAAC,WAAU;;;;;;8CAClB,8UAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,8UAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,8UAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,8UAAC,iLAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,8UAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,AAAC,WAAoB,OAAV,KAAK,IAAI;;;;;4BAS/B;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8UAAC,mUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC,yUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8UAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,+LAAW;;;;;kDAEZ,8UAAC,iLAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAE,WAAU;;gEACV,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS;gEAAC;gEAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;;;;;;;sEAErD,8UAAC;4DAAE,WAAU;sEACV,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,WAAW;;;;;;;;;;;;8DAGrC,8UAAC;oDAAI,WAAU;8DACb,cAAA,8UAAC,iLAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,8UAAC,6UAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8UAAC;wBAAK,WAAU;kCACd,cAAA,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzJwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 2068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/classes/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport ClassTable from '@/components/classes/class-table';\nimport DashboardLayout from '@/components/layout/dashboard-layout';\nimport { adminNavigation } from '@/lib/navigation';\nimport {\n  Users,\n  GraduationCap,\n  BookOpen,\n  FileText,\n  Calendar,\n  BarChart3,\n  Settings,\n  UserPlus,\n  ClipboardList,\n  Award\n} from 'lucide-react';\n\ninterface Class {\n  id: number;\n  name: string;\n  capacity?: number;\n  academicYear?: string;\n  isActive: boolean;\n  section: {\n    id: number;\n    name: string;\n  };\n  teacher?: {\n    id: number;\n    firstName: string;\n    lastName: string;\n    email: string;\n  };\n  _count: {\n    students: number;\n  };\n}\n\nexport default function ClassesPage() {\n  const router = useRouter();\n  const [classes, setClasses] = useState<Class[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0,\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterActive, setFilterActive] = useState('all');\n\n  const fetchClasses = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n        ...(searchTerm && { search: searchTerm }),\n        ...(filterActive !== 'all' && { isActive: filterActive }),\n      });\n\n      const response = await fetch(`/api/admin/classes?${params}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch classes');\n      }\n\n      const data = await response.json();\n      setClasses(data.classes);\n      setPagination(data.pagination);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchClasses();\n  }, [pagination.page, searchTerm, filterActive]);\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }));\n  };\n\n  const handleSearch = (search: string) => {\n    setSearchTerm(search);\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  const handleFilter = (filter: string) => {\n    setFilterActive(filter);\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  return (\n    <DashboardLayout title=\"Class Management\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-3xl font-bold\">Class Management</h1>\n          <Button onClick={() => router.push('/admin/classes/new')}>\n            Add New Class\n          </Button>\n        </div>\n\n        {error && (\n          <Alert variant=\"destructive\">\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        <ClassTable\n          classes={classes}\n          pagination={pagination}\n          onPageChange={handlePageChange}\n          onSearch={handleSearch}\n          onFilter={handleFilter}\n          loading={loading}\n        />\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card key=\"total-classes\">\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Classes</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{pagination.total}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"active-classes\">\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Active Classes</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {classes.filter(c => c.isActive).length}\n              </div>\n            </CardContent>\n          </Card>\n          <Card key=\"total-students-in-classes\">\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Students</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {classes.reduce((sum, c) => sum + c._count.students, 0)}\n              </div>\n            </CardContent>\n          </Card>\n          <Card key=\"average-class-size\">\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Class Size</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {classes.length > 0\n                  ? Math.round(classes.reduce((sum, c) => sum + c._count.students, 0) / classes.length)\n                  : 0\n                }\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AA4Ce,SAAS;;IACtB,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAU,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0TAAQ,EAAC;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0TAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0TAAQ,EAAC;IAEjD,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,GAAI,cAAc;oBAAE,QAAQ;gBAAW,CAAC;gBACxC,GAAI,iBAAiB,SAAS;oBAAE,UAAU;gBAAa,CAAC;YAC1D;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,sBAA4B,OAAP;YACnD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,WAAW,KAAK,OAAO;YACvB,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,2TAAS;iCAAC;YACR;QACF;gCAAG;QAAC,WAAW,IAAI;QAAE;QAAY;KAAa;IAE9C,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IAC1C;IAEA,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,qBACE,8UAAC,mMAAe;QAAC,OAAM;QAAmB,YAAY,gLAAe;kBACnE,cAAA,8UAAC;YAAI,WAAU;;8BACb,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8UAAC,iLAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;sCAAuB;;;;;;;;;;;;gBAK3D,uBACC,8UAAC,+KAAK;oBAAC,SAAQ;8BACb,cAAA,8UAAC,0LAAgB;kCAAE;;;;;;;;;;;8BAIvB,8UAAC,+LAAU;oBACT,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,UAAU;oBACV,SAAS;;;;;;8BAIX,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;8CACpB,cAAA,8UAAC,kLAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8UAAC,oLAAW;8CACV,cAAA,8UAAC;wCAAI,WAAU;kDAAsB,WAAW,KAAK;;;;;;;;;;;;2BAL/C;;;;;sCAQV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;8CACpB,cAAA,8UAAC,kLAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8UAAC,oLAAW;8CACV,cAAA,8UAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;2BANnC;;;;;sCAUV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;8CACpB,cAAA,8UAAC,kLAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8UAAC,oLAAW;8CACV,cAAA,8UAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;;;;;;;;;;;;2BANjD;;;;;sCAUV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;8CACpB,cAAA,8UAAC,kLAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8UAAC,oLAAW;8CACV,cAAA,8UAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,GAAG,IACd,KAAK,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,KAAK,QAAQ,MAAM,IAClF;;;;;;;;;;;;2BARA;;;;;;;;;;;;;;;;;;;;;;AAiBpB;GAjIwB;;QACP,mSAAS;;;KADF", "debugId": null}}]}