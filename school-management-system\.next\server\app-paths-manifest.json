{"/(auth)/login/page": "app/(auth)/login/page.js", "/(dash)/admin/attendance/page": "app/(dash)/admin/attendance/page.js", "/(dash)/admin/classes/page": "app/(dash)/admin/classes/page.js", "/(dash)/admin/exams/page": "app/(dash)/admin/exams/page.js", "/(dash)/admin/marks/page": "app/(dash)/admin/marks/page.js", "/(dash)/admin/page": "app/(dash)/admin/page.js", "/(dash)/admin/reports/page": "app/(dash)/admin/reports/page.js", "/(dash)/admin/settings/page": "app/(dash)/admin/settings/page.js", "/(dash)/admin/students/page": "app/(dash)/admin/students/page.js", "/(dash)/admin/subjects/page": "app/(dash)/admin/subjects/page.js", "/(dash)/admin/teachers/page": "app/(dash)/admin/teachers/page.js", "/(dash)/student/marks/page": "app/(dash)/student/marks/page.js", "/(dash)/student/page": "app/(dash)/student/page.js", "/(dash)/student/reports/page": "app/(dash)/student/reports/page.js", "/api/admin/classes/route": "app/api/admin/classes/route.js", "/api/admin/dashboard/stats/route": "app/api/admin/dashboard/stats/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/subjects/route": "app/api/admin/subjects/route.js", "/api/admin/teachers/route": "app/api/admin/teachers/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/student/dashboard/stats/route": "app/api/student/dashboard/stats/route.js", "/api/student/marks/route": "app/api/student/marks/route.js", "/page": "app/page.js"}